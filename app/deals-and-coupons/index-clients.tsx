'use client';
import BreadcrumbSaveShare from '@/app/components/atoms/breadcrumb-container';
import CommonHeader from '@/app/components/headers/common-header';
import React, { useState } from 'react';
import MainOfferCard from '@/app/components/cards/main-offer-card';
import PlusIcon from '@/app/components/svg/plus-icon';
import CopySVG from '@/app/components/svg/copy';
import CommonToolbar, {
  dealsAndCouponsTypeSortItems,
} from '@/app/components/common-toolbar';
import AsideDealsCoupons from './aside-deals-coupons';
import CommonFilterMobile from '../components/misc/common-filter-mobile';
import type { DealAndCouponsResponse } from '@/services/api/data-contracts';
import { formatIndRs } from '@/utils/helpers';
import { ConfigProvider, Pagination, theme } from 'antd';
import { useTheme } from 'next-themes';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import {
  useCopyCoupon,
  useCreateMultiQueryString,
  useResponsiveGrid,
} from '@/utils/custom-hooks';
import EnhancedNoData from '../components/enhanced-no-data';

const IndexClients = ({ data }: { data: DealAndCouponsResponse }) => {
  const [isShowFilterModal, setShowFilterModal] = useState(false);
  const { resolvedTheme } = useTheme();
  const { replace } = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const createMultiQueryString = useCreateMultiQueryString(searchParams);
  const handleCopyCoupon = useCopyCoupon();

  // Use our custom responsive grid hook
  const { getGridProps } = useResponsiveGrid();

  return (
    <>
      <CommonHeader
        backRoute='/'
        headline='Deals and Coupons'
        showMenuBtn
        subHeading={
          <>
            <span>Results</span>
            <span className='ml-[6px] text-[7px] sm:text-[8px] font-nexa'>
              {`${data.pagination.pageSize}/${data.pagination.total}`}
            </span>
          </>
        }
      />
      <div className='flex h-full lg:min-h-[calc(100svh-112px)] lg:m-[8px] mb-0 gap-x-[8px]'>
        <AsideDealsCoupons />
        <div className='w-full'>
          <BreadcrumbSaveShare
            breadCrumbs={[
              { title: 'Cashback Home', link: '/' },
              { title: 'Deals and Coupons' },
            ]}
          />
          <section>
            <CommonToolbar
              customSortItems={dealsAndCouponsTypeSortItems}
              initialSort={dealsAndCouponsTypeSortItems.find(
                (item) => item.label === 'Popular'
              )}
              onClickFilterBtn={() => setShowFilterModal(!isShowFilterModal)}
              rootClassName='sticky top-[64px] lg:top-[103px]'
            />
            <div className='bg-container mx-[6px] px-[8px] lg:mx-0 lg:pt-[16px] lg:px-[40px] pb-[18px]'>
              <h4 className='hidden lg:block text-[14px] font-pat font-normal text-blackWhite'>
                Results{' '}
                <span className='text-xs font-nexa font-[800]'>
                  ({`${data.pagination.pageSize}/${data.pagination.total}`})
                </span>
              </h4>
              {data?.offers?.length ? (
                <div {...getGridProps()}>
                  {data.offers.map((item) => (
                    <MainOfferCard
                      duration={item.endDate}
                      hideCbTag={item?.hideCbTag}
                      isAutoGenerated={item?.isAutoGenerated}
                      isOfferUpto={Boolean(item?.offerCaption?.trim())}
                      key={item.uid}
                      offerTitle={item.offerTitle}
                      productImgUrl={item.productImage || ''}
                      rootClass='!w-full'
                      saved={item.saved}
                      showNewBadge={false}
                      storeImgUrl={item.storeLogoUrl}
                      storeName={item.storeName}
                      uid={item.uid}
                    >
                      {/* first child */}
                      <p
                        dangerouslySetInnerHTML={{ __html: item.offerTitle }}
                      />
                      {/* second child */}
                      <p className='text-primary dark:text-white font-medium text-[9px]'>
                        {item.salePrice > 0 && (
                          <>
                            Offer Applied Price
                            <span className='font-nexa font-black ml-[4px] text-[10px] sm:text-[11px]'>
                              {formatIndRs(item.salePrice)}
                            </span>
                          </>
                        )}
                      </p>
                      {/* third child */}
                      <>
                        <PlusIcon className='text-black shrink-0' />
                        <p className='text-[10px] text-black font-bold leading-none ml-[4px] mt-[2px] truncate'>
                          {item.offerCaption}
                        </p>
                      </>
                      {/* fourth child */}
                      {item.couponCode ? (
                        <button
                          className='bg-primary w-full p-[4px] h-full rounded-b-[6px] relative hover:bg-[#5448b0] active:bg-primary'
                          onClick={(event) =>
                            handleCopyCoupon({
                              event,
                              uid: item.uid,
                              couponCode: item.couponCode,
                            })
                          }
                          type='button'
                        >
                          <div className='border-dashed border-[0.5px] border-[#E0DCFF] rounded-b-[4px] h-full flex items-center justify-center'>
                            <span className='text-[10px] sm:text-[12px] font-semibold text-white'>
                              Copy Code
                            </span>
                            <CopySVG className='text-white absolute top-[16px] right-[12px]' />
                          </div>
                        </button>
                      ) : (
                        <div className='bg-primary p-[4px] h-full rounded-b-[6px] relative flex-center'>
                          <span className='text-[10px] font-semibold text-white'>
                            GRAB DEAL
                          </span>
                        </div>
                      )}
                    </MainOfferCard>
                  ))}
                </div>
              ) : (
                <EnhancedNoData
                  customHeight='min-h-[400px]'
                  message='No deals available at the moment. Please check back later!'
                  showHomeLink={false}
                />
              )}
              {data.pagination.pageSize > 0 && (
                <div className='flex-center w-full my-[50px]'>
                  <ConfigProvider
                    theme={{
                      algorithm:
                        resolvedTheme === 'dark'
                          ? theme.darkAlgorithm
                          : theme.defaultAlgorithm,
                    }}
                  >
                    <Pagination
                      defaultCurrent={1}
                      defaultPageSize={15}
                      onChange={(pageNumber, pageSize) =>
                        replace(
                          `${pathname}?${createMultiQueryString([
                            { name: 'page', value: pageNumber.toString() },
                            {
                              name: 'pageSize',
                              value: pageSize.toString(),
                            },
                          ])}`
                        )
                      }
                      total={data.pagination.total}
                    />
                  </ConfigProvider>
                </div>
              )}
            </div>
          </section>
        </div>
      </div>
      <CommonFilterMobile
        filterProps={[{ filter: 'user' }, { filter: 'offer' }]}
        isShowFilterModal={isShowFilterModal}
        setShowFilterModal={() => setShowFilterModal(!isShowFilterModal)}
      />
    </>
  );
};

export default IndexClients;
