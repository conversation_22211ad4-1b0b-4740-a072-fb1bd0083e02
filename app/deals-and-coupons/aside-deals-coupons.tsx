'use client';
import React from 'react';
import CommonFilterSidebar from '@/app/components/misc/common-filter-sidebar';

const AsideDealsCoupons = () => {
  return (
    <aside className='shrink-0 w-[270px] lg:max-h-[calc(100svh-112px)] sticky top-[112px] overflow-hidden  hidden lg:block scrollbarNone'>
      <CommonFilterSidebar
        filterProps={[{ filter: 'user' }, { filter: 'offer' }]}
        rootClass='!mt-0'
      />
    </aside>
  );
};

export default AsideDealsCoupons;
