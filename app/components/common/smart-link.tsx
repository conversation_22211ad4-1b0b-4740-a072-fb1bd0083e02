'use client';

import Link from 'next/link';
import {
  determineLinkType,
  getRelAttribute,
  LinkType,
} from '@/utils/link-utils';
import { forwardRef, ReactNode } from 'react';
import { ForesightLink } from './foresight-link';
import { type ForesightRect } from 'js.foresight';

interface SmartLinkProps {
  href: string;
  children: ReactNode;
  className?: string;
  linkType?: LinkType;
  target?: string;
  onClick?: (event?: React.MouseEvent<Element, MouseEvent>) => void;
  onKeyDown?: (e: React.KeyboardEvent) => void;
  ariaLabel?: string;
  title?: string;
  // ForesightJS options
  enableForesight?: boolean;
  hitSlop?: number | ForesightRect;
  unregisterOnCallback?: boolean;
  foresightName?: string;
  fallbackPrefetch?: boolean;
}

/**
 * SmartLink component that automatically adds appropriate rel attributes based on link type
 * Now with optional ForesightJS integration for predictive prefetching.
 *
 * @example
 * // Internal link (no rel attribute)
 * <SmartLink href="/about">About Us</SmartLink>
 *
 * @example
 * // External link (adds nofollow by default)
 * <SmartLink href="https://example.com">External Site</SmartLink>
 *
 * @example
 * // Sponsored link
 * <SmartLink href="https://partner.com" linkType={LinkType.EXTERNAL_SPONSORED}>Partner</SmartLink>
 *
 * @example
 * // With ForesightJS predictive prefetching
 * <SmartLink href="/products" enableForesight hitSlop={20}>Products</SmartLink>
 */
const SmartLink = forwardRef<HTMLAnchorElement, SmartLinkProps>(
  ({
    href,
    children,
    className,
    linkType,
    target,
    onClick,
    onKeyDown,
    ariaLabel,
    title,
    enableForesight = false,
    hitSlop,
    unregisterOnCallback,
    foresightName,
    fallbackPrefetch,
  }: SmartLinkProps) => {
    // If ForesightJS is enabled, use ForesightLink
    if (enableForesight) {
      return (
        <ForesightLink
          ariaLabel={ariaLabel}
          className={className}
          fallbackPrefetch={fallbackPrefetch}
          hitSlop={hitSlop}
          href={href}
          linkType={linkType}
          name={foresightName}
          onClick={onClick}
          onKeyDown={onKeyDown}
          target={target}
          title={title}
          unregisterOnCallback={unregisterOnCallback}
        >
          {children}
        </ForesightLink>
      );
    }

    // Original SmartLink behavior
    const type = determineLinkType(href, linkType);
    const rel = getRelAttribute(type);
    const isExternal = type !== LinkType.INTERNAL;

    // For external links, use anchor tag
    if (isExternal) {
      return (
        <a
          aria-label={ariaLabel}
          className={className}
          href={href}
          onClick={onClick}
          rel={rel}
          target={target || '_blank'}
          title={title}
        >
          {children}
        </a>
      );
    }

    // For internal links, use Next.js Link
    return (
      <Link
        aria-label={ariaLabel}
        className={className}
        href={href}
        onClick={onClick}
        onKeyDown={onKeyDown}
        target={target}
        title={title}
      >
        {children}
      </Link>
    );
    // eslint-disable-next-line react/display-name
  }
);

export default SmartLink;
