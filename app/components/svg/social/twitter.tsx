import React from 'react';

const TwitterSVG = ({ ...props }) => {
  return (
    <svg
      {...props}
      fill='none'
      viewBox='0 0 25 26'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        clipRule='evenodd'
        d='M12.5 0C19.4036 0 25 5.8203 25 13C25 20.1797 19.4036 26 12.5 26C5.59644 26 0 20.1797 0 13C0 5.8203 5.59644 0 12.5 0ZM14.7535 8.125C14.1163 8.12468 13.5052 8.38379 13.0547 8.84528C12.6042 9.30676 12.3512 9.93277 12.3515 10.5854C12.3515 10.776 12.3742 10.966 12.4111 11.1492C10.4787 11.0482 8.67874 10.1125 7.45817 8.57457C7.25006 8.93984 7.13139 9.35887 7.13139 9.81577C7.13056 10.6394 7.53269 11.4086 8.20237 11.8645C7.8088 11.8492 7.4373 11.735 7.11708 11.5523V11.5829C7.11547 12.755 7.92151 13.7655 9.04317 13.9975C8.84221 14.0506 8.62575 14.0812 8.41048 14.0812C8.25425 14.0812 8.10517 14.0659 7.95669 14.0433C8.27304 15.0449 9.17542 15.7303 10.203 15.7493C9.35245 16.4329 8.30195 16.8032 7.22084 16.8006C7.01988 16.8006 6.83383 16.7926 6.64062 16.77C7.70445 17.4712 8.96863 17.875 10.3294 17.875C14.7463 17.875 17.1632 14.127 17.1632 10.8743C17.1632 10.768 17.1632 10.6618 17.1554 10.5549C17.6238 10.2032 18.0309 9.77307 18.3594 9.28252C17.9196 9.47948 17.4534 9.60795 16.9765 9.66367C17.4779 9.35832 17.8534 8.8762 18.0326 8.30764C17.5618 8.59437 17.0462 8.79545 16.5084 8.90197C16.0695 8.42186 15.4452 8.125 14.7535 8.125Z'
        fill='currentColor'
        fillRule='evenodd'
      />
    </svg>
  );
};

export default TwitterSVG;
