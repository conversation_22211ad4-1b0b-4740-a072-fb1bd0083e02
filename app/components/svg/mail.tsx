import React from 'react';

const MailSVG = ({ ...props }) => {
  return (
    // <svg
    //   fill='none'
    //   viewBox='0 0 12 13'
    //   xmlns='http://www.w3.org/2000/svg'
    //   {...props}
    // >
    //   <path
    //     d='M9.88889 12V5.95M9.88889 3.75V1M6 12V9.25M6 7.05V1M2.11111 12V5.95M2.11111 3.75V1M1 5.95H3.22222M8.77778 5.95H11M4.88889 7.05H7.11111'
    //     stroke='currentColor'
    //     strokeLinecap='round'
    //     strokeLinejoin='round'
    //     strokeMiterlimit='10'
    //   />
    // </svg>

    <svg
      xmlns='http://www.w3.org/2000/svg'
      {...props}
      fill='none'
      viewBox='0 0 15 15'
    >
      <path
        d='M10.625 12.8125H4.375C2.5 12.8125 1.25 11.875 1.25 9.6875V5.3125C1.25 3.125 2.5 2.1875 4.375 2.1875H10.625C12.5 2.1875 13.75 3.125 13.75 5.3125V9.6875C13.75 11.875 12.5 12.8125 10.625 12.8125Z'
        stroke='currentColor'
        stroke-linecap='round'
        stroke-linejoin='round'
        stroke-miterlimit='10'
      />
      <path
        d='M10.625 5.625L8.66875 7.1875C8.025 7.7 6.96875 7.7 6.325 7.1875L4.375 5.625'
        stroke='currentColor'
        stroke-linecap='round'
        stroke-linejoin='round'
        stroke-miterlimit='10'
      />
    </svg>
  );
};

export default MailSVG;
