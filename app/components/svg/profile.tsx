import React from 'react';

const ProfileSVG = ({ ...props }) => {
  return (
    <svg
      fill='none'
      viewBox='0 0 14 14'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        d='M7.00016 7.00008C7.77371 7.00008 8.51558 6.69279 9.06256 6.14581C9.60954 5.59883 9.91683 4.85696 9.91683 4.08341C9.91683 3.30987 9.60954 2.568 9.06256 2.02102C8.51558 1.47404 7.77371 1.16675 7.00016 1.16675C6.22661 1.16675 5.48475 1.47404 4.93777 2.02102C4.39079 2.568 4.0835 3.30987 4.0835 4.08341C4.0835 4.85696 4.39079 5.59883 4.93777 6.14581C5.48475 6.69279 6.22661 7.00008 7.00016 7.00008V7.00008Z'
        stroke='currentColor'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M11.2058 9.18167L9.14077 11.2467C9.0591 11.3283 8.98327 11.48 8.96577 11.5908L8.85494 12.3783C8.8141 12.6642 9.01244 12.8625 9.29827 12.8217L10.0858 12.7108C10.1966 12.6933 10.3541 12.6175 10.4299 12.5358L12.4949 10.4708C12.8508 10.115 13.0199 9.70084 12.4949 9.17584C11.9758 8.65667 11.5616 8.82584 11.2058 9.18167V9.18167Z'
        stroke='currentColor'
        strokeLinecap='round'
        strokeLinejoin='round'
        strokeMiterlimit='10'
      />
      <path
        d='M10.9082 9.47925C11.0832 10.1092 11.5732 10.5992 12.2032 10.7742'
        stroke='currentColor'
        strokeLinecap='round'
        strokeLinejoin='round'
        strokeMiterlimit='10'
      />
      <path
        d='M1.98926 12.8333C1.98926 10.5758 4.23509 8.75 7.00009 8.75C7.60676 8.75 8.19009 8.8375 8.73259 9.00083'
        stroke='currentColor'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  );
};

export default ProfileSVG;
