import React from 'react';

const ReferFriendSVG = ({ ...props }) => {
  return (
    <svg
      fill='none'
      viewBox='0 0 27 25'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        d='M24.7163 2.61312C24.2978 2.14171 23.7833 1.76477 23.2071 1.50748C22.6309 1.25019 22.0063 1.11848 21.375 1.12113C20.7838 1.12039 20.1984 1.23596 19.6521 1.4612C19.1058 1.68644 18.6094 2.01693 18.1914 2.43375C17.7734 2.85057 17.4419 3.34553 17.216 3.89027C16.9902 4.43501 16.8743 5.01884 16.875 5.60831C16.875 6.44966 17.1113 7.24613 17.5275 7.91921C17.7525 8.30062 18.045 8.64838 18.3825 8.94004C19.17 9.65799 20.2163 10.0955 21.375 10.0955C21.87 10.0955 22.3425 10.017 22.7813 9.85991C23.8163 9.53459 24.6825 8.82786 25.2225 7.91921C25.4588 7.5378 25.6388 7.1003 25.74 6.65158C25.83 6.31504 25.875 5.96729 25.875 5.60831C25.875 4.46408 25.4363 3.40959 24.7163 2.61312ZM23.0513 6.42722H22.2188V7.30222C22.2188 7.76216 21.8363 8.14357 21.375 8.14357C20.9138 8.14357 20.5313 7.76216 20.5313 7.30222V6.42722H19.6988C19.2375 6.42722 18.855 6.04581 18.855 5.58588C18.855 5.12594 19.2375 4.74453 19.6988 4.74453H20.5313V3.94806C20.5313 3.48812 20.9138 3.10671 21.375 3.10671C21.8363 3.10671 22.2188 3.48812 22.2188 3.94806V4.74453H23.0513C23.275 4.74453 23.4896 4.83317 23.6479 4.99095C23.8061 5.14874 23.895 5.36274 23.895 5.58588C23.895 5.80902 23.8061 6.02302 23.6479 6.1808C23.4896 6.33858 23.275 6.42722 23.0513 6.42722Z'
        fill='currentColor'
      />
      <path
        d='M24.75 13.4621C24.75 11.9926 24.4688 10.5791 23.94 9.28906C23.5913 9.53585 23.1975 9.72656 22.7812 9.86118C22.6575 9.90605 22.5338 9.9397 22.3988 9.97336C23.0774 11.6916 23.2399 13.5696 22.8663 15.3783C22.4928 17.1871 21.5993 18.8483 20.295 20.1593C19.9688 19.7442 19.5525 19.3628 19.0575 19.0375C16.0088 16.9958 11.0138 16.9958 7.9425 19.0375C7.4475 19.3628 7.0425 19.7442 6.705 20.1593C4.93291 18.3781 3.93822 15.9711 3.9375 13.4621C3.9375 8.20092 8.22375 3.92688 13.5 3.92688C14.7262 3.92688 15.9075 4.16246 16.9875 4.58874C17.0213 4.45412 17.055 4.33073 17.1 4.19611C17.235 3.78105 17.4263 3.39964 17.685 3.05188C16.3558 2.5143 14.9344 2.23996 13.5 2.24419C7.30125 2.24419 2.25 7.28105 2.25 13.4621C2.25 16.7153 3.65625 19.6432 5.88375 21.6961C5.88375 21.7073 5.88375 21.7073 5.8725 21.7185C5.985 21.8307 6.12 21.9205 6.2325 22.0214C6.3 22.0775 6.35625 22.1336 6.42375 22.1785C6.62625 22.3468 6.85125 22.5038 7.065 22.6609L7.29 22.8179C7.50375 22.9637 7.72875 23.0984 7.965 23.2218C8.04375 23.2666 8.13375 23.3227 8.2125 23.3676C8.4375 23.491 8.67375 23.6032 8.92125 23.7041C9.01125 23.749 9.10125 23.7939 9.19125 23.8275C9.43875 23.9285 9.68625 24.0182 9.93375 24.0968C10.0237 24.1304 10.1137 24.1641 10.2037 24.1865C10.4738 24.265 10.7437 24.3323 11.0138 24.3996C11.0925 24.4221 11.1713 24.4445 11.2613 24.4557C11.5763 24.523 11.8912 24.5679 12.2175 24.6016C12.2625 24.6016 12.3075 24.6128 12.3525 24.624C12.735 24.6577 13.1175 24.6801 13.5 24.6801C13.8825 24.6801 14.265 24.6577 14.6363 24.624C14.6813 24.624 14.7262 24.6128 14.7712 24.6016C15.0975 24.5679 15.4125 24.523 15.7275 24.4557C15.8062 24.4445 15.885 24.4109 15.975 24.3996C16.245 24.3323 16.5262 24.2762 16.785 24.1865C16.875 24.1528 16.965 24.1192 17.055 24.0968C17.3025 24.007 17.5612 23.9285 17.7975 23.8275C17.8875 23.7939 17.9775 23.749 18.0675 23.7041C18.3037 23.6032 18.54 23.491 18.7763 23.3676C18.8663 23.3227 18.945 23.2666 19.0238 23.2218C19.2488 23.0871 19.4738 22.9637 19.6987 22.8179C19.7775 22.773 19.845 22.7169 19.9237 22.6609C20.1487 22.5038 20.3625 22.3468 20.565 22.1785C20.6325 22.1224 20.6888 22.0663 20.7563 22.0214C20.88 21.9205 21.0037 21.8195 21.1163 21.7185C21.1163 21.7073 21.1163 21.7073 21.105 21.6961C23.3438 19.6432 24.75 16.7153 24.75 13.4621Z'
        fill='currentColor'
      />
      <path
        d='M13.5 7.77539C11.1713 7.77539 9.28125 9.66001 9.28125 11.9821C9.28125 14.2594 11.07 16.1103 13.4437 16.1776H13.6463C14.7383 16.1418 15.7736 15.6842 16.5336 14.9013C17.2935 14.1184 17.7185 13.0717 17.7188 11.9821C17.7188 9.66001 15.8288 7.77539 13.5 7.77539Z'
        fill='currentColor'
      />
    </svg>
  );
};

export const ReferralHistorySVG = ({ ...props }) => {
  return (
    <svg
      fill='none'
      viewBox='0 0 14 14'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <g clipPath='url(#clip0_2084_8659)'>
        <path
          d='M13.1644 8.13853C12.6873 7.65724 12.0569 7.36414 11.3877 7.30222C11.9654 6.78251 12.3299 6.03222 12.3299 5.19408C12.3299 3.62609 11.0593 2.35547 9.49096 2.35547C7.99312 2.35547 6.76672 3.51568 6.66028 4.98607C7.99281 5.56252 8.92794 6.89048 8.92794 8.43224C8.92794 8.99527 8.79984 9.54548 8.56163 10.0448C8.90476 10.2241 9.22104 10.4559 9.49676 10.7341C10.2037 11.4459 10.6054 12.4301 10.5978 13.4336L10.5972 13.454L10.5966 13.4714L10.5716 13.9463H13.972L14 10.1857C14.0058 9.4186 13.7054 8.68295 13.1644 8.13853Z'
          fill='currentColor'
        />
        <path
          d='M7.07105 10.5411C7.64841 10.0217 8.0141 9.27111 8.0141 8.43297C8.0141 6.86468 6.74287 5.59375 5.17488 5.59375C3.60658 5.59375 2.33565 6.86468 2.33565 8.43297C2.33565 9.26958 2.69982 10.0196 3.27627 10.5393C1.80008 10.6537 0.634072 11.8761 0.623397 13.3813L0.595337 13.9464H4.91197H9.6556L9.68366 13.4236C9.68915 12.6569 9.38842 11.9212 8.84766 11.3768C8.37003 10.8952 7.73991 10.603 7.07105 10.5411Z'
          fill='currentColor'
        />
        <path
          d='M1.83121 3.46914V4.52382C1.83121 4.96057 2.18622 5.31529 2.62328 5.31529C3.06096 5.31529 3.41536 4.96057 3.41536 4.52382V3.46914H4.46883C4.90589 3.46914 5.2606 3.11412 5.2606 2.67706C5.2606 2.2403 4.90589 1.88528 4.46883 1.88528H3.41536V0.846156C3.41536 0.409095 3.06096 0.0546875 2.62328 0.0546875C2.18622 0.0546875 1.83121 0.409095 1.83121 0.846156V1.88498H0.791774C0.354102 1.88498 0 2.24 0 2.67675C0 3.11381 0.354102 3.46883 0.791774 3.46883L1.83121 3.46914Z'
          fill='currentColor'
        />
      </g>
      <defs>
        <clipPath id='clip0_2084_8659'>
          <rect fill='currentColor' height='14' width='14' />
        </clipPath>
      </defs>
    </svg>
  );
};
export default ReferFriendSVG;
