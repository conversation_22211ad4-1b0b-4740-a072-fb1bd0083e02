import React from 'react';

export const ArrowUp = ({ ...props }) => {
  return (
    <svg
      fill='none'
      height='13'
      viewBox='0 0 13 13'
      width='13'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        d='M10.79 8.1521L7.25829 4.62043C6.84121 4.20335 6.15871 4.20335 5.74163 4.62043L2.20996 8.1521'
        stroke='currentColor'
        strokeLinecap='round'
        strokeLinejoin='round'
        strokeMiterlimit='10'
        strokeWidth='1.5'
      />
    </svg>
  );
};

export const ArrowDown = ({ ...props }) => {
  return (
    <svg
      fill='none'
      height='13'
      viewBox='0 0 13 13'
      width='13'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        d='M2.21004 4.8479L5.74171 8.37957C6.15879 8.79665 6.84129 8.79665 7.25837 8.37957L10.79 4.8479'
        stroke='currentColor'
        strokeLinecap='round'
        strokeLinejoin='round'
        strokeMiterlimit='10'
        strokeWidth='1.5'
      />
    </svg>
  );
};
