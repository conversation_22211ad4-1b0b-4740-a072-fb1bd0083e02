import React from 'react';

const WarningIcon = ({ ...props }) => {
  return (
    <svg
      fill='none'
      viewBox='0 0 12 12'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <g clip-path='url(#clip0_0_10003)'>
        <path
          d='M6.00008 3.875V6.5M10.5401 4.29V7.71C10.5401 8.27 10.2401 8.79 9.75508 9.075L6.78508 10.79C6.30008 11.07 5.70008 11.07 5.21008 10.79L2.24008 9.075C2.00097 8.93636 1.80255 8.73724 1.66475 8.49764C1.52696 8.25804 1.45465 7.9864 1.45508 7.71V4.29C1.45508 3.73 1.75508 3.21 2.24008 2.925L5.21008 1.21C5.69508 0.93 6.29508 0.93 6.78508 1.21L9.75508 2.925C10.2401 3.21 10.5401 3.725 10.5401 4.29Z'
          stroke='currentColor'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
        <path
          d='M6 8.09961V8.14961'
          stroke='currentColor'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
      </g>
      <defs>
        <clipPath id='clip0_0_10003'>
          <rect fill='currentColor' height='12' width='12' />
        </clipPath>
      </defs>
    </svg>
  );
};

export default WarningIcon;
