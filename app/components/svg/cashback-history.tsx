import React from 'react';

const CashbackHistory = ({ ...props }) => {
  return (
    <svg
      fill='none'
      viewBox='0 0 18 15'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        d='M10.8701 7.58379C12.9643 7.58379 14.662 5.8861 14.662 3.79189C14.662 1.69769 12.9643 0 10.8701 0C8.77594 0 7.07825 1.69769 7.07825 3.79189C7.07825 5.8861 8.77594 7.58379 10.8701 7.58379Z'
        fill='currentColor'
      />
      <path
        clipRule='evenodd'
        d='M3.79189 8.68024C3.79189 7.98177 3.22596 7.41602 2.52825 7.41602C2.12157 7.41602 1.67032 7.41602 1.26364 7.41602C0.565933 7.41602 0 7.98177 0 8.68024C0 10.0825 0 12.3333 0 13.7356C0 14.434 0.565933 14.9998 1.26364 14.9998C1.67032 14.9998 2.12157 14.9998 2.52825 14.9998C3.22596 14.9998 3.79189 14.434 3.79189 13.7356C3.79189 12.3333 3.79189 10.0825 3.79189 8.68024Z'
        fill='currentColor'
        fillRule='evenodd'
      />
      <path
        clipRule='evenodd'
        d='M4.47351 9.31209C4.47351 8.97714 4.60686 8.65546 4.84385 8.41846C5.08084 8.18147 5.40252 8.04812 5.73747 8.04812H8.89739C8.90118 8.04812 10.1613 8.05002 10.1613 9.31209C10.1613 10.5761 8.89739 10.5761 8.89739 10.5761H11.534C11.869 10.5761 12.1906 10.4427 12.4276 10.2057C13.1298 9.50358 14.5852 8.04812 14.5852 8.04812C16.0849 6.54843 18.5117 8.22002 16.0028 10.7296C16.0028 10.7296 13.933 13.0016 13.0653 13.9553C12.8258 14.2182 12.4864 14.3679 12.1306 14.3679C10.8268 14.3679 7.55379 14.3679 5.73747 14.3679C5.40252 14.3679 5.08084 14.2346 4.84385 13.9976C4.60686 13.7606 4.47351 13.4389 4.47351 13.104C4.47351 12.0024 4.47351 10.4136 4.47351 9.31209Z'
        fill='currentColor'
        fillRule='evenodd'
      />
    </svg>
  );
};

export default CashbackHistory;
