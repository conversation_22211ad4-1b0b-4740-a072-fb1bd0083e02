import React from 'react';

const ICBCardSVG = ({ ...props }) => {
  return (
    <svg
      fill='none'
      viewBox='0 0 20 20'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        d='M18.3332 7.70801H1.6665C1.32484 7.70801 1.0415 7.42467 1.0415 7.08301C1.0415 6.74134 1.32484 6.45801 1.6665 6.45801H18.3332C18.6748 6.45801 18.9582 6.74134 18.9582 7.08301C18.9582 7.42467 18.6748 7.70801 18.3332 7.70801Z'
        fill='#AEB5C9'
      />
      <path
        d='M6.66667 14.375H5C4.65833 14.375 4.375 14.0917 4.375 13.75C4.375 13.4083 4.65833 13.125 5 13.125H6.66667C7.00833 13.125 7.29167 13.4083 7.29167 13.75C7.29167 14.0917 7.00833 14.375 6.66667 14.375Z'
        fill='#AEB5C9'
      />
      <path
        d='M14.6332 17.7087H5.3665C2.04984 17.7087 1.0415 16.7087 1.0415 13.4253V6.57533C1.0415 3.29199 2.04984 2.29199 5.3665 2.29199H14.6248C17.9415 2.29199 18.9498 3.29199 18.9498 6.57533V13.417C18.9582 16.7087 17.9498 17.7087 14.6332 17.7087ZM5.3665 3.54199C2.74984 3.54199 2.2915 3.99199 2.2915 6.57533V13.417C2.2915 16.0003 2.74984 16.4503 5.3665 16.4503H14.6248C17.2415 16.4503 17.6998 16.0003 17.6998 13.417V6.57533C17.6998 3.99199 17.2415 3.54199 14.6248 3.54199H5.3665Z'
        fill='currentColor'
      />
    </svg>
  );
};

export default ICBCardSVG;
