import React from 'react';

const RoundCheck = ({ ...props }) => {
  return (
    <svg
      fill='none'
      viewBox='0 0 11 11'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <g clipPath='url(#clip0_0_31928)'>
        <path
          d='M5.50033 10.0827C8.02116 10.0827 10.0837 8.02018 10.0837 5.49935C10.0837 2.97852 8.02116 0.916016 5.50033 0.916016C2.97949 0.916016 0.916992 2.97852 0.916992 5.49935C0.916992 8.02018 2.97949 10.0827 5.50033 10.0827Z'
          stroke='currentcolor'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
        <path
          d='M3.55176 5.50021L4.84884 6.79729L7.44759 4.20312'
          stroke='currentcolor'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
      </g>
      <defs>
        <clipPath id='clip0_0_31928'>
          <rect fill='currentcolor' height='11' width='11' />
        </clipPath>
      </defs>
    </svg>
  );
};

export default RoundCheck;
