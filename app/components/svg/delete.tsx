import React from 'react';

const DeleteSVG = ({ ...props }) => {
  return (
    <svg
      fill='none'
      height='13'
      viewBox='0 0 12 13'
      width='12'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        d='M5 6V9.49997'
        stroke='currentColor'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M7.49951 6V9.49997'
        stroke='currentColor'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M1 3.5H10.9999'
        stroke='currentColor'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M2.5 3.5H6.24996H9.99993V10.1785C9.99993 11.1845 9.1605 11.9999 8.12495 11.9999H4.37498C3.33946 11.9999 2.5 11.1845 2.5 10.1785V3.5Z'
        stroke='currentColor'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M3.89453 2.15788C3.89453 1.5184 4.58573 1 5.43838 1H6.98222C7.83489 1 8.52607 1.5184 8.52607 2.15788V3.31577H3.89453V2.15788Z'
        stroke='currentColor'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  );
};

export default DeleteSVG;
