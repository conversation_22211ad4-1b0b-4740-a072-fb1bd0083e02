import React from 'react';

const ClickHistorySVG = ({ ...props }) => {
  return (
    <svg
      fill='none'
      viewBox='0 0 11 17'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        d='M2.82788 16.6195C2.82788 16.8284 2.99875 16.9993 3.20759 16.9993H8.81973C9.02857 16.9993 9.19945 16.8284 9.19945 16.6195V15.75H2.82788V16.6195Z'
        fill='currentColor'
      />
      <path
        d='M2.70075 6.13047L2.70267 3.35287C2.70267 3.03201 2.82416 2.73204 3.04631 2.508C3.29312 2.26119 3.64626 2.1207 4.01837 2.1207C4.30885 2.1207 4.58794 2.20993 4.80436 2.36941C5.06638 2.56306 5.22964 2.85544 5.26381 3.18959C5.26381 3.19718 5.4138 4.64579 5.5486 5.94251C6.49219 5.38813 7.1282 4.3629 7.1282 3.18959C7.1282 1.42772 5.70048 0 3.93862 0C2.17675 0 0.749027 1.42772 0.749027 3.18959C0.747128 4.51099 1.55212 5.64633 2.70075 6.13047Z'
        fill='currentColor'
      />
      <path
        d='M10.1944 9.75593C10.1944 9.38571 9.95709 9.05724 9.60396 8.94333C8.55026 8.59969 6.42386 7.9143 6.09919 7.84975C5.99857 7.83077 5.89984 7.82127 5.80491 7.82127C5.54861 7.82127 5.32458 7.89152 5.1898 8.01113H5.18788C5.18788 8.01113 5.18788 8.01113 5.18788 8.00923C5.18598 7.99402 4.69617 3.25712 4.69617 3.24953C4.65628 2.86982 4.34114 2.68945 4.01646 2.68945C3.64816 2.68945 3.26845 2.92108 3.27033 3.35205L3.26653 11.028C3.26653 11.1191 3.20008 11.195 3.10895 11.2083C3.10705 11.2083 3.10705 11.2083 3.10515 11.2083C3.09758 11.2102 3.08809 11.2102 3.08047 11.2102C3.07857 11.2102 3.07668 11.2102 3.07478 11.2102C2.81657 11.2026 2.23371 11.195 1.66604 10.373C1.5901 10.2647 1.52175 10.166 1.4591 10.0749C1.26165 9.79198 0.997747 9.6705 0.749035 9.6705C0.312365 9.6705 -0.0806381 10.0483 0.0142902 10.6122C0.150987 11.4342 1.05091 13.3499 2.82417 15.1459H9.19575L9.83934 14.0581C10.0691 13.6727 10.1906 13.2322 10.1906 12.7822L10.1944 9.75593Z'
        fill='currentColor'
      />
    </svg>
  );
};

export default ClickHistorySVG;
