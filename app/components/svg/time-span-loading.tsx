import React from 'react';

const TimeSpanLoading = ({ ...props }) => {
  return (
    <svg
      fill='currentColor'
      viewBox='0 0 21 28'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        d='M7.28013 8.05038C9.22975 9.33496 10.5003 11.5138 10.5003 13.9902C10.5003 11.5138 11.7709 9.33496 13.6943 8.06788C14.6971 7.39409 15.4164 6.42453 15.7419 5.28696L5.25 5.25195C5.58427 6.42453 6.30532 7.39584 7.26263 8.04163L7.28013 8.05038Z'
        fill='currentColor'
      />
      <path
        d='M16.066 10.6057C18.0034 9.19158 19.2495 6.92868 19.2512 4.37527V3.50022H21.0013V0H0V3.50022H1.75011V4.37527C1.75186 6.92868 2.99794 9.19158 4.91431 10.5899C6.09038 11.3425 6.87618 12.5675 7.00044 13.9834C6.83768 15.4675 6.03613 16.7205 4.88455 17.4888C2.99269 18.9134 1.78336 21.1256 1.75186 23.6195V24.4998H0.00174998V28H21.0031V24.4998H19.253V23.6247C19.2215 21.1238 18.0121 18.9134 16.157 17.5133C14.9669 16.7188 14.1654 15.4675 14.0044 14.0201C14.1286 12.5658 14.9144 11.339 16.0503 10.6144L16.066 10.6057ZM12.2508 14.0009C12.3768 15.9995 13.3971 17.7391 14.9092 18.8364C16.444 19.9267 17.4381 21.6541 17.5011 23.6177V24.5015H3.50022V23.6265C3.56322 21.6541 4.55728 19.9267 6.05363 18.8609C7.60423 17.7374 8.62279 15.9995 8.7488 14.0201C8.63854 12.053 7.62698 10.3606 6.12713 9.32458C4.52753 8.21676 3.50372 6.4159 3.49847 4.37702V3.50197H17.4993V4.37702C17.4941 6.41765 16.472 8.21851 14.9109 9.30008C13.3708 10.3624 12.3593 12.0548 12.249 13.9869L12.2508 14.0009Z'
        fill='currentColor'
      />
    </svg>
  );
};

export default TimeSpanLoading;
