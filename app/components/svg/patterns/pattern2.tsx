import React from 'react';

const Pattern2 = ({ ...props }) => {
  return (
    <svg
      fill='none'
      height='36'
      viewBox='0 0 45 36'
      width='45'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <g clipPath='url(#clip0_0_53349)'>
        <circle cx='1.5' cy='1.5' fill='currentColor' r='1.5' />
        <circle cx='10.5' cy='1.5' fill='currentColor' r='1.5' />
        <circle cx='19.5' cy='1.5' fill='currentColor' r='1.5' />
        <circle cx='28.5' cy='1.5' fill='currentColor' r='1.5' />
        <circle cx='37.5' cy='1.5' fill='currentColor' r='1.5' />
        <circle cx='1.5' cy='10.5' fill='currentColor' r='1.5' />
        <circle cx='10.5' cy='10.5' fill='currentColor' r='1.5' />
        <circle cx='19.5' cy='10.5' fill='currentColor' r='1.5' />
        <circle cx='28.5' cy='10.5' fill='currentColor' r='1.5' />
        <circle cx='37.5' cy='10.5' fill='currentColor' r='1.5' />
        <circle cx='1.5' cy='19.5' fill='currentColor' r='1.5' />
        <circle cx='10.5' cy='19.5' fill='currentColor' r='1.5' />
        <circle cx='19.5' cy='19.5' fill='currentColor' r='1.5' />
        <circle cx='28.5' cy='19.5' fill='currentColor' r='1.5' />
        <circle cx='37.5' cy='19.5' fill='currentColor' r='1.5' />
        <circle cx='1.5' cy='28.5' fill='currentColor' r='1.5' />
        <circle cx='10.5' cy='28.5' fill='currentColor' r='1.5' />
        <circle cx='19.5' cy='28.5' fill='currentColor' r='1.5' />
        <circle cx='28.5' cy='28.5' fill='currentColor' r='1.5' />
        <circle cx='37.5' cy='28.5' fill='currentColor' r='1.5' />
      </g>
      <defs>
        <clipPath id='clip0_0_53349'>
          <rect fill='currentColor' height='36' width='45' />
        </clipPath>
      </defs>
    </svg>
  );
};

export default Pattern2;
