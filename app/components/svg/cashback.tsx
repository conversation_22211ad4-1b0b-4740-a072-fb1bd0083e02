import React from 'react';

const CashbackSVG = ({ ...props }) => {
  return (
    <svg
      fill='none'
      height='14'
      viewBox='0 0 14 14'
      width='14'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        d='M12.5585 6.34117L11.6719 5.4545C11.5202 5.30284 11.3977 5.00534 11.3977 4.79534V3.53534C11.3977 3.02201 10.9777 2.60201 10.4644 2.60201H9.21019C9.0002 2.60201 8.7027 2.47951 8.55103 2.32784L7.66436 1.44117C7.3027 1.07951 6.7077 1.07951 6.34603 1.44117L5.44769 2.32784C5.30186 2.47951 5.00436 2.60201 4.78853 2.60201H3.53436C3.02103 2.60201 2.60103 3.02201 2.60103 3.53534V4.7895C2.60103 4.9995 2.47853 5.297 2.32686 5.44867L1.4402 6.33534C1.07853 6.69701 1.07853 7.29201 1.4402 7.65367L2.32686 8.54034C2.47853 8.69201 2.60103 8.98951 2.60103 9.1995V10.4537C2.60103 10.967 3.02103 11.387 3.53436 11.387H4.78853C4.99853 11.387 5.29603 11.5095 5.44769 11.6612L6.33436 12.5478C6.69603 12.9095 7.29103 12.9095 7.6527 12.5478L8.53936 11.6612C8.69103 11.5095 8.98853 11.387 9.19853 11.387H10.4527C10.966 11.387 11.386 10.967 11.386 10.4537V9.1995C11.386 8.98951 11.5085 8.69201 11.6602 8.54034L12.5469 7.65367C12.926 7.29784 12.926 6.70284 12.5585 6.34117ZM4.66603 5.25034C4.66603 4.9295 4.92853 4.66701 5.24936 4.66701C5.57019 4.66701 5.8327 4.9295 5.8327 5.25034C5.8327 5.57117 5.57603 5.83367 5.24936 5.83367C4.92853 5.83367 4.66603 5.57117 4.66603 5.25034ZM5.55853 9.05951C5.47103 9.14701 5.36019 9.18784 5.24936 9.18784C5.13853 9.18784 5.02769 9.14701 4.9402 9.05951C4.85883 8.97717 4.8132 8.86609 4.8132 8.75034C4.8132 8.63459 4.85883 8.5235 4.9402 8.44117L8.4402 4.94117C8.60936 4.772 8.88936 4.772 9.05853 4.94117C9.2277 5.11034 9.2277 5.39034 9.05853 5.5595L5.55853 9.05951ZM8.74936 9.33367C8.4227 9.33367 8.1602 9.07117 8.1602 8.75034C8.1602 8.4295 8.4227 8.16701 8.74353 8.16701C9.06436 8.16701 9.32686 8.4295 9.32686 8.75034C9.32686 9.07117 9.0702 9.33367 8.74936 9.33367Z'
        fill='currentColor'
      />
    </svg>
  );
};

export default CashbackSVG;
