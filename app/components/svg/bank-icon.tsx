import React from 'react';

const BankSVG = ({ ...props }) => {
  return (
    <svg
      fill='none'
      viewBox='0 0 14 13'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        d='M2.63636 9.73983V5.78453M4.81818 9.73983V5.78453M7 9.73983V5.78453M9.18182 9.73983V5.78453M11.3636 9.73983V5.78453M1 12H13M7.20182 0.783903L12.1109 2.81806C12.3018 2.89716 12.4545 3.13448 12.4545 3.34355V5.21949C12.4545 5.53026 12.2091 5.78453 11.9091 5.78453H2.09091C1.79091 5.78453 1.54545 5.53026 1.54545 5.21949V3.34355C1.54545 3.13448 1.69818 2.89716 1.88909 2.81806L6.79818 0.783903C6.90727 0.738699 7.09273 0.738699 7.20182 0.783903ZM12.4545 12H1.54545V10.3049C1.54545 9.9941 1.79091 9.73983 2.09091 9.73983H11.9091C12.2091 9.73983 12.4545 9.9941 12.4545 10.3049V12Z'
        stroke='currentcolor'
        strokeLinecap='round'
        strokeLinejoin='round'
        strokeMiterlimit='10'
      />
      <path
        d='M7 4.5C7.09849 4.5 7.19602 4.4806 7.28701 4.44291C7.37801 4.40522 7.46069 4.34997 7.53033 4.28033C7.59997 4.21069 7.65522 4.12801 7.69291 4.03701C7.7306 3.94602 7.75 3.84849 7.75 3.75C7.75 3.65151 7.7306 3.55398 7.69291 3.46299C7.65522 3.37199 7.59997 3.28931 7.53033 3.21967C7.46069 3.15003 7.37801 3.09478 7.28701 3.05709C7.19602 3.0194 7.09849 3 7 3C6.80109 3 6.61032 3.07902 6.46967 3.21967C6.32902 3.36032 6.25 3.55109 6.25 3.75C6.25 3.94891 6.32902 4.13968 6.46967 4.28033C6.61032 4.42098 6.80109 4.5 7 4.5V4.5Z'
        stroke='currentColor'
        strokeLinecap='round'
        strokeLinejoin='round'
        strokeMiterlimit='10'
      />
    </svg>
  );
};

export default BankSVG;
