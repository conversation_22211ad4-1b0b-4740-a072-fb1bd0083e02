import React from 'react';

const SearchSVG = ({ ...props }) => {
  return (
    <svg
      fill='none'
      height='19'
      viewBox='0 0 21 19'
      width='21'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <rect
        fill='currentColor'
        height='2'
        rx='1'
        transform='rotate(38.8323 13.7109 11.5635)'
        width='9.30042'
        x='13.7109'
        y='11.5635'
      />
      <path
        clipRule='evenodd'
        d='M8.30742 2.19466C4.89917 2.19466 2.12637 4.96672 2.12637 8.37497C2.12637 11.7832 4.89917 14.556 8.30742 14.556C11.7149 14.556 14.4877 11.7832 14.4877 8.37497C14.4877 4.96672 11.7149 2.19466 8.30742 2.19466ZM8.30638 15.6813C4.27771 15.6813 1 12.4036 1 8.37496C1 4.34629 4.27771 1.06934 8.30638 1.06934C12.3351 1.06934 15.612 4.34629 15.612 8.37496C15.612 12.4036 12.3351 15.6813 8.30638 15.6813Z'
        fill='currentColor'
        fillRule='evenodd'
      />
      <path
        d='M8.30742 1.94466C4.76114 1.94466 1.87637 4.82861 1.87637 8.37497H2.37637C2.37637 5.10483 5.03721 2.44466 8.30742 2.44466V1.94466ZM1.87637 8.37497C1.87637 11.9213 4.7611 14.806 8.30742 14.806V14.306C5.03724 14.306 2.37637 11.6451 2.37637 8.37497H1.87637ZM8.30742 14.806C11.853 14.806 14.7377 11.9213 14.7377 8.37497H14.2377C14.2377 11.6452 11.5768 14.306 8.30742 14.306V14.806ZM14.7377 8.37497C14.7377 4.82863 11.853 1.94466 8.30742 1.94466V2.44466C11.5769 2.44466 14.2377 5.10481 14.2377 8.37497H14.7377ZM8.30638 15.4313C4.41578 15.4313 1.25 12.2656 1.25 8.37496H0.75C0.75 12.5417 4.13963 15.9313 8.30638 15.9313V15.4313ZM1.25 8.37496C1.25 4.4844 4.41574 1.31934 8.30638 1.31934V0.819336C4.13967 0.819336 0.75 4.20819 0.75 8.37496H1.25ZM8.30638 1.31934C12.197 1.31934 15.362 4.48436 15.362 8.37496H15.862C15.862 4.20822 12.4731 0.819336 8.30638 0.819336V1.31934ZM15.362 8.37496C15.362 12.2656 12.1969 15.4313 8.30638 15.4313V15.9313C12.4732 15.9313 15.862 12.5417 15.862 8.37496H15.362Z'
        fill='currentColor'
      />
    </svg>
  );
};

export default SearchSVG;
