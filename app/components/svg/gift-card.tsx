import React from 'react';

const GiftCardSVG = ({ ...props }) => {
  return (
    <svg
      fill='none'
      viewBox='0 0 21 15'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        d='M9.19404 6.67045C10.1373 7.61592 10.9519 8.67687 11.6179 9.82683L10.1853 10.544C9.30002 9.0485 8.14454 7.72365 6.77531 6.63457L6.68197 6.56033L6.5886 6.63454C5.2192 7.723 4.06341 9.04722 3.17762 10.5421L1.74504 9.82498C2.41516 8.66905 3.23524 7.60303 4.18495 6.65373L4.44546 6.39333L4.07715 6.39765C3.0036 6.41024 1.95344 6.09182 1.07452 5.48775C0.482101 4.90131 0.15 4.10921 0.15 3.28404C0.15 2.45464 0.485522 1.65864 1.08367 1.07131C1.6819 0.483896 2.49386 0.15341 3.34105 0.15341C4.18667 0.15341 4.99717 0.482664 5.59509 1.06803C6.00088 1.5208 6.32156 2.04054 6.5423 2.60308L6.68193 2.95891L6.82157 2.60308C7.04234 2.04049 7.36306 1.52069 7.76891 1.06788C8.2988 0.546401 8.99985 0.225148 9.74838 0.161621C10.4984 0.0979653 11.247 0.29727 11.8611 0.723722C12.4752 1.15013 12.915 1.77589 13.1034 2.48959C13.2917 3.20324 13.2165 3.95935 12.8911 4.62399L12.7853 4.83996H13.0258H19.0909H19.2409V4.68996V3.75268C19.2409 3.46343 19.1239 3.18658 18.9164 2.9829C18.7091 2.77931 18.4285 2.66541 18.1364 2.66541H14.4036C14.3281 2.11056 14.1438 1.57589 13.8615 1.09085H18.1364C18.857 1.09085 19.5475 1.37195 20.0562 1.87145C20.5648 2.37086 20.85 3.04761 20.85 3.75268V12.1882C20.85 12.8932 20.5648 13.57 20.0562 14.0694C19.5475 14.5689 18.857 14.85 18.1364 14.85H6.68193C5.96136 14.85 5.27085 14.5689 4.76215 14.0694C4.25354 13.57 3.96832 12.8932 3.96832 12.1882V11.7187L4.21836 11.2373C4.60532 10.5565 5.06065 9.91578 5.5774 9.32459V12.1882C5.5774 12.4774 5.69445 12.7543 5.90188 12.958C6.10922 13.1615 6.38987 13.2754 6.68193 13.2754H18.1364C18.4285 13.2754 18.7091 13.1615 18.9164 12.958C19.1239 12.7543 19.2409 12.4774 19.2409 12.1882V6.56451V6.41451H19.0909H9.30023H8.9387L9.19404 6.67045ZM2.22216 4.38358L2.24538 4.40641L2.27598 4.41756C3.29147 4.78758 4.37538 4.94133 5.45587 4.86868L5.63391 4.85671L5.59151 4.68337C5.37491 3.79785 4.99742 2.95808 4.47727 2.20439L4.46916 2.19265L4.45899 2.18264C4.16232 1.89093 3.76037 1.72736 3.34169 1.72707C2.92301 1.72678 2.52083 1.88979 2.22375 2.18108C1.92657 2.47247 1.75895 2.86843 1.75865 3.28201C1.75835 3.69558 1.92541 4.09177 2.22216 4.38358ZM11.0952 4.41507L11.122 4.4039L11.1427 4.38355C11.2896 4.23902 11.4062 4.06734 11.4858 3.87824C11.5653 3.68914 11.6062 3.48641 11.606 3.28164C11.6058 3.07687 11.5646 2.87421 11.4846 2.68526C11.4047 2.49631 11.2878 2.32485 11.1406 2.18059C10.9934 2.03634 10.8189 1.92211 10.627 1.84429C10.4352 1.76647 10.2297 1.72655 10.0223 1.72674C9.81487 1.72693 9.60947 1.76723 9.41778 1.84541C9.22608 1.92358 9.05177 2.03814 8.90486 2.18266L8.89461 2.19275L8.88646 2.20459C8.37365 2.95023 7.99967 3.77947 7.78205 4.65373L7.73569 4.83996H7.92761H8.79693C9.58517 4.86264 10.3692 4.71773 11.0952 4.41507Z'
        fill='currentColor'
        stroke='currentColor'
        strokeWidth='0.3'
      />
    </svg>
  );
};

export default GiftCardSVG;
