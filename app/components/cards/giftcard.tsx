import Image from 'next/image';
import React, { useEffect, useState } from 'react';
import CashbackSVG from '../svg/cashback';
import clsx from 'clsx';
import OfferCardMenu from '../dropdowns/offer-card-menu';
import cardMenuSVG from '@/public/svg/card-menu-btn.svg';
import SaveSVG, { ShareSVG } from '../svg/save';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import { setShowLeftPanel } from '@/redux/slices/main-header-slice';
import { setLoginModalOpen } from '@/redux/slices/auth-slice';
import { RemoveOfferDto, SaveOfferDto } from '@/services/api/data-contracts';
import { toast } from 'react-toastify';
import { generateGiftCardUrl } from '@/utils/helpers';
import { useRouter } from 'next/dist/client/components/navigation';
import fetchWrapper from '@/utils/fetch-wrapper';

const menuIcons = [
  <SaveSVG
    className='text-primary dark:text-[#A9AFC8] my-[14px] w-[10px] h-[12px]'
    fill={'transparent'}
    key={1}
  />,
  <ShareSVG
    className='text-primary dark:text-[#A9AFC8] my-[14px] w-[10px] h-[12px]'
    key={2}
  />,
];
const menuItems = [
  {
    key: 1,
    value: 'Save',
  },
  {
    key: 2,
    value: 'Share',
  },
];

const Giftcard = ({
  imgUrl,
  title,
  caption,
  fromSavedScreen = false,
  rootClass,
  saved = false,
  uid,
}: {
  imgUrl: string;
  title: string;
  caption: string;
  fromSavedScreen?: boolean;
  rootClass?: string;
  saved: boolean;
  uid: number;
}) => {
  const [showMenuDropdown, setShowMenuDropdown] = useState(false);
  const [isSaved, setIsSaved] = useState(false);
  const router = useRouter();

  const dispatch = useAppDispatch();
  const { isUserLogin } = useAppSelector((state) => state.auth);

  const handleMenuItemClick = (_key: number, value: string) => {
    if (value === 'Save') {
      if (isUserLogin) {
        handleSaveGiftCard();
      } else {
        dispatch(setShowLeftPanel(false));
        dispatch(setLoginModalOpen(true));
      }
    }
  };
  const handleSaveGiftCard = async () => {
    try {
      if (isSaved) {
        const body: RemoveOfferDto = {
          itemUid: uid,
        };
        await fetchWrapper(`/api/proxy/gift-cards/remove`, {
          method: 'POST',
          body: JSON.stringify(body),
          suppressToast: true,
        });
        setIsSaved(false);
        toast.info('Gift card has been removed successfully.');
      } else {
        const body: SaveOfferDto = {
          itemUid: uid,
        };
        await fetchWrapper(`/api/proxy/gift-cards/save`, {
          method: 'POST',
          body: JSON.stringify(body),
          suppressToast: true,
        });
        setIsSaved(true);
        toast.info('Gift card  has been saved successfully.');
      }
    } catch (e: any) {
      const message =
        e?.message ?? 'Save giftcard failed. Please try again later.';
      toast.error(message);
    }
  };

  useEffect(() => {
    setIsSaved(saved);
  }, [saved]);

  if (isSaved === false && fromSavedScreen) {
    return <></>;
  }

  return (
    <div
      className={clsx(
        rootClass,
        'w-[153px] lg:w-[208px] p-[4px] lg:p-[8px] rounded-[5px] shadow-md shrink-0 cursor-pointer'
      )}
      onClick={() => {
        router.push(`/giftcards/${generateGiftCardUrl(title)}?uid=${uid}`);
      }}
    >
      <div className=' relative w-full h-auto rounded-[5px]'>
        <Image alt='giftcard img' height={115} src={imgUrl} width={230} />
        <div
          className='absolute top-[3px] right-[3px]'
          onClick={(e) => {
            e.stopPropagation();
            setShowMenuDropdown(!showMenuDropdown);
          }}
        >
          <Image
            alt='card menu'
            priority={true}
            quality={100}
            src={cardMenuSVG}
          />
          <OfferCardMenu
            cardType='giftcard'
            icons={menuIcons}
            items={menuItems}
            onClickItem={(key: number, value: string) => {
              handleMenuItemClick(key, value);
              setTimeout(() => setShowMenuDropdown(false), 200);
            }}
            rootClass={clsx(
              showMenuDropdown ? 'block' : 'hidden',
              '!top-[22px]'
            )}
            saved={isSaved}
            shareUrl={`/giftcards/${generateGiftCardUrl(title)}?uid=${uid}`}
          />
        </div>
      </div>
      <div className='my-[6px] lg:mt-[12px] lg:mb-[2px]'>
        <p className='text-center text-[8px] sm:text-[9px] lg:text-[10px] font-medium'>
          {title}
        </p>
        <div className='flex-center gap-x-[4px] text-primary dark:text-white mt-[5px]'>
          <CashbackSVG className='text-primary dark:text-white w-[10px] h-[10px]' />
          <span className='text-[10px] font-nexa font-black pt-[4px]'>
            + {caption}
          </span>
        </div>
      </div>
    </div>
  );
};

export default Giftcard;
