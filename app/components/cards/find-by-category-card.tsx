import Image from 'next/image';
import type { KeyboardEvent } from 'react';
import categorySVG from '@/public/svg//category.svg';
import clsx from 'clsx';
import ShimmerEffect, { RectShimmer } from '../atoms/shimmer-effect';
import { formatStoreName } from '@/utils/helpers';
import SmartLink from '../common/smart-link';
import { LinkType } from '@/utils/link-utils';

const FindByCategoryCard = ({
  bgColor,
  deals,
  offering,
  onClickCard,
  searchResultStyle,
  className,
  title,
  url,
  isLoading = false,
}: {
  bgColor?: string;
  deals: number;
  offering: string;
  onClickCard?: () => void;
  searchResultStyle?: boolean;
  className?: string;
  title: string;
  url: string;
  isLoading?: boolean;
}) => {
  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      onClickCard?.();
    }
  };

  if (isLoading) {
    return (
      <div className='flex flex-col items-center justify-center pb-[7px] shrink-0 bg-transparent w-auto'>
        <div
          className={clsx(
            searchResultStyle && '!w-[79px] !h-[46px] !p-[10px]',
            'w-full lg:w-[146px] h-[100px] lg:h-[107px] p-[10px] relative flex items-center justify-center overflow-hidden shadow-md rounded-[5px] shrink-0 transition-all duration-300'
          )}
          style={{ background: '#f0f0f0' }}
        >
          <ShimmerEffect className='w-full h-full' rounded='sm' />
        </div>
        <div
          className={clsx(
            searchResultStyle && '!text-[10px]',
            'text-xs lg:text-sm font-medium mt-[8px] lg:mt-[10px] text-content w-full text-center'
          )}
        >
          <RectShimmer className='mx-auto' height='14px' width='80%' />
        </div>
        <div className='mt-[3px] lg:my-[5px] flex items-center w-full justify-center'>
          <div className='w-[12px] h-[12px] md:w-[12px] md:h-[13px] rounded-full bg-gray-200 dark:bg-gray-700 animate-pulse' />
          <div className='ml-[6px] w-[30px]'>
            <RectShimmer height='12px' width='100%' />
          </div>
          <div className='ml-[4px] w-[40px]'>
            <RectShimmer height='12px' width='100%' />
          </div>
        </div>
        {!searchResultStyle && (
          <div className='hidden lg:flex lg:flex-col items-center mt-[4px] h-[20px] w-full'>
            <RectShimmer className='mx-auto' height='12px' width='40%' />
          </div>
        )}
      </div>
    );
  }

  return (
    <SmartLink
      className={clsx(
        'flex flex-col items-center justify-center pb-[7px] shrink-0 cursor-pointer bg-transparent border-0 text-inherit w-auto',
        className
      )}
      href={`/store/${formatStoreName(title)}`}
      linkType={LinkType.INTERNAL}
      onClick={onClickCard}
      onKeyDown={handleKeyDown}
    >
      <div
        className={clsx(
          searchResultStyle && '!w-24 !h-16 !p-[10px]',
          'w-full aspect-[16/9] p-[10px] bg-[#FBFBFB] dark:bg-[#464953] relative flex items-center justify-center overflow-hidden shadow-md rounded-[5px] shrink-0 transition-all duration-300 hover:scale-105 hover:shadow-lg active:scale-[0.97]'
        )}
        style={{
          backgroundColor: bgColor === '#70367c' ? '#ffffff' : bgColor,
        }}
      >
        <div
          className='w-full h-full flex items-center justify-center transition-opacity duration-200 hover:opacity-100'
          style={{ opacity: 0.9 }}
        >
          <Image
            alt={'category img'}
            className='!static object-contain'
            fill
            quality={100}
            src={url}
          />
        </div>
      </div>
      <span
        className={clsx(
          searchResultStyle && '!text-[10px]',
          'text-xs lg:text-sm font-medium mt-2 lg:mt-4 text-content'
        )}
      >
        {title}
      </span>
      <div className='mt-[3px] lg:my-[5px] text-content flex items-center'>
        <Image
          alt='icon'
          className='w-[12px] h-[12px] md:w-[12px] md:h-[13px]'
          src={categorySVG}
        />
        <span
          className={clsx(
            searchResultStyle && '!text-[10px]',
            'ml-2 text-xs font-black font-nexa'
          )}
        >
          {deals}
        </span>
        <span
          className={clsx(
            searchResultStyle && '!text-[10px]',
            'text-xs font-medium ml-1 pt-[1px]'
          )}
        >
          Deals
        </span>
      </div>
      {!searchResultStyle && (
        <div className='hidden lg:flex lg:flex-col text-center mt-1 text-primary text-xs h-[20px]'>
          <span className='font-black font-nexa mt-0.5 transition-transform duration-300 hover:scale-110'>
            {offering}
          </span>
        </div>
      )}
    </SmartLink>
  );
};

export default FindByCategoryCard;
