'use client';
import clsx from 'clsx';
import Image from 'next/image';
import React from 'react';

const BlogCard = ({
  blogImg,
  userPic,
  userName,
  postedOn,
  category,
  readTime,
  blogTitle,
  rootClass,
}: {
  blogImg: string;
  userPic: string;
  userName: string;
  postedOn: string;
  category: string;
  readTime: string;
  blogTitle: string;
  rootClass?: string;
}) => {
  return (
    <div
      className={clsx(
        rootClass,
        'w-[200px] lg:w-[314px] rounded-[5px] shrink-0 shadow-md cursor-pointer'
      )}
    >
      <div className='relative overflow-hidden h-[180px] rounded-t-[5px]'>
        <Image
          alt='blog-img'
          className='object-cover'
          fill
          src={blogImg || '/temp/blog1.png'}
        />
        <div className='flex flex-col justify-center items-left absolute bottom-[8px] pl-[8px]'>
          <div className='flex pl-[8px] items-center'>
            <div className='py-[2px] px-[5px] lg:py-[5px] lg:px-[8px] text-[7px] lg:text-[10px] shrink-0 rounded-[2.5px] bg-[#0000007d] text-white flex items-center justify-center font-bold'>
              {category}
            </div>
            <span className='text-[7px] lg:text-[9px] font-bold leading-[18px] text-white ml-[5px]'>
              {readTime}
            </span>
          </div>
          <div className='mt-[6px] text-left text-white text-[15px] font-bold leading-[18px] px-[8px]'>
            {blogTitle}
          </div>
        </div>
      </div>
      <div className='h-[60px] lg:h-[75px] bg-[#f1f1f1] w-full flex items-center justify-left pl-[10px] rounded-b-[5px]'>
        <div className='relative w-[30px] h-[30px] lg:w-[50px] lg:h-[50px] shrink-0'>
          <Image alt='user pic' className='object-cover' fill src={userPic} />
        </div>
        <div className='ml-[10px] flex flex-col justify-center gap-y-[2px]'>
          <span className='text-[10px] lg:text-sm font-semibold text-black'>
            {userName}
          </span>
          <span className='text-[#616161] font-[300] text-[8px] lg:text-[10px] mt-[2px]'>
            {postedOn}
          </span>
        </div>
      </div>
    </div>
  );
};

export default BlogCard;
