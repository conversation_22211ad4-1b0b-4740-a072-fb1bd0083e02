import Image from 'next/image';
import React from 'react';
// import HeartIcon from '../svg/heart-icon';

const CategoryCard = ({
  imgUrl,
  text,
  // isLiked,
  onClickCard,
}: {
  imgUrl: string;
  text: string;
  isLiked?: boolean;
  onClickCard?: () => void;
}) => {
  return (
    <div
      className='h-full min-w-[82px] bg-white dark:bg-[#3E424C] rounded-[6px] relative shadow-md cursor-pointer'
      onClick={onClickCard}
      style={{ boxShadow: 'box-shadow: 0px 3px 4px 0px rgba(0, 0, 0, 0.08)' }}
    >
      {/* <div className='absolute top-[7px] right-[9px]'>
        <HeartIcon
          className='w-[15px] h-[14px] text-[#988BFC]'
          fill={isLiked ? '#988BFC' : 'transparent'}
        />
      </div> */}
      <div className='min-h-[90px] px-[8px] pt-[12px] pb-[6px] flex-center'>
        <Image
          alt='category img'
          className='w-full h-auto max-h-[80px] object-contain'
          height={100}
          src={imgUrl}
          width={100}
        />
      </div>
      <div className='text-center text-[10px] text-blackWhite pt-[6px] pb-[12px] px-[12px]'>
        {text}
      </div>
    </div>
  );
};

export default CategoryCard;
