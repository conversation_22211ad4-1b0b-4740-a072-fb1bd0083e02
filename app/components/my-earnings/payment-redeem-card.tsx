import React from 'react';
import RightArrow from '../svg/right-arrow';
import Image from 'next/image';
import offerBadge from '@/public/svg/offer-badge.svg';
import clsx from 'clsx';
import { motion } from 'framer-motion';

const PaymentRedeemCard = ({
  imgUrl,
  title,
  caption,
  offer,
  imgClass,
  onClick,
}: {
  imgUrl: string;
  title: string;
  caption: string;
  offer?: { percent: number; upto?: boolean };
  imgClass?: string;
  onClick?: () => void;
}) => {
  return (
    <motion.div
      animate={{ opacity: 1, y: 0 }}
      className='relative rounded-[4px] bg-white dark:bg-[#3E424C] px-[10px] py-[14px] lg:px-[16px] shadow-md cursor-pointer'
      initial={{ opacity: 0, y: 20 }}
      onClick={onClick}
      transition={{ duration: 0.5 }}
      whileHover={{ scale: 1.05, boxShadow: '0px 8px 30px rgba(0, 0, 0, 0.1)' }}
      whileTap={{ scale: 0.97 }}
    >
      <motion.div
        animate={{ opacity: 1, x: 0 }}
        initial={{ opacity: 0, x: 10 }}
        transition={{ duration: 0.3, delay: 0.1 }}
        whileHover={{ x: 5 }}
      >
        <RightArrow className='w-[10px] h-[7px] text-blackWhite absolute top-[12px] right-[14px]' />
      </motion.div>
      {Boolean(offer?.percent) && (
        <motion.div
          animate={{ opacity: 1, scale: 1, rotate: 0 }}
          className='absolute top-[-8px] left-[-5px] drop-shadow-md'
          initial={{ opacity: 0, scale: 0, rotate: -50 }}
          transition={{ duration: 0.5, delay: 0.2, type: 'spring' }}
        >
          <Image alt='offer badge' src={offerBadge} />
          <motion.div
            animate={{ opacity: 1 }}
            className='w-[90%] absolute top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%] rotate-[-38.297deg] text-white flex-center flex-col'
            initial={{ opacity: 0 }}
            transition={{ duration: 0.3, delay: 0.3 }}
          >
            {offer?.upto && (
              <span className='text-[5px] font-medium'>Up to</span>
            )}
            <span className='text-[7px] font-black font-nexa mt-[2px]'>
              {offer?.percent}%
            </span>
            {!offer?.upto && <span className='text-[7px] font-bold'>CB</span>}
          </motion.div>
        </motion.div>
      )}
      <motion.div
        animate={{ opacity: 1 }}
        className='flex flex-col h-full justify-between'
        initial={{ opacity: 0 }}
        transition={{ duration: 0.5, delay: 0.4 }}
      >
        <motion.div
          animate={{ opacity: 1, y: 0 }}
          initial={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.5, delay: 0.5 }}
          whileHover={{ scale: 1.1, rotate: 5 }}
          whileTap={{ scale: 0.9 }}
        >
          <Image
            alt=''
            className={clsx(imgClass, 'w-[60px] lg:w-[90px] h-auto mx-auto')}
            height={90}
            src={imgUrl}
            width={90}
          />
        </motion.div>
        <motion.div
          animate={{ opacity: 1, y: 0 }}
          className='mt-[8px]'
          initial={{ opacity: 0, y: 10 }}
          transition={{ duration: 0.5, delay: 0.6 }}
        >
          <motion.h4
            animate={{ opacity: 1 }}
            className='text-[10px] font-semibold uppercase text-center'
            initial={{ opacity: 0 }}
            transition={{ duration: 0.3, delay: 0.7 }}
          >
            {title}
          </motion.h4>
          <motion.p
            animate={{ opacity: 1 }}
            className='text-[7px] font-normal capitalize text-center'
            initial={{ opacity: 0 }}
            transition={{ duration: 0.3, delay: 0.8 }}
          >
            {caption}
          </motion.p>
        </motion.div>
      </motion.div>
    </motion.div>
  );
};

export default PaymentRedeemCard;
