import clsx from 'clsx';
import React from 'react';
import { motion } from 'framer-motion';

const RedeemHistoryCont = ({ status }: { status: 'Success' | 'Failed' }) => {
  return (
    <motion.div
      animate={{ opacity: 1, y: 0 }}
      className='rounded-[5px] overflow-hidden flex w-full shadow-sm'
      initial={{ opacity: 0, y: 20 }}
      transition={{ duration: 0.3 }}
      whileHover={{ boxShadow: '0px 8px 30px rgba(0, 0, 0, 0.1)' }}
    >
      <motion.div
        animate={{ opacity: 1 }}
        className='flex flex-col w-full'
        initial={{ opacity: 0 }}
        transition={{ duration: 0.3, delay: 0.1 }}
      >
        <motion.div
          animate={{ opacity: 1 }}
          className='flex w-full items-center justify-evenly bg-white dark:bg-[#3E424C] h-[46px] lg:h-[52px] shrink-0'
          initial={{ opacity: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <motion.div
            animate={{ opacity: 1, y: 0 }}
            className='flex-center flex-col lg:flex-row text-[10px] lg:text-xs '
            initial={{ opacity: 0, y: 10 }}
            transition={{ duration: 0.3, delay: 0.3 }}
          >
            <span className='text-[#969696] dark:text-[#ccc9c9df]'>
              Giftcard ID:
            </span>
            <span className='text-blackWhite mt-[3px] lg:mt-0 lg:ml-[10px]'>
              ICBGC123456789
            </span>
          </motion.div>
          <motion.div
            animate={{ opacity: 1, scaleY: 1 }}
            className='w-[1px] h-[23px] bg-[#E1E1E1] shrink-0 grow-0 basis-[1px]'
            initial={{ opacity: 0, scaleY: 0 }}
            transition={{ duration: 0.3, delay: 0.4 }}
          />
          <motion.div
            animate={{ opacity: 1, y: 0 }}
            className='flex-center flex-col lg:flex-row text-[10px] lg:text-xs'
            initial={{ opacity: 0, y: 10 }}
            transition={{ duration: 0.3, delay: 0.5 }}
          >
            <span className='text-[#969696] dark:text-[#ccc9c9df]'>
              Amount:
            </span>
            <motion.span
              animate={{ scale: 1 }}
              className='text-blackWhite mt-[3px] lg:mt-0 font-bold lg:ml-[10px]'
              initial={{ scale: 0.8 }}
              transition={{ duration: 0.3, delay: 0.6 }}
              whileHover={{ scale: 1.1 }}
            >
              ₹ 1,356
            </motion.span>
          </motion.div>
        </motion.div>
        <motion.div
          animate={{ opacity: 1 }}
          className='bg-container dark:bg-[#31343D] h-[40px] lg:h-[48px] flex items-center justify-between px-[20px]'
          initial={{ opacity: 0 }}
          transition={{ duration: 0.3, delay: 0.7 }}
        >
          <motion.div
            animate={{ opacity: 1, x: 0 }}
            className='flex gap-x-[10px] w-full lg:justify-around'
            initial={{ opacity: 0, x: -10 }}
            transition={{ duration: 0.3, delay: 0.8 }}
          >
            <div className='text-[10px] lg:text-xs'>
              <span className='text-[#969696] dark:text-[#ccc9c9df]'>
                Date:
              </span>
              <span className='text-blackWhite ml-[2px] lg:ml-[5px]'>
                12 JUN 2023
              </span>
            </div>
            <div className='text-[8px] lg:text-xs'>
              <span className='text-[#969696] dark:text-[#ccc9c9df]'>
                Time:
              </span>
              <span className='text-blackWhite ml-[2px] lg:ml-[5px]'>
                12:55 PM
              </span>
            </div>
          </motion.div>
          <motion.div
            animate={{ opacity: 1, scale: 1 }}
            className='lg:hidden'
            initial={{ opacity: 0, scale: 0 }}
            transition={{ duration: 0.3, delay: 0.9 }}
          >
            <motion.span
              className={clsx(
                status === 'Failed' && '!text-[#FF4141]',
                'text-[10px] text-[#407BFF] font-semibold'
              )}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              {status}
            </motion.span>
          </motion.div>
        </motion.div>
      </motion.div>
      <motion.div
        animate={{ opacity: 1, x: 0 }}
        className='w-[165px] hidden lg:flex-center bg-[#f1f1f1] dark:bg-[#3e424c] border-l-[1px] border-gray-300'
        initial={{ opacity: 0, x: 20 }}
        transition={{ duration: 0.3, delay: 0.8 }}
      >
        <motion.span
          animate={{ scale: 1 }}
          className='text-[14px] text-[#407BFF] font-semibold'
          initial={{ scale: 0.8 }}
          transition={{ duration: 0.3, delay: 0.9 }}
          whileHover={{ scale: 1.1 }}
        >
          {status === 'Failed' ? 'Failed' : 'Paid'}
        </motion.span>
      </motion.div>
    </motion.div>
  );
};

export default RedeemHistoryCont;
