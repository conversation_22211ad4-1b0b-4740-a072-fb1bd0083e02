import React from 'react';
import clsx from 'clsx';
import { Switch } from 'antd';

const Subscriptioncontainer = ({
  icon,
  title,
  caption,
  rootClass,
}: {
  icon: React.ReactNode;
  title: string;
  caption: string;
  rootClass?: string;
}) => {
  return (
    <div
      className={clsx(
        rootClass,
        'flex lg:items-center w-full pl-[13px] py-[15px] pr-[10px] lg:p-[10px] rounded-[6px]'
      )}
    >
      {icon}
      <div className='ml-[12px] lg:mt-0 lg:ml-[13px] flex flex-col'>
        <h4 className='text-[10px] lg:text-xs font-medium text-blackWhite'>
          {title}
        </h4>
        <p className='mt-[4px] lg:mt-[6px] leading-normal text-[8px] sm:text-[9px] lg:text-[10px] text-[#666] dark:text-[#BEC6DB]'>
          {caption}
        </p>
      </div>
      <div className='ml-auto user-subscription'>
        <Switch
          checkedChildren={
            <span className='text-[9px] lg:text-[10px] translate-y-[-1px] font-medium text-blackWhite inline-block'>
              On
            </span>
          }
          className='!bg-white dark:!bg-[#555966] shadow-md'
          unCheckedChildren={
            <span className='text-[9px] lg:text-[10px] translate-y-[-1px] font-medium text-blackWhite inline-block'>
              Off
            </span>
          }
        />
      </div>
    </div>
  );
};

export default Subscriptioncontainer;
