import Image from 'next/image';
import React from 'react';
import RightArrow from '../svg/right-arrow';
import clsx from 'clsx';
import { motion } from 'framer-motion';

const LinkContainer = ({
  iconUrl,
  title,
  caption,
  notificationCount,
  iconClass,
  rootClass,
  onClick,
}: {
  iconUrl: string;
  title: string;
  caption: string;
  notificationCount?: number;
  iconClass?: string;
  rootClass?: string;
  onClick?: () => void;
}) => {
  return (
    <motion.div
      animate={{ opacity: 1, y: 0 }}
      className={clsx(
        rootClass,
        'flex lg:flex-col lg:flex-center pl-[13px] py-[15px] pr-[18px] lg:p-[10px] lg:w-[218px] lg:min-h-[154px] bg-white dark:bg-[#3E424C] rounded-[6px]'
      )}
      initial={{ opacity: 0, y: 20 }}
      onClick={onClick}
      style={{ boxShadow: '0px 1px 5px 0px rgba(0, 0, 0, 0.07)' }}
      transition={{ duration: 0.4 }}
      whileHover={{
        scale: 1.03,
        boxShadow: '0px 5px 15px rgba(0, 0, 0, 0.1)',
        transition: { duration: 0.3 },
      }}
      whileTap={{ scale: 0.97 }}
    >
      <motion.div
        animate={{ scale: 1 }}
        initial={{ scale: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        whileHover={{ rotate: 5, scale: 1.1, transition: { duration: 0.2 } }}
      >
        <Image
          alt='icon'
          className={iconClass}
          height={30}
          src={iconUrl}
          width={30}
        />
      </motion.div>
      <motion.div
        animate={{ opacity: 1, x: 0 }}
        className='ml-[12px] lg:ml-0 lg:mt-[13px] flex flex-col lg:flex-center'
        initial={{ opacity: 0, x: -10 }}
        transition={{ duration: 0.4, delay: 0.3 }}
      >
        <div className='flex'>
          <motion.h4
            animate={{ opacity: 1 }}
            className='text-[11px] lg:text-xs font-medium text-blackWhite'
            initial={{ opacity: 0 }}
            transition={{ duration: 0.3, delay: 0.4 }}
          >
            {title}
          </motion.h4>
          {notificationCount && (
            <motion.div
              animate={{ scale: 1 }}
              className='hidden lg:block p-[5px] text-[10px] bg-[#F06B6B] font-bold text-white rounded-[5px]'
              initial={{ scale: 0 }}
              transition={{ duration: 0.3, delay: 0.5, type: 'spring' }}
              whileHover={{ scale: 1.1 }}
            >
              {notificationCount}
            </motion.div>
          )}
        </div>
        <motion.p
          animate={{ opacity: 1 }}
          className='mt-[6px] lg:mt-[13px] lg:text-center leading-normal text-[8px] sm:text-[9px] lg:text-[10px] text-[#666] dark:text-[#BEC6DB]'
          initial={{ opacity: 0 }}
          transition={{ duration: 0.3, delay: 0.5 }}
        >
          {caption}
        </motion.p>
      </motion.div>
      <motion.div
        animate={{ opacity: 1, x: 0 }}
        className='flex lg:hidden ml-auto'
        initial={{ opacity: 0, x: 10 }}
        transition={{ duration: 0.4, delay: 0.4 }}
      >
        {notificationCount && (
          <motion.div
            animate={{ scale: 1 }}
            className='hidden lg:block p-[5px] text-[10px] bg-[#F06B6B] font-bold text-white rounded-[5px]'
            initial={{ scale: 0 }}
            transition={{ duration: 0.3, delay: 0.5, type: 'spring' }}
            whileHover={{ scale: 1.1 }}
          >
            {notificationCount}
          </motion.div>
        )}
        <motion.div
          transition={{ type: 'spring', stiffness: 400, damping: 10 }}
          whileHover={{ x: 5 }}
        >
          <RightArrow className='text-blackWhite w-[10px]' />
        </motion.div>
      </motion.div>
    </motion.div>
  );
};

export default LinkContainer;
