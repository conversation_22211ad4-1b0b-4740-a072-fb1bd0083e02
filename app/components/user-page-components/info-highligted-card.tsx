import clsx from 'clsx';
import React from 'react';
import { motion } from 'framer-motion';

const InfoHighligtedCard = ({
  icon,
  number,
  caption,
  stripColorClass,
  onClick,
}: {
  icon: React.ReactNode;
  number: number;
  caption: string;
  stripColorClass: string;
  onClick?: () => void;
}) => {
  return (
    <motion.div
      animate={{ opacity: 1, y: 0 }}
      className='w-[71px] z-10 lg:w-[84px] h-[72px] bg-white dark:bg-[#3E424C] shrink-0 rounded-[5px] relative before:w-[63px] before:bottom-[-4px] before:left-[4px] before:h-[72px] before:rounded-b-[5px] before:opacity-50 before:bg-white dark:before:bg-[#3E424C] before:absolute before:z-[-1] lg:before:content-none cursor-pointer'
      initial={{ opacity: 0, y: 20 }}
      onClick={onClick}
      transition={{ duration: 0.3 }}
      whileHover={{ scale: 1.05, transition: { duration: 0.2 } }}
      whileTap={{ scale: 0.95 }}
    >
      <motion.div
        animate={{ scaleY: 1 }}
        className={clsx(
          stripColorClass,
          'h-[22px] w-[4px] rounded-r-[10px] absolute top-[8px] left-0'
        )}
        initial={{ scaleY: 0 }}
        transition={{ duration: 0.4, delay: 0.2 }}
      />
      <motion.div
        animate={{ opacity: 1 }}
        className='w-full h-full flex-col flex-center relative z-[1]'
        initial={{ opacity: 0 }}
        transition={{ duration: 0.4, delay: 0.3 }}
      >
        <motion.div
          animate={{ scale: 1 }}
          initial={{ scale: 0 }}
          transition={{ duration: 0.3, delay: 0.4 }}
        >
          {icon}
        </motion.div>
        <motion.p
          animate={{ opacity: 1 }}
          className='mt-[8px] text-xs font-bold text-black dark:text-white'
          initial={{ opacity: 0 }}
          transition={{ duration: 0.3, delay: 0.5 }}
        >
          {number}
        </motion.p>
        <motion.p
          animate={{ opacity: 1 }}
          className='text-[8px] lg:text-[10px] text-black dark:text-white'
          initial={{ opacity: 0 }}
          transition={{ duration: 0.3, delay: 0.6 }}
        >
          {caption}
        </motion.p>
      </motion.div>
    </motion.div>
  );
};

export default InfoHighligtedCard;
