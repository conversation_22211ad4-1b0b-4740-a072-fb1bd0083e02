'use client';
import type React from 'react';
import { useEffect, useRef, useState } from 'react';
import SearchSVG from './svg/search';
import CrossSVG from './svg/cross';
import clsx from 'clsx';
import ToolbarDropdown from './atoms/toolbar-dropdown';
import FilterSVG from './svg/filter';
import type { MenuProps } from 'antd';
import SortSVG from './svg/sort';
import DateRangePickerCustom from './dropdowns/date-range-picker';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import {
  setSearchValue,
  setSelectedSort,
} from '@/redux/slices/common-filters-slice';
import { useCreateQueryString } from '@/utils/custom-hooks';
import type { SortTypes } from '@/services/api/data-contracts';

export const sortItems: MenuProps['items'] = [
  {
    label: 'Newest',
    key: 'newest',
  },
  {
    label: 'Highest CB Amount',
    key: 'highestCbAmount',
  },
  {
    label: 'Highest CB %',
    key: 'highestCbPercent',
  },
  // {
  //   label: 'Alphabetical',
  //   key: 'alphabetical',
  // },
];

export const reviewSortItems: MenuProps['items'] = [
  {
    label: 'Newest',
    key: 'newest',
  },
  {
    label: 'Rating',
    key: 'rating',
  },
];

export const storeSortItems: MenuProps['items'] = [
  {
    label: 'Popular',
    key: 'popular',
  },
  {
    label: 'Newest',
    key: 'newest',
  },
  {
    label: 'Highest CB Amount',
    key: 'highestCbAmount',
  },
  {
    label: 'Highest CB %',
    key: 'highestCbPercent',
  },
  {
    label: 'Alphabetical',
    key: 'alphabetical',
  },
];

export const dealsAndCouponsSortItems: MenuProps['items'] = [
  {
    label: 'Popular',
    key: 'popular',
  },
  {
    label: 'Highest CB Amount',
    key: 'highestCbAmount',
  },
  {
    label: 'Highest CB %',
    key: 'highestCbPercent',
  },
  // {
  //   label: 'Alphabetical',
  //   key: 'alphabetical',
  // },
  {
    label: 'Newest',
    key: 'newest',
  },
];

export const storeTypeSortItems = storeSortItems as {
  key: number;
  label: string;
}[];

export const dealsAndCouponsTypeSortItems = dealsAndCouponsSortItems as {
  key: number;
  label: string;
}[];

const customTypeSortItems = sortItems as {
  key: number;
  label: string;
}[];

const CommonToolbar = ({
  onClickFilterBtn, // sortItems,
  rootClassName,
  hideFilter = false,
  showDatePicker = false,
  customSortItems = [],
  initialSort,
}: {
  onClickFilterBtn?: () => void;
  rootClassName?: string;
  hideFilter?: boolean;
  showDatePicker?: boolean;
  customSortItems?: { label: string; key: number }[]; // New prop for passing custom sortItems
  initialSort?: { label: string; key: number };
}) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const [searchActive, setSearchActive] = useState(false);
  const pathname = usePathname();
  const { replace } = useRouter();
  const searchParams = useSearchParams();
  const { searchValue, selectedSort } = useAppSelector(
    (state) => state.commonFilters
  );
  const dispatch = useAppDispatch();

  useEffect(() => {
    dispatch(setSearchValue(searchParams.get('searchParam') || ''));
    dispatch(
      setSelectedSort(
        (searchParams.get('sortType') as SortTypes) ||
          (initialSort ? initialSort?.label.toLocaleLowerCase() : 'newest')
      )
    );
  }, [searchParams, dispatch, initialSort]);

  const createQueryString = useCreateQueryString(searchParams);

  const handleCloseSearch = () => {
    if (inputRef.current) {
      if (inputRef.current.value) {
        inputRef.current.value = '';
        inputRef.current.focus();
        dispatch(setSearchValue(''));
      } else {
        setSearchActive(false);
      }
    }
  };

  const handleSearch = (e: React.KeyboardEvent<HTMLInputElement>) => {
    const newSearchValue = inputRef.current?.value;
    if (e.key === 'Enter' && newSearchValue) {
      replace(
        pathname + '?' + createQueryString('searchParam', newSearchValue)
      );
    }
  };

  const onClickSortBy: MenuProps['onClick'] = ({ key }) => {
    dispatch(setSelectedSort(key as SortTypes));
    replace(pathname + '?' + createQueryString('sortType', key));
  };

  const onClickSortBy2 = (label: SortTypes) => {
    dispatch(setSelectedSort(label));
    replace(pathname + '?' + createQueryString('sortType', label));
  };

  // Replace customTypeSortItems with customSortItems if provided
  const sortItemsToDisplay = customSortItems.length
    ? customSortItems
    : customTypeSortItems;

  return (
    <div
      className={clsx(
        rootClassName,
        'flex flex-wrap gap-x-[8px] gap-y-[10px] justify-between items-center py-[10px] px-[15px] md:px-[20px] lg:px-[30px] transition-all duration-300 bg-[#E1E2E4] dark:bg-[#2D313A] shadow-sm z-90'
      )}
    >
      {/* Sort options - desktop */}
      <div
        className={clsx(
          searchActive && 'hidden md:flex',
          'hidden md:flex gap-x-[10px] lg:gap-x-[15px] items-center flex-wrap py-2'
        )}
      >
        <SortSVG className='w-[14px] lg:w-[20px] text-[#7366D9] dark:text-white transition-transform duration-300 hover:scale-110' />
        <span className='text-xs font-semibold whitespace-nowrap text-blackWhite'>
          Sort :
        </span>

        {sortItemsToDisplay.map((item) => (
          <span
            className={clsx(
              String(item?.key) === selectedSort && '!text-primary !font-bold',
              `text-xs font-medium text-blackWhite cursor-pointer transition-all duration-200 hover:text-primary`
            )}
            key={item?.key}
            onClick={() => {
              const sortKey = String(item?.key);
              onClickSortBy2(sortKey as SortTypes);
            }}
          >
            {item?.label}
          </span>
        ))}
      </div>

      {/* Controls container for mobile */}
      <div className='flex md:hidden items-center gap-x-[8px] md:order-2'>
        {/* Sort dropdown - mobile */}
        <ToolbarDropdown
          className={clsx(searchActive && 'hidden md:flex py-5', 'md:hidden')}
          items={sortItemsToDisplay.map((item) => ({
            label: item.label,
            key: String(item.key),
          }))}
          name='Sort'
          onClick={onClickSortBy}
        >
          <>
            <SortSVG className='w-[14px] lg:w-[20px] text-[#7366D9] dark:text-white transition-transform duration-300 hover:scale-110' />
            <FilterCount char={(selectedSort?.charAt(0) || '').toUpperCase()} />
          </>
        </ToolbarDropdown>

        {/* Filter button */}
        {hideFilter === false && (
          <button
            className={clsx(
              searchActive && 'hidden md:flex',
              'relative flex bg-white dark:bg-[#3D4049] h-[40px] w-[65px] rounded-[5px] text-[12px] text-[#1C132E] dark:text-white font-medium justify-evenly items-center lg:dark:bg-[#515662] lg:hidden transition-all duration-300 hover:shadow-md active:scale-95'
            )}
            onClick={onClickFilterBtn}
            style={{ boxShadow: '0px 4px 4px 0px rgba(0, 0, 0, 0.07)' }}
          >
            <FilterSVG className='w-[10px] lg:w-[15px] text-[#7366D9] dark:text-white transition-transform duration-300 hover:scale-110' />
            <span>Filter</span>
          </button>
        )}

        {/* Date picker */}
        {showDatePicker && (
          <DateRangePickerCustom
            rootClassName={clsx(searchActive && 'hidden lg:flex')}
          />
        )}
      </div>

      {/* Search bar */}
      <div
        className={clsx(
          searchActive
            ? 'w-full lg:w-[280px]'
            : 'w-[180px] md:w-[220px] lg:w-[269px]',
          'flex items-center justify-end h-[40px] bg-white dark:bg-container rounded-[5px] px-4 cursor-pointer relative z-[501] transition-all duration-300 hover:shadow-md md:order-1 ml-auto md:ml-0'
        )}
        style={{ boxShadow: '0px 4px 4px 0px rgba(0, 0, 0, 0.07)' }}
      >
        <input
          className='placeholder:text-[#AFAFAF] h-full w-full outline-none bg-transparent dark:text-white text-[12px] transition-all duration-200'
          defaultValue={searchValue}
          onBlur={() => {
            if (inputRef.current?.value === '') {
              setSearchActive(false);
              replace(pathname + '?' + createQueryString('searchParam', ''));
            }
          }}
          onFocus={() => setSearchActive(true)}
          onKeyDown={(e) => handleSearch(e)}
          placeholder={'search..'}
          ref={inputRef}
          type='text'
        />
        {!searchActive ? (
          <div className='h-full flex justify-center items-center'>
            <SearchSVG className='w-[14px] lg:w-[18px] text-[#7366D9] dark:text-white transition-transform duration-300 hover:scale-110' />
          </div>
        ) : (
          <div
            className='h-full flex items-center justify-center pl-2'
            onClick={() => {
              handleCloseSearch();
              replace(pathname + '?' + createQueryString('searchParam', ''));
            }}
          >
            <CrossSVG className='w-[10px] lg:w-[12px] text-[#7366D9] dark:text-white cursor-pointer transition-transform duration-300 hover:scale-110 active:scale-90' />
          </div>
        )}
      </div>
    </div>
  );
};

export const FilterCount = ({
  totalFilters,
  char,
}: {
  totalFilters?: number;
  char?: string;
}) => {
  return (
    <span className='absolute uppercase right-[-4px] w-[15px] h-[15px] top-[-4px] lg:top-[-9px] lg:h-[18px] lg:w-[18px] rounded-full bg-primary flex-center text-white font-nexa text-[7px] font-black pt-[2px] lg:pt-[3px] transition-transform duration-300 hover:scale-110'>
      {totalFilters || char}
    </span>
  );
};

export default CommonToolbar;
