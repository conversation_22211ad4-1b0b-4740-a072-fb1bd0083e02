'use client';
import { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import DateIcon from '../svg/date-icon';
import { DatePicker, Modal } from 'antd';
import dayjs, { type Dayjs } from 'dayjs';
import clsx from 'clsx';
import { useAppDispatch } from '@/redux/hooks';
import { setDateRangeFilter } from '@/redux/slices/common-filters-slice';
import { usePathname, useSearchParams } from 'next/navigation';
import {
  useCreateMultiQueryString,
  // useCreateQueryString,
} from '@/utils/custom-hooks';
import { useRouter } from 'next/navigation';

function DateRangePickerCustom({ rootClassName }: { rootClassName?: string }) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [datePicker, setDatePicker] = useState<{
    startValue: Dayjs;
    endValue: Dayjs;
    endOpen: boolean;
  }>({
    startValue: dayjs(),
    endValue: dayjs(),
    endOpen: false,
  });

  const dispatch = useAppDispatch();

  const searchParams = useSearchParams();

  const createMultiQueryString = useCreateMultiQueryString(searchParams);
  const pathname = usePathname();
  const { replace } = useRouter();

  const handleOk = () => {
    // Check if dates are valid before formatting
    const startIsValid =
      datePicker.startValue && datePicker.startValue.isValid();
    const endIsValid = datePicker.endValue && datePicker.endValue.isValid();

    const dateValue = {
      startDate: startIsValid ? datePicker.startValue.format('YYYY-MM-DD') : '',
      endDate: endIsValid ? datePicker.endValue.format('YYYY-MM-DD') : '',
    };

    dispatch(setDateRangeFilter(dateValue));

    // Only add parameters to URL if they are valid
    const queries = [];
    if (startIsValid && dateValue.startDate) {
      queries.push({ name: 'startDate', value: dateValue.startDate });
    }
    if (endIsValid && dateValue.endDate) {
      queries.push({ name: 'endDate', value: dateValue.endDate });
    }

    const queryString = createMultiQueryString(queries);
    replace(pathname + (queryString ? '?' + queryString : ''));
    setIsModalOpen(false);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const disabledStartDate = (startValue: Dayjs) => {
    const { endValue } = datePicker;
    if (!startValue || !endValue) {
      return false;
    }
    return startValue.valueOf() > endValue.valueOf();
  };

  const disabledEndDate = (endValue: Dayjs) => {
    const { startValue } = datePicker;
    if (!endValue || !startValue) {
      return false;
    }
    return endValue.valueOf() <= startValue.valueOf();
  };

  const onChange = (field: string, value: any) => {
    // const serializedValue = new Date(value).toISOString();
    setDatePicker({ ...datePicker, [field]: value });
  };

  //     const serializedEndDate = new Date(selection.endDate).toISOString();

  const onStartChange = (value: any) => {
    onChange('startValue', value);
  };

  const onEndChange = (value: any) => {
    onChange('endValue', value);
  };

  const handleStartOpenChange = (open: boolean) => {
    if (!open) {
      setDatePicker({ ...datePicker, endOpen: true });
    }
  };

  const handleEndOpenChange = (open: boolean) => {
    setDatePicker({ ...datePicker, endOpen: open });
  };

  useEffect(() => {
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    // Only dispatch valid dates to Redux
    dispatch(
      setDateRangeFilter({
        startDate: startDate ?? '',
        endDate: endDate ?? '',
      })
    );

    // Create valid dayjs objects only if dates are valid
    const startDayjs = startDate ? dayjs(startDate) : dayjs();
    const endDayjs = endDate ? dayjs(endDate) : dayjs();

    setDatePicker({
      endOpen: false,
      startValue: startDayjs.isValid() ? startDayjs : dayjs(),
      endValue: endDayjs.isValid() ? endDayjs : dayjs(),
    });
  }, [searchParams, dispatch]);

  return (
    <>
      <motion.button
        animate={{ opacity: 1, y: 0 }}
        className={clsx(
          rootClassName,
          'overflow-hidden bg-white dark:bg-[#3D4049] h-[40px] w-[65px] lg:w-[92px] rounded-[5px] text-[12px] lg:text-[12px] text-[#1C132E] dark:text-white font-medium lg:font-semibold flex justify-evenly items-center lg:dark:bg-[#515662]'
        )}
        initial={{ opacity: 0, y: 10 }}
        onClick={() => setIsModalOpen(!isModalOpen)}
        style={{ boxShadow: '0px 4px 4px 0px rgba(0, 0, 0, 0.07)' }}
        transition={{ duration: 0.3 }}
        whileHover={{
          scale: 1.05,
          boxShadow: '0px 8px 15px rgba(0, 0, 0, 0.1)',
        }}
        whileTap={{ scale: 0.95 }}
      >
        <motion.div
          animate={{ opacity: 1, rotate: 0 }}
          initial={{ opacity: 0, rotate: -10 }}
          transition={{ duration: 0.3, delay: 0.1 }}
          whileHover={{ rotate: 10, scale: 1.1 }}
        >
          <DateIcon className='w-[15px] lg:w-[20px] text-[#7366D9] dark:text-white' />
        </motion.div>
        <motion.span
          animate={{ opacity: 1 }}
          initial={{ opacity: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          Date
        </motion.span>
      </motion.button>

      <AnimatePresence>
        {isModalOpen && (
          <Modal
            className='datePickerModal'
            classNames={{
              body: 'p-4',
              content: '!bg-container',
              header: '!bg-container !text-blackWhite',
            }}
            destroyOnClose={true}
            maskClosable={false}
            okText='Apply'
            onCancel={handleCancel}
            onOk={handleOk}
            open={isModalOpen}
            title={
              <div className='flex items-center gap-2'>
                <DateIcon className='w-[18px] text-primary dark:text-white' />
                <span className='text-blackWhite text-sm font-medium'>
                  Select Date Range
                </span>
              </div>
            }
            width={400}
          >
            <motion.div
              animate={{ opacity: 1, y: 0 }}
              className='flex flex-col gap-4 mt-2'
              exit={{ opacity: 0, y: -20 }}
              initial={{ opacity: 0, y: 20 }}
              transition={{ duration: 0.3 }}
            >
              <div className='space-y-2'>
                <label className='text-xs text-blackWhite font-medium'>
                  Start Date
                </label>
                <motion.div
                  animate={{ opacity: 1, x: 0 }}
                  className='w-full'
                  initial={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.3, delay: 0.1 }}
                  whileHover={{
                    scale: 1.02,
                    boxShadow: '0px 4px 15px rgba(0, 0, 0, 0.1)',
                  }}
                >
                  <DatePicker
                    className='hover:border-primary focus:border-primary w-full'
                    disabledDate={disabledStartDate}
                    format='DD-MM-YYYY'
                    inputReadOnly
                    onChange={onStartChange}
                    onOpenChange={handleStartOpenChange}
                    placeholder='Select start date'
                    {...(datePicker.startValue?.isValid() && {
                      value: datePicker?.startValue,
                    })}
                  />
                </motion.div>
              </div>

              <div className='space-y-2'>
                <label className='text-xs text-blackWhite font-medium'>
                  End Date
                </label>
                <motion.div
                  animate={{ opacity: 1, x: 0 }}
                  className='w-full'
                  initial={{ opacity: 0, x: 20 }}
                  transition={{ duration: 0.3, delay: 0.2 }}
                  whileHover={{
                    scale: 1.02,
                    boxShadow: '0px 4px 15px rgba(0, 0, 0, 0.1)',
                  }}
                >
                  <DatePicker
                    className='hover:border-primary focus:border-primary w-full'
                    disabledDate={disabledEndDate}
                    format='DD-MM-YYYY'
                    inputReadOnly
                    onChange={onEndChange}
                    onOpenChange={handleEndOpenChange}
                    open={datePicker?.endOpen}
                    placeholder='Select end date'
                    {...(datePicker.endValue?.isValid() && {
                      value: datePicker?.endValue,
                    })}
                  />
                </motion.div>
              </div>

              <div className='mt-2 text-xs text-gray-500 dark:text-gray-400'>
                <p>Select a date range to filter your results</p>
              </div>
            </motion.div>
          </Modal>
        )}
      </AnimatePresence>
    </>
  );
}

// const DateRangeCalendar = ({
//   startEndDate,
// }: {
//   startEndDate?: (start: string, end: string) => void;
// }) => {
//   const [state, setState] = useState({
//     startDate: new Date(),
//     endDate: new Date(),
//     key: 'selection',
//   });

//   const handleSelections = ({ selection }: any) => {
//     setState({
//       startDate: selection.startDate,
//       endDate: selection.endDate,
//       key: 'selection',
//     });

//     //serializing the date before dispatching into redux
//     const serializedStartDate = new Date(selection.startDate).toISOString();
//     const serializedEndDate = new Date(selection.endDate).toISOString();
//     if (startEndDate) {
//       startEndDate(serializedStartDate, serializedEndDate);
//     }
//   };

//   return (
//   );
// };

export default DateRangePickerCustom;
