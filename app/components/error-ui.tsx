'use client'; // Error components must be Client Components

import Image from 'next/image';
import CommonHeader from './headers/common-header';
import SmartLink from './common/smart-link';
import { LinkType } from '@/utils/link-utils';

export default function ErrorUI({ reset }: { reset: () => void }) {
  return (
    <>
      <CommonHeader headline='Server Error' showMenuBtn={false} />
      <section className='bg-container flex-center min-h-[calc(100vh-65px)] lg:min-h-[calc(100vh-104px)]'>
        <div className='flex flex-col items-center'>
          <div className='relative w-[300px] h-[300px] lg:w-[500px] lg:h-[400px]'>
            <Image
              alt='404 image'
              className='object-contain'
              fill
              sizes='(max-width:1024px) 100vw, 50vw'
              src={`/svg/error-429.svg`}
            />
          </div>
          <div className=' flex justify-center space-x-5 py-5'>
            <SmartLink
              className='w-[135px] h-[40px] mt-[20px] border-[1.5px] border-[#4D3EC1] rounded-[5px] flex-center text-[10px] font-semibold text-blackWhite'
              href={'/'}
              linkType={LinkType.INTERNAL}
            >
              Take me to Home
            </SmartLink>
            <button
              className='w-[135px] h-[40px] mt-[20px] border-[1.5px] border-[#4D3EC1] rounded-[5px] flex-center text-[10px] font-semibold text-blackWhite'
              onClick={
                // Attempt to recover by trying to re-render the segment
                () => reset()
              }
            >
              Try Again
            </button>
          </div>
        </div>
      </section>
    </>
  );
}
