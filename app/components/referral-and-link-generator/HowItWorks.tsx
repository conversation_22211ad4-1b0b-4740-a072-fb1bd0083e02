'use client';
import Image from 'next/image';
import React from 'react';

const HowItWorksMain = ({ type }: { type: 'referral' | 'link-generator' }) => {
  const howItWorksReferral: {
    badgeText: string;
    title: string;
    description: string;
    steps: {
      image: string;
      imageAlt: string;
      title: string;
      description: string;
    }[];
  } = {
    badgeText: 'Simple Process',
    title: 'How It Works',
    description:
      'Follow these simple steps to start earning rewards through our referral program',
    steps: [
      {
        image: '/temp/referrals/invite.png',
        imageAlt: 'Invite your friends',
        title: 'Invite your friends',
        description: 'Share Your Unique Referral Link.',
      },
      {
        image: '/temp/referrals/shop.png',
        imageAlt: 'Friend signs up & shops',
        title: 'Friend signs up & shops',
        description:
          'They Earn ₹50 Approved Cashback On Sign-up (Withdrawable After Earning ₹200 Total Cashback).',
      },
      {
        image: '/temp/referrals/earn.png',
        imageAlt: 'You earn rewards',
        title: 'You earn rewards',
        description:
          '₹25 Pending Reward Per Successful Referral Plus 10% Of Their Approved Cashback For Life! 🤑',
      },
    ],
  };

  const howItWorksLinkGenerator: {
    badgeText: string;
    title: string;
    description: string;
    steps: {
      image: string;
      imageAlt: string;
      title: string;
      description: string;
    }[];
  } = {
    badgeText: 'Simple Process',
    title: 'How It Works',
    description: 'Follow these simple steps to Generate Link affiliate program',
    steps: [
      {
        image: '/temp/referrals/invite.png',
        imageAlt: 'Paste Link',
        title: '1. Paste Link',
        description:
          'Share any product from supported stores like Amazon, Flipkart, Myntra, etc.',
      },
      {
        image: '/temp/referrals/shop.png',
        imageAlt: 'Generate Link',
        title: '2. Generate Link',
        description:
          'Your unique earning link is created instantly with our system.',
      },
      {
        image: '/temp/referrals/earn.png',
        imageAlt: 'Share & Earn',
        title: '3. Share & Earn',
        description:
          "Earn cashback when others shop via your link. It's that simple!",
      },
    ],
  };

  const howItWorks =
    type === 'referral' ? howItWorksReferral : howItWorksLinkGenerator;

  return (
    <HowItWorks
      badgeText={howItWorks.badgeText}
      description={howItWorks.description}
      steps={howItWorks.steps}
      title={howItWorks.title}
    />
  );
};

const HowItWorks = ({
  badgeText,
  title,
  description,
  steps,
}: {
  badgeText: string;
  title: string;
  description: string;
  steps: {
    image: string;
    imageAlt: string;
    title: string;
    description: string;
  }[];
}) => {
  return (
    <section className='w-full py-12 md:py-24 flex flex-col items-center gap-y-4 bg-[#EEEEEE] dark:bg-[#212327] rounded-lg shadow-lg px-4 md:px-8'>
      {/* How it works content goes here */}
      <div className='px-4 py-2 rounded-full text-sm text-primary bg-[#E6E2FF]'>
        {badgeText}
      </div>
      <h2 className='text-3xl md:text-4xl font-bold text-primary text-center'>
        {title}
      </h2>
      <p className='text-center max-w-xl text-sm font-light px-4'>
        {description}
      </p>
      <div className='grid grid-cols-1 md:grid-cols-3 gap-8 justify-center my-6 w-full max-w-5xl'>
        <div className='flex flex-col items-center gap-y-5 px-4'>
          <div className='flex flex-col items-center gap-y-5'>
            <Image
              alt={steps[0].imageAlt}
              className='w-20 h-20'
              height={100}
              src={steps[0].image}
              width={100}
            />
            <p className='text-center max-w-[150px] text-base font-medium'>
              {steps[0].title}
            </p>
          </div>
          <p className='text-center max-w-xs text-xs font-light'>
            {steps[0].description}
          </p>
        </div>
        <div className='flex flex-col items-center gap-y-5 px-4'>
          <div className='flex flex-col items-center gap-y-5'>
            <Image
              alt={steps[1].imageAlt}
              className='w-20 h-20'
              height={100}
              src={steps[1].image}
              width={100}
            />
            <p className='text-center max-w-[135px] text-base font-medium'>
              {steps[1].title}
            </p>
          </div>
          <p className='text-center max-w-xs text-xs font-extralight'>
            {steps[1].description}
          </p>
        </div>
        <div className='flex flex-col items-center gap-y-5 px-4'>
          <div className='flex flex-col items-center gap-y-5'>
            <Image
              alt={steps[2].imageAlt}
              className='w-20 h-20'
              height={100}
              src={steps[2].image}
              width={100}
            />
            <p className='text-center max-w-[135px] text-base font-medium'>
              {steps[2].title}
            </p>
          </div>
          <p className='text-center max-w-xs text-xs font-extralight'>
            {steps[2].description}
          </p>
        </div>
      </div>
    </section>
  );
};

export default HowItWorksMain;
