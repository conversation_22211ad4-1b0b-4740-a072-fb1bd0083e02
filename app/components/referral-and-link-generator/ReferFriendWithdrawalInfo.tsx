import Image from 'next/image';
import React from 'react';

const ReferFriendWithdrawalInfo = () => {
  return (
    <section className='w-full py-12 md:py-24 flex flex-col items-center gap-y-4 rounded-lg px-4 md:px-8'>
      {/* How it works content goes here */}
      <div className='px-4 py-2 rounded-full text-sm text-primary bg-white'>
        Get Your Money
      </div>
      <h2 className='text-3xl md:text-4xl font-bold text-primary text-center'>
        Withdrawal Information
      </h2>
      <p className='text-center max-w-xl text-sm font-light px-4'>
        Multiple ways to get your cashback rewards
      </p>
      <div className='bg-[#E6E2FF] dark:bg-[#7b66ff] rounded-lg shadow-xs p-6 md:p-8 w-full my-4 text-center'>
        <p className='text-base md:text-lg font-medium mb-2'>
          Minimum Withdrawal: ₹200
        </p>
        <p className='text-xs sm:text-sm max-w-xl mx-auto'>
          Once You've Earned At Least ₹200 In Approved Cashback, You Can
          Withdraw Your Earnings Using Any Of The Methods Below.
        </p>
      </div>
      <div className='grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 md:gap-6 w-full max-w-6xl'>
        <WithdrawalMethod
          description='Direct Transfer To Your Bank Account Via UPI Or Account Number.'
          icon='/temp/referrals/bank.png'
          title='Bank Transfer'
        />
        <WithdrawalMethod
          description='Convert your cashback into Amazon gift cards.'
          icon='/temp/referrals/gift.png'
          title='Amazon Gift Card'
        />
        <WithdrawalMethod
          description='Convert your cashback into Flipkart gift cards.'
          icon='/temp/referrals/cart.png'
          title='Flipkart Gift Card'
        />
        <WithdrawalMethod
          description='Transfer directly to your PayTM wallet.'
          icon='/temp/referrals/coin.png'
          title='PayTM Wallet'
        />
      </div>
    </section>
  );
};

export default ReferFriendWithdrawalInfo;

// This component provides information about the withdrawal methods available
const WithdrawalMethod = ({
  icon,
  title,
  description,
}: {
  icon: string;
  title: string;
  description: string;
}) => {
  return (
    <div className='bg-white dark:bg-[#212327] rounded-lg shadow p-4 md:p-6 py-8 md:py-10 flex flex-col items-center justify-center'>
      <Image
        alt='referral hero'
        className='w-8 md:w-10 h-8 md:h-10 object-scale-down'
        height={150}
        src={icon}
        width={150}
      />
      <h4 className='font-semibold mt-4 mb-2'>{title}</h4>
      <p className='text-xs text-center font-light'>{description}</p>
    </div>
  );
};
