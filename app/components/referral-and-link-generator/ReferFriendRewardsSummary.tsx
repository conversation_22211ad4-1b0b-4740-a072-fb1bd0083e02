import { Check } from 'lucide-react';
import Image from 'next/image';
import type React from 'react';

const ReferFriendRewardsSummary = () => {
  const referrerData = {
    image: '/temp/referrals/referrer.png',
    name: 'Referrer (You)',
    rewards: [
      <p key='reward-1'>
        <span className='font-semibold'>₹25 </span>
        <span>Pending Reward Per Successful Referral</span>
      </p>,
      <p key='reward-2'>
        <span className='font-semibold'>10% Lifetime Cashback </span>
        <span>from your referee's approved cashback</span>
      </p>,
      <p key='reward-3'>
        <span className='font-semibold'>Unlimited Referrals </span>
        <span>- invite as many friends as you want</span>
      </p>,
      <p key='reward-4'>
        <span className='font-semibold'>Top Spot On Leaderboard </span>
        <span>to win additional prizes</span>
      </p>,
    ],
  };

  const refereeData = {
    image: '/temp/referrals/referee.png',
    name: 'Referee (Friend)',
    rewards: [
      <p key='reward-1'>
        <span className='font-semibold'>₹50 </span>
        <span>Approved Cashback On Sign-Up</span>
      </p>,
      <p key='reward-2'>
        <span className='font-semibold'>
          Cashback Becomes Withdrawable After{' '}
        </span>
        <span>₹200 Total Cashback</span>
      </p>,
      <p key='reward-3'>
        <span className='font-semibold'>Must Sign Up Through Your </span>
        <span>Referral Link</span>
      </p>,
      <p key='reward-4'>
        <span className='font-semibold'>Access to all cashback </span>
        <span>offers and deals</span>
      </p>,
    ],
  };
  return (
    <section className='w-full py-12 md:py-24 flex flex-col items-center gap-y-4 rounded-lg px-4 md:px-8'>
      {/* How it works content goes here */}
      <div className='px-4 py-2 rounded-full text-sm text-primary bg-white'>
        Earn More
      </div>
      <h2 className='text-3xl md:text-4xl font-bold text-primary text-center'>
        Rewards Summary
      </h2>
      <p className='text-center max-w-xl text-sm font-light px-4'>
        See what you and your friends earn through our referral program
      </p>
      <div className='flex flex-col md:flex-row gap-8 justify-center w-full max-w-5xl'>
        <RewardItem
          image={referrerData.image}
          rewards={referrerData.rewards}
          title={referrerData.name}
        />
        <RewardItem
          image={refereeData.image}
          rewards={refereeData.rewards}
          title={refereeData.name}
        />
      </div>
    </section>
  );
};

export default ReferFriendRewardsSummary;

const RewardItem = ({
  image,
  title,
  rewards,
}: {
  image: string;
  title: string;
  rewards: React.ReactNode[];
}) => {
  return (
    <div className='p-4 md:p-6 min-w-[250px] w-full'>
      <Image
        alt='referrer'
        className='w-20 md:w-24 h-20 md:h-24 object-cover mx-auto'
        height={100}
        src={image}
        width={100}
      />
      <h3 className='font-semibold text-lg mt-6 md:mt-8 mb-3 md:mb-4 w-full text-center'>
        {title}
      </h3>
      <ul className='list-disc text-sm w-full space-y-2'>
        {rewards.map((reward, index) => (
          <li
            className='flex gap-2 items-start sm:items-center justify-start'
            key={`reward-${index + 1}`}
          >
            <Check className='w-5 h-5 text-white bg-primary rounded-full p-1 stroke-3 mt-0.5 sm:mt-0 flex-shrink-0' />
            <div className='flex-1'>{reward}</div>
          </li>
        ))}
      </ul>
    </div>
  );
};
