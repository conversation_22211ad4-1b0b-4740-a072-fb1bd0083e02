'use client';
import type React from 'react';
import type { TopLink } from './link-generator-data';

interface LinkGeneratorTableProps {
  links: TopLink[];
}

const LinkGeneratorTable: React.FC<LinkGeneratorTableProps> = ({
  links,
}: {
  links: TopLink[];
}) => {
  console.log('links', links);
  return (
    <div className='overflow-x-auto w-full max-w-6xl rounded-lg shadow-lg'>
      <table className='min-w-full bg-white dark:bg-[#212327]'>
        <thead className='hidden sm:table-header-group'>
          <tr className='text-xs border-b border-gray-200 dark:border-gray-700'>
            <th className='py-3 px-4 !font-medium bg-white dark:bg-[#18191d] text-center min-w-[120px]'>
              Product
            </th>
            <th className='py-3 px-4 !font-medium bg-white dark:bg-[#18191d] text-left w-fit min-w-[120px]'>
              Store
            </th>
            <th className='py-3 px-4 !font-medium bg-white dark:bg-[#18191d] min-w-[50px]'>
              Clicks
            </th>
            <th className='py-3 px-4 !font-medium bg-white dark:bg-[#18191d] text-center min-w-[120px]'>
              Conversations
            </th>
            <th className='py-3 px-4 !font-medium bg-white dark:bg-[#18191d] text-left w-fit min-w-[140px]'>
              Cashback Earned
            </th>
          </tr>
        </thead>
        <tbody className='text-sm'>
          {links.map((link) => (
            <tr
              className='block sm:table-row border-b border-gray-200 dark:border-gray-700 text-center'
              key={link.productName}
            >
              <td className='py-2 h-[55px] px-4 text-center border-b sm:border-b-0 block sm:table-cell before:content-["Rank:_"] before:font-bold sm:before:content-none bg-[#EEEEEE] dark:bg-[#212327]'>
                <span className='ml-0 sm:ml-0 text-center whitespace-nowrap'>
                  {link.productName}
                </span>
              </td>
              <td className='flex flex-row items-center justify-start py-2 h-[55px] px-4 text-left border-b sm:border-b-0 sm:table-cell before:content-["User:_"] before:font-bold sm:before:content-none bg-[#EEEEEE] dark:bg-[#212327]'>
                <span className='ml-0 sm:ml-0 whitespace-nowrap'>
                  {link.storeName}
                </span>
              </td>
              <td className='py-2 h-[55px] px-4 text-center border-b sm:border-b-0 block sm:table-cell before:content-["Total_Referrals:_"] before:font-bold sm:before:content-none bg-[#EEEEEE] dark:bg-[#212327]'>
                <span className='ml-0 sm:ml-0 whitespace-nowrap'>
                  {link.clicks}
                </span>
              </td>
              <td className='py-2 h-[55px] px-4 text-right border-b sm:border-b-0 block sm:table-cell before:content-["Cashback_Earned:_"] before:font-bold sm:before:content-none bg-[#EEEEEE] dark:bg-[#212327]'>
                <span className='ml-0 sm:ml-0 whitespace-nowrap'>
                  {link.conversations}
                </span>
              </td>
              <td className='py-2 h-[55px] px-4 text-right border-b sm:border-b-0 block sm:table-cell before:content-["Cashback_Earned:_"] before:font-bold sm:before:content-none bg-[#EEEEEE] dark:bg-[#212327]'>
                <span className='ml-0 sm:ml-0 whitespace-nowrap'>
                  ₹{link.cashbackEarned}
                </span>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default LinkGeneratorTable;

export const ApprovedStatus = () => {
  return (
    <div className='px-4 py-2 rounded-full text-sm text-black bg-[#7AE453] w-[105px] text-center m-auto'>
      Approved
    </div>
  );
};

export const PendingStatus = () => {
  return (
    <div className='px-4 py-2 rounded-full text-sm text-black bg-[#FFBF41] w-[105px] text-center m-auto'>
      Pending
    </div>
  );
};

export const RejectedStatus = () => {
  return (
    <div className='px-4 py-2 rounded-full text-sm text-black bg-red-500 w-[105px] text-center m-auto'>
      Rejected
    </div>
  );
};
