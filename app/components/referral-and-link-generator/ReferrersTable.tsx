'use client';
import type React from 'react';
import Image from 'next/image';
import { BronzeBadge, SilverBadge, GoldBadge } from './Badges';
import { GetReferralLeaderboardResponse } from '@/services/api/data-contracts';

interface ReferrersTableProps {
  referrers: GetReferralLeaderboardResponse[];
}

const ReferrersTable: React.FC<ReferrersTableProps> = ({ referrers }) => {
  return (
    <div className='overflow-x-auto w-full max-w-6xl rounded-lg shadow-lg'>
      <table className='min-w-full bg-white dark:bg-[#212327]'>
        <thead className='hidden sm:table-header-group'>
          <tr className='text-xs border-b border-gray-200 dark:border-gray-700'>
            <th className='py-3 px-4 !font-medium bg-white dark:bg-[#18191d] text-center min-w-[100px]'>
              Rank
            </th>
            <th className='py-3 px-4 !font-medium bg-white dark:bg-[#18191d] text-left w-fit min-w-[120px]'>
              User
            </th>
            <th className='py-3 px-4 !font-medium bg-white dark:bg-[#18191d] min-w-[120px]'>
              Total Referrals
            </th>
            <th className='py-3 px-4 !font-medium bg-white dark:bg-[#18191d] text-right min-w-[140px]'>
              Cashback Earned
            </th>
            <th className='py-3 px-4 !font-medium bg-white dark:bg-[#18191d] text-center min-w-[150px]'>
              Badge
            </th>
          </tr>
        </thead>
        <tbody className='text-sm'>
          {referrers.map((referrer, index) => (
            <tr
              className='block sm:table-row border-b border-gray-200 dark:border-gray-700 text-center'
              key={index}
            >
              <td className='py-2 h-[55px] px-4 text-center border-b sm:border-b-0 block sm:table-cell before:content-["Rank:_"] before:font-bold sm:before:content-none bg-[#EEEEEE] dark:bg-[#212327]'>
                <span className='ml-0 sm:ml-0 text-center whitespace-nowrap'>
                  {index + 1}
                </span>
              </td>
              <td className='flex flex-row items-center justify-start py-2 h-[55px] px-4 text-left border-b sm:border-b-0 sm:table-cell before:content-["User:_"] before:font-bold sm:before:content-none bg-[#EEEEEE] dark:bg-[#212327]'>
                <div className='flex flex-row items-center justify-start gap-x-2'>
                  <Image
                    alt='profile'
                    className='w-[30px] h-[30px] rounded-full'
                    height={30}
                    src={
                      `https://ui-avatars.com/api/?name=${referrer.name}&background=random&rounded=true&format=png` ||
                      '/temp/profile.png'
                    }
                    width={30}
                  />
                  <span className='ml-0 sm:ml-0 whitespace-nowrap'>
                    {referrer.name}
                  </span>
                </div>
              </td>
              <td className='py-2 h-[55px] px-4 text-center border-b sm:border-b-0 block sm:table-cell before:content-["Total_Referrals:_"] before:font-bold sm:before:content-none bg-[#EEEEEE] dark:bg-[#212327]'>
                <span className='ml-0 sm:ml-0 whitespace-nowrap'>
                  {referrer.referralCount}
                </span>
              </td>
              <td className='py-2 h-[55px] px-4 text-right border-b sm:border-b-0 block sm:table-cell before:content-["Cashback_Earned:_"] before:font-bold sm:before:content-none bg-[#EEEEEE] dark:bg-[#212327]'>
                <span className='ml-0 sm:ml-0 whitespace-nowrap'>
                  ₹{referrer.totalReferralCommission}
                </span>
              </td>
              <td className='py-2 h-[55px] px-4 border-b sm:border-b-0 block sm:table-cell before:content-["Badge:_"] before:font-bold sm:before:content-none bg-[#EEEEEE] dark:bg-[#212327] min-w-[20%]'>
                {index === 0 && <GoldBadge />}
                {index === 1 && 'silver' && <SilverBadge />}
                {index === 2 && 'bronze' && <BronzeBadge />}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default ReferrersTable;
