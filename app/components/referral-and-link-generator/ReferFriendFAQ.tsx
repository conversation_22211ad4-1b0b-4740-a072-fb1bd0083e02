'use client';
import React, { useState } from 'react';
import { referralFaqData } from './referral-faq-data';
import { generateFAQSchema } from '@/utils/schema';
import FAQAccordion from '../accordians/faq-accordian';
import SearchInput from '../atoms/search-input';
import { motion } from 'framer-motion';
import { linkGeneratorFaqData } from './link-generator-faq-data';

const ReferFriendFAQ = ({ type }: { type: 'referral' | 'link-generator' }) => {
  const [activeAccordionId, setActiveAccordionId] = useState<number | null>(
    null
  );
  const [searchValue, setSearchValue] = useState('');
  const [filteredFAQs, setFilteredFAQs] = useState(
    type === 'referral' ? referralFaqData : linkGeneratorFaqData
  );

  // Handle accordion toggle
  const handleToggle = (id: number) => {
    setActiveAccordionId(activeAccordionId === id ? null : id);
  };

  // Handle search input change
  const handleSearchChange = (value: string) => {
    setSearchValue(value);
    if (value.trim()) {
      const searchTerm = value.toLowerCase();
      const filtered = referralFaqData.filter(
        (faq) =>
          faq.question.toLowerCase().includes(searchTerm) ||
          faq.answer.toLowerCase().includes(searchTerm)
      );
      setFilteredFAQs(filtered);
    } else {
      setFilteredFAQs(referralFaqData);
    }
  };

  // Create FAQ schema for SEO
  const faqSchema = generateFAQSchema({
    mainEntity:
      type === 'referral'
        ? referralFaqData.map((faq) => ({
            '@type': 'Question' as const,
            name: faq.question,
            acceptedAnswer: {
              '@type': 'Answer' as const,
              text: faq.answer,
            },
          }))
        : linkGeneratorFaqData.map((faq) => ({
            '@type': 'Question' as const,
            name: faq.question,
            acceptedAnswer: {
              '@type': 'Answer' as const,
              text: faq.answer,
            },
          })),
  });

  return (
    <section className='w-full py-12 md:py-24 flex flex-col items-center gap-y-4 bg-[#EEEEEE] dark:bg-[#212327] rounded-lg shadow-lg px-4 md:px-8'>
      <script
        dangerouslySetInnerHTML={{ __html: faqSchema }}
        type='application/ld+json'
      />
      <h2 className='text-xl md:text-2xl lg:text-3xl font-bold mb-4 md:mb-8 text-[#574ABE] text-center'>
        Frequently Asked Questions
      </h2>

      {/* Search Bar */}
      <div className='w-full max-w-xl mb-4 md:mb-8 px-2 md:px-0'>
        <SearchInput
          onChange={handleSearchChange}
          onClose={() => {
            setSearchValue('');
            setFilteredFAQs(referralFaqData);
          }}
          placeholder='Search referral questions...'
          rootClass='w-full mx-auto'
          value={searchValue}
        />
      </div>

      {/* FAQ Content */}
      <div className='w-full max-w-2xl'>
        {filteredFAQs.length === 0 ? (
          <motion.div
            animate={{ opacity: 1 }}
            className='text-center py-4 md:py-8'
            initial={{ opacity: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h3 className='text-base md:text-lg font-medium text-gray-700 mb-2'>
              No results found
            </h3>
            <p className='text-sm md:text-base text-gray-500'>
              We couldn't find any FAQs matching your search. Try different
              keywords.
            </p>
          </motion.div>
        ) : (
          <div>
            {filteredFAQs.map((faq) => (
              <FAQAccordion
                activeId={activeAccordionId}
                answer={faq.answer}
                highlight={searchValue}
                id={faq.id}
                key={faq.id}
                onClick={handleToggle}
                question={faq.question}
              />
            ))}
          </div>
        )}
      </div>
    </section>
  );
};

export default ReferFriendFAQ;
