import clsx from 'clsx';
import React, { useEffect, useRef, useState } from 'react';

type TabsProps = {
  value: number;
  onChange: (index: number) => void;
  children: React.ReactNode; // Accepts React elements as children
  className?: string;
  sliderClass?: string;
};

const Tabs: React.FC<TabsProps> = ({
  value,
  onChange,
  children,
  sliderClass,
  className,
}) => {
  const [underlineStyle, setUnderlineStyle] = useState({});
  const tabsRef = useRef<(HTMLButtonElement | null)[]>([]); // Correct typing for HTML elements

  useEffect(() => {
    const currentTab = tabsRef.current[value];
    console.log(currentTab);
    if (currentTab) {
      setUnderlineStyle({
        width: currentTab.offsetWidth,
        left: currentTab.offsetLeft,
      });
    }
  }, [value]);

  return (
    <div
      aria-label='tabs'
      className={clsx(
        'relative flex items-center bg-transparent w-max md:mx-auto pb-1.5',
        className
      )}
    >
      {React.Children.map(children, (child, index) => {
        if (!React.isValidElement(child)) {
          return null;
        }

        // Passing props like ref, isActive, and onClick to each Tab child
        return React.cloneElement(child as React.ReactElement<any>, {
          ref: (el: HTMLButtonElement | null) => (tabsRef.current[index] = el),
          isActive: index === value,
          onClick: () => onChange(index),
        });
      })}
      <span
        className={clsx(
          'absolute inline-block bottom-0 h-[5px] transition-all duration-300 shrink-0 rounded-t-[6px] bg-primary',
          sliderClass
        )}
        style={underlineStyle}
      />
    </div>
  );
};

export default Tabs;
