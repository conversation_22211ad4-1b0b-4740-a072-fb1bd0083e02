import clsx from 'clsx';
import React from 'react';

type TabProps = {
  onClick?: () => void;
  isActive?: boolean;
  children: string;
  className?: string;
};

const Tab = React.forwardRef<HTMLButtonElement, TabProps>(
  ({ isActive, onClick, children, className }, ref) => {
    return (
      <span
        className={clsx(
          'px-2 py-2 text-sm font-medium transition-colors whitespace-nowrap duration-300 focus:outline-none border-none focus:ring-0 bg-transparent',
          isActive
            ? 'text-primary'
            : 'text-gray-500 dark:text-white hover:text-primary dark:hover:text-primary',
          className
        )}
        onClick={onClick}
        ref={ref}
        role='button'
      >
        {children}
      </span>
    );
  }
);

export default Tab;
