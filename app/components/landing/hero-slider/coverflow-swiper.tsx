'use client';
import type React from 'react';
import { Swiper } from 'swiper/react';
import type { Swiper as SwiperType } from 'swiper';
import 'swiper/css';
import 'swiper/css/effect-coverflow';
import 'swiper/css/pagination';
import { EffectCoverflow, Pagination } from 'swiper/modules';
import clsx from 'clsx';
import { motion } from 'framer-motion';

const CoverflowSwiper = ({
  swiperName,
  children,
  className,
  onSwiper,
}: {
  swiperName: string;
  children: React.ReactNode;
  className?: string;
  onSwiper?: (swiper: SwiperType) => void;
}) => {
  return (
    <motion.div
      animate={{ opacity: 1, y: 0 }}
      className={clsx(className, 'coverflowSwiperWrapper relative')}
      initial={{ opacity: 0, y: 20 }}
      transition={{ duration: 0.6, ease: 'easeOut' }}
    >
      <Swiper
        allowTouchMove={true}
        centeredSlides={true}
        className={swiperName}
        coverflowEffect={{
          rotate: 0,
          stretch: 0,
          depth: 100,
          modifier: 2,
          slideShadows: false,
        }}
        effect={'coverflow'}
        grabCursor={true}
        loop={true}
        modules={[EffectCoverflow, Pagination]}
        onSwiper={onSwiper}
        pagination={{ clickable: true, enabled: false }}
        simulateTouch={true}
        slidesPerView={'auto'}
        touchAngle={45}
        touchRatio={1}
      >
        {children}
      </Swiper>
    </motion.div>
  );
};

export default CoverflowSwiper;
