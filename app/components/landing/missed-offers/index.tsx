'use client';
import React, { useRef } from 'react';
import Pattern2 from '../../svg/patterns/pattern2';
import Image from 'next/image';
import soldOutImg from '@/public/img/sold-out.png';
import sadBoyImg from '@/public/img/sad-boy.png';
import MissedOfferCard from '../../cards/missed-offer-card';
import Pattern5 from '../../svg/patterns/pattern5';
import { LeftRoundButton, RightRoundButton } from '../../atoms/rounded-buttons';
import { sideScroll } from '@/utils/helpers';
import { ContextMissedOfferType } from '../../../../services/api/data-contracts';
import { PromiseStatus } from '@/types/global-types';
import { useOverflowing } from '@/utils/custom-hooks';

const Index = ({
  missedOffers,
  promiseStatus,
}: {
  missedOffers: ContextMissedOfferType[];
  promiseStatus: PromiseStatus;
}) => {
  const containerRef = useRef<HTMLDivElement | null>(null);

  const isOverflowing: boolean = useOverflowing(containerRef);

  if (promiseStatus === 'rejected') {
    return;
  }
  return (
    <section
      className='missedOffersWrapper lg:bg-container mt-[16px] w-full relative pt-[12px] pb-[30px] 
    lg:flex lg:w-auto lg:mx-[8px] lg:py-0 lg:shadow lg:border-[0.5px] border-white dark:lg:border-none'
    >
      <div className='relative bg-transparent lg:bg-[#EAEAEA] lg:dark:bg-[#1f2022] lg:w-[162px] flex justify-center shrink-0'>
        <Pattern2 className='text-[#8E69DB] shrink-0 absolute top-[15px] left-[13px] lg:hidden' />
        <Pattern5 className='text-[#D7D7D7] dark:text-[#3B3D45] shrink-0 absolute hidden lg:block top-[15px] left-[13px]' />
        <div className='flex items-center justify-center lg:flex-col text-center'>
          <Image
            alt='shop image'
            className='w-[40px] h-[40px] lg:hidden'
            quality={100}
            src={soldOutImg}
          />

          <Image
            alt='sad-boy'
            className='h-[133px] w-[54px] mx-[20px] shrink-0 hidden lg:block relative z-[2]'
            height={133}
            src={sadBoyImg}
            width={54}
          />
          <h3 className='text-sm md:text-lg lg:text-sm lg:font-[400] lg:text-heading font-medium font-pat ml-[11px] lg:ml-0 text-[#E7E9EB] capitalize lg:w-[64px]'>
            Offers you have missed!
          </h3>
        </div>
      </div>
      {isOverflowing && (
        <LeftRoundButton
          classCont='mt-[0px] ml-[12px]'
          onClick={() => sideScroll(containerRef.current, 10, 400, -10)}
        />
      )}
      <div className='relative flex mt-[16px] lg:mt-0 w-full lg:w-[calc(100%-60px)] overflow-auto'>
        <Image
          alt='sad-boy'
          className='h-[133px] w-[54px] mx-[20px] shrink-0 lg:hidden'
          height={133}
          src={sadBoyImg}
          width={54}
        />
        <div
          className='flex gap-x-[8px] lg:gap-x-[24px] lg:py-[40px] pb-[5px] pr-[10px] lg:px-[8px] lg:mx-[30px] customScrollbar overflow-auto'
          ref={containerRef}
        >
          {missedOffers.map((item) => (
            <MissedOfferCard
              currentAmount={item.currentAmount}
              key={item.uid}
              offerTitle={item.offerTitle}
              productImage={item.productImage}
              storeLogoUrl={item.storeLogoUrl}
              storeName={item.storeName}
              uid={item.uid}
            />
          ))}
        </div>

        {isOverflowing && (
          <RightRoundButton
            classCont='mt-[0px] ml-[6px]'
            onClick={() => sideScroll(containerRef.current, 10, 400, 10)}
          />
        )}
      </div>
    </section>
  );
};

export default Index;
