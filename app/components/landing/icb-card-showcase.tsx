import Image from 'next/image';
import React from 'react';
import CommonContainer from '../common-container';
import Pattern3 from '../svg/patterns/pattern3';

const IcbCardShowCase = () => {
  return (
    <CommonContainer className='icbCardSection !h-[169px] md:!min-h-[260px] lg:hidden'>
      <Pattern3 className='text-[#E2E2E2] dark:text-[#3B3D45] pl-[14px] shrink-0 mt-[15px]' />
      <Image
        alt='icb card showcase'
        className='absolute bottom-[13px] left-[35px] md:w-[338px] md:max-h-[206px]'
        height={100}
        src={'/img/card-and-hand.png'}
        width={200}
      />
      <h3 className='text-sm md:text-lg text-heading font-medium font-pat ml-[11px] absolute top-[18px] left-[50%] translate-x-[-50%]'>
        ICB Card
      </h3>
      <div className='absolute top-[18px] right-[20px] flex flex-col'>
        <span className='font-nexa font-sm font-black text-[#1A1A1F40] dark:text-[#CCCBD640]'>
          10,000+
        </span>
        <span className='text-[10px] font-[300] text-[#1A1A1F40] dark:text-[#CCCBD640]'>
          Active Users
        </span>
      </div>
    </CommonContainer>
  );
};

export default IcbCardShowCase;
