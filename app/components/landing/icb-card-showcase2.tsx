import Image from 'next/image';
import React from 'react';
import IcbCardShowCase2 from '@/public/img/icb-card-showcase2.png';
import Pattern4 from '../svg/patterns/pattern4';
import SmartLink from '../common/smart-link';
import { LinkType } from '@/utils/link-utils';

const bgStyle = {
  background:
    'linear-gradient(0deg, #A991FF 0%, #A991FF 100%), linear-gradient(0deg, #C4C4C4 0%, #C4C4C4 100%), linear-gradient(0deg, #DEDEDE 0%, #DEDEDE 100%), #B7B7B7',
};
const ICBCardShowcase2 = () => {
  return (
    <SmartLink
      href='https://card.indiancashback.com'
      linkType={LinkType.EXTERNAL}
      target='_blank'
    >
      <div
        className='h-[92px] flex mt-[22px] px-[16px] pt-[8px] lg:hidden'
        style={bgStyle}
      >
        <div className='flex flex-col justify-center gap-[4px]'>
          <h2 className='text-[10px] md:text-[16px] font-[700] text-white'>
            ICB Debit Card
          </h2>
          <p className='text-white md:text-[12px] text-[8px] font-[300]'>
            Get maximum offers on your shopping and payments
          </p>
        </div>
        <div className='flex shrink-0 items-end mr-[10px] ml-auto'>
          <Image alt={'icb card'} src={IcbCardShowCase2} width={119} />
        </div>
        <div className=''>
          <Pattern4 className='text-[#E2E2E2] dark:text-[#3B3D45]' />

          <button className='w-[71px] h-[20px] bg-white text-[8px] font-medium rounded-[2px] uppercase flex items-center justify-center mt-[7px] text-[#353309F]'>
            Apply Now
          </button>
        </div>
      </div>
    </SmartLink>
  );
};

export default ICBCardShowcase2;
