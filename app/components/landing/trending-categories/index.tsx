'use client';
import React, { useRef } from 'react';
import Pattern2 from '../../svg/patterns/pattern2';
import Image from 'next/image';
import medalImg from '@/public/img/medal.png';
import trendingCategoriesBoy from '@/public/img/trending-categories-boy.png';
import TrendingCategoryCard from '../../cards/trending-category-card';
import Pattern5 from '../../svg/patterns/pattern5';
import { LeftRoundButton, RightRoundButton } from '../../atoms/rounded-buttons';
import { sideScroll } from '@/utils/helpers';
import type { PromiseStatus } from '@/types/global-types';
import type { CategoryResponse } from '@/services/api/data-contracts';
import { useOverflowing } from '@/utils/custom-hooks';

const bgStyle = {
  background: 'linear-gradient(90deg, #FFC554 -3.85%, #DDA029 107.18%)',
};
const Index = ({
  trendingCategories,
  promiseStatus,
}: {
  trendingCategories: Array<CategoryResponse>;
  promiseStatus: PromiseStatus;
}) => {
  const containerRef = useRef<HTMLDivElement | null>(null);
  const isOverflowing: boolean = useOverflowing(containerRef);

  if (
    promiseStatus === 'rejected' ||
    !trendingCategories ||
    trendingCategories.length === 0
  ) {
    return null;
  }
  return (
    <section
      className='trendingCategories mt-[16px] lg:flex relative pt-[12px] pb-[30px] lg:py-0 mx-[8px] lg:w-auto shadow lg:border-[0.5px] border-white dark:lg:border-none'
      style={bgStyle}
    >
      <div className='relative bg-transparent lg:bg-[#EAEAEA] lg:dark:bg-[#1f2022] lg:w-[162px] flex justify-center shrink-0 lg:h-auto'>
        <Pattern2 className='text-[#8E69DB] shrink-0 absolute top-[15px] left-[13px] lg:hidden' />
        <Pattern5 className='text-[#D7D7D7] dark:text-[#3B3D45] shrink-0 absolute hidden lg:block top-[15px] left-[13px]' />
        <div className='flex items-center justify-center lg:flex-col lg:w-min text-center'>
          <Image
            alt='medal image'
            className='w-[40px] h-[40px] lg:w-[80px] lg:h-[80px] lg:hidden'
            quality={100}
            src={medalImg}
          />
          <Image
            alt='trending-categories-boy'
            className='h-[133px] w-[65px] mx-[20px] shrink-0 hidden lg:block relative z-[2]'
            height={133}
            src={trendingCategoriesBoy}
            width={54}
          />
          <h3 className='text-sm md:text-lg lg:text-sm text-black lg:text-blackWhite lg:font-[400] font-medium font-pat ml-[11px] lg:ml-0'>
            Trending Categories
          </h3>
        </div>
      </div>

      {isOverflowing && (
        <LeftRoundButton
          classCont='mt-[-20px] ml-[12px]'
          onClick={() => sideScroll(containerRef.current, 10, 400, -10)}
        />
      )}

      <div className='relative flex mt-[16px] lg:mt-0 items-center lg:grow lg:overflow-hidden'>
        <Image
          alt='trending-categories-boy'
          className='h-[133px] w-[65px] mx-[20px] shrink-0 lg:hidden'
          height={133}
          src={trendingCategoriesBoy}
          width={54}
        />
        <div
          className='flex gap-x-[8px] lg:gap-x-[20px] overflow-auto h-fit pr-[10px] w-full lg:w-[calc(100%-60px)] customScrollbar lg:py-[40px] lg:mx-[30px]'
          ref={containerRef}
        >
          {trendingCategories.map((item) => (
            <TrendingCategoryCard
              iconUrl={item.iconUrl}
              id={item.uid.toString()}
              key={item.id}
              title={item.name}
            />
          ))}
        </div>
      </div>
      {isOverflowing && (
        <RightRoundButton
          classCont='mt-[-20px] ml-[6px]'
          onClick={() => sideScroll(containerRef.current, 10, 400, 10)}
        />
      )}
    </section>
  );
};

export default Index;
