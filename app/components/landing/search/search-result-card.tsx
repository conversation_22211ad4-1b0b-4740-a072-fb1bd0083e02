import Image from 'next/image';
import React, { useState } from 'react';
import CashbackSVG from '../../svg/cashback';
import clsx from 'clsx';
// import SaveSVG from '../../svg/save';
// import TrackSVG from '../../svg/track';

const SearchResultCard = ({
  title,
  productPrice,
  cashback,
  // isSaved,
  // onClickSave,
  // onClickTrack,
  // isTrackable,
  newUsers,
  oldUsers,
  imgSrc,
  onClickCard,
}: {
  title: string;
  cashback?: string;
  productPrice?: string;
  // isSaved?: boolean;
  // onClickSave?: () => void;
  // onClickTrack?: () => void;
  // isTrackable?: boolean;
  imgSrc: string;
  newUsers?: string;
  oldUsers?: string;
  onClickCard?: () => void;
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      onClickCard?.();
    }
  };

  return (
    <div
      className={clsx(
        'flex justify-between items-center p-3 rounded-lg transition-all duration-300',
        isHovered
          ? 'bg-gray-100 dark:bg-gray-800 shadow-md scale-[1.01]'
          : 'hover:bg-gray-50 dark:hover:bg-gray-800/60'
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div
        aria-label={`View details for ${title}`}
        className='flex items-center cursor-pointer w-full'
        onClick={onClickCard}
        onKeyDown={handleKeyDown}
        role='button'
        tabIndex={0}
      >
        <div
          className={clsx(
            'relative w-[65px] h-[65px] rounded-md overflow-hidden transition-transform duration-300 bg-gray-100 dark:bg-gray-700 flex-shrink-0',
            isHovered && 'shadow-md'
          )}
        >
          {!imageLoaded && !imageError && (
            <div className='absolute inset-0 animate-pulse bg-gray-200 dark:bg-gray-700' />
          )}

          {!imageError ? (
            <Image
              alt={title}
              className={clsx(
                'object-contain transition-opacity duration-300',
                !imageLoaded && 'opacity-0',
                imageLoaded && 'opacity-100'
              )}
              fill
              onError={() => setImageError(true)}
              onLoad={() => setImageLoaded(true)}
              src={imgSrc}
            />
          ) : (
            <div className='flex h-full w-full items-center justify-center bg-gray-200 dark:bg-gray-700 text-gray-500 text-xs'>
              No image
            </div>
          )}
        </div>

        <div className='ml-[12px] flex flex-col flex-grow'>
          <span
            className={clsx(
              'text-heading text-xs font-medium line-clamp-2 transition-colors duration-200',
              isHovered && 'text-primary dark:text-primary'
            )}
          >
            {title}
          </span>

          {productPrice && (
            <span className='text-heading text-[11px] lg:text-xs font-bold font-nexa mt-[7px] leading-none'>
              {productPrice}
            </span>
          )}

          {cashback && (
            <div
              className={clsx(
                'flex items-center mt-[6px] transition-transform duration-300',
                isHovered && 'translate-x-0.5'
              )}
            >
              <CashbackSVG className='text-primary dark:text-[#988BFC] w-[12px] h-[12px]' />
              <span className='text-primary dark:text-[#988BFC] font-black text-[10px] pt-[2px] font-nexa ml-[5px] leading-none'>
                {cashback}
              </span>
            </div>
          )}

          {newUsers && oldUsers && (
            <div className='flex flex-wrap items-center gap-2 mt-[6px]'>
              <div className='flex items-center'>
                <span className='text-[9px] lg:text-[11px] font-medium text-gray-700 dark:text-gray-300'>
                  New User:
                </span>
                <span className='ml-1 font-nexa text-[9px] lg:text-[11px] font-bold text-primary'>
                  {newUsers}
                </span>
              </div>

              <div className='flex items-center'>
                <span className='text-[9px] lg:text-[11px] font-medium text-gray-700 dark:text-gray-300'>
                  Old User:
                </span>
                <span className='ml-1 font-nexa text-[9px] lg:text-[11px] font-bold text-black dark:text-white'>
                  {oldUsers}
                </span>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* <div className='flex items-center'>
        <SaveSVG
          className='text-primary cursor-pointer'
          fill={isSaved ? 'bg-primary' : 'none'}
          onClick={onClickSave}
        />
        {isTrackable && (
          <div
            className='w-[60px] h-[26px] flex items-center justify-center ml-[15px] border-[1.2px] border-[#8374FC] shadown rounded-[5px] cursor-pointer'
            onClick={onClickTrack}
          >
            <TrackSVG className='text-primary' />
            <span className='text-primary dark:text-white font-semibold ml-[5px] text-[8px]'>
              TRACK
            </span>
          </div>
        )}
      </div> */}
    </div>
  );
};

export default SearchResultCard;
