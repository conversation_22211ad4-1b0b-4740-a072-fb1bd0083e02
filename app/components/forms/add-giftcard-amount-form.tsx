import React from 'react';
import SelectGiftcardAmount from '../misc/select-giftcard-amount-pills';
import { GiftCardAmountsList } from '@/types/global-types';

const AddGiftcardAmountForm = ({
  customAmount,
  cardsList,
  showCustomAmount,
  totalAmount,
  addCustomAmount,
  setCustomAmount,
  addGiftCardAmount,
  updateGiftCardQuantity,
}: {
  customAmount: number;
  showCustomAmount: boolean;
  totalAmount: number;
  cardsList: Array<GiftCardAmountsList>;
  addCustomAmount: () => void;
  setCustomAmount: (value: number) => void;
  addGiftCardAmount: (value: GiftCardAmountsList) => void;
  updateGiftCardQuantity: (
    value: GiftCardAmountsList,
    action: 'add' | 'subtract'
  ) => void;
}) => {
  return (
    <div className='bg-[#EEE] dark:bg-[#35383E] rounded-[10px] overflow-auto relative pb-[68px] mx-[6px] lg:mx-0'>
      <SelectGiftcardAmount
        addCustomAmount={addCustomAmount}
        addGiftCardAmount={addGiftCardAmount}
        cardsList={cardsList}
        rootClassName='lg:hidden'
      />
      <h4 className='ml-[16px] mt-[32px] text-[10px] lg:text-sm lg:text-center text-blackWhite font-normal font-pat'>
        Add Count
      </h4>
      <div className='flex justify-center items-start flex-col gap-y-[14px] px-[16px] mx-auto mt-[14px] lg:mt-[36px] w-full lg:w-max'>
        {cardsList.map((item, index) => {
          return item && item.isSelected && item.quantity ? (
            <div
              className='flex items-center justify-center gap-x-[11px]'
              key={index}
            >
              <div className='w-[139px] lg:min-w-[160px]  max-w-[200px] h-[35px] lg:h-[42px] shrink bg-[#E2E2E2] dark:bg-[#18191D] flex-center rounded-[5px]'>
                <span className='text-blackWhite font-nexa text-[10px] lg:text-sm font-[800] mt-[5px]'>
                  ₹ {item.amount}
                </span>
              </div>
              <div
                className='flex-center shrink-0 rounded-[5px] w-[35px] h-[35px] lg:w-[48px] lg:h-[42px] bg-[#CCC5FF] dark:bg-[#3E424C] text-sm lg:text-[23px] font-[700] shadow cursor-pointer'
                onClick={() => updateGiftCardQuantity(item, 'subtract')}
              >
                {/* <DeleteSVG className='text-black dark:text-white lg:w-[17px] lg:h-[19px]' /> */}
                -
              </div>

              <div className='text-xs lg:text-sm font-[800]  flex-center shrink-0 rounded-[5px] w-[35px] h-[35px] lg:w-[48px] lg:h-[42px] bg-white dark:bg-[#18191D] border-[1px] border-primary'>
                {item.quantity}
              </div>

              <div
                className='flex-center shrink-0 rounded-[5px] w-[35px] h-[35px] lg:w-[48px] lg:h-[42px] bg-[#CCC5FF] dark:bg-[#3E424C] text-sm lg:text-[23px] font-[700] shadow cursor-pointer'
                onClick={() => updateGiftCardQuantity(item, 'add')}
              >
                +
              </div>

              <span className='sm:ml-[10px] lg:ml-[30px] text-[8px] lg:text-[14px] font-[800] font-nexa whitespace-nowrap'>
                ₹ {item.amount * item.quantity}
              </span>
            </div>
          ) : (
            <></>
          );
        })}
        {/* <div className='flex items-center justify-center gap-x-[11px]'>
          <div className='w-[139px] lg:min-w-[160px] max-w-[200px] h-[35px] lg:h-[42px] shrink bg-[#E2E2E2] dark:bg-[#18191D] flex-center rounded-[5px]'>
            <span className='text-blackWhite font-nexa text-[10px] lg:text-sm font-[800] mt-[5px]'>
              ₹ 1000
            </span>
          </div>
          <div className='flex-center shrink-0 rounded-[5px] w-[35px] h-[35px] lg:w-[48px] lg:h-[42px] bg-[#CCC5FF] dark:bg-[#3E424C] shadow cursor-pointer'>
            <DeleteSVG className='text-black dark:text-white lg:w-[17px] lg:h-[19px]' />
          </div>

          <div className='text-xs lg:text-sm font-[800]  flex-center shrink-0 rounded-[5px] w-[35px] h-[35px] lg:w-[48px] lg:h-[42px] bg-white dark:bg-[#18191D] border-[1px] border-primary'>
            2
          </div>

          <div className='flex-center shrink-0 rounded-[5px] w-[35px] h-[35px] lg:w-[48px] lg:h-[42px] bg-[#CCC5FF] dark:bg-[#3E424C] text-sm lg:text-[23px] font-[700] shadow cursor-pointer'>
            +
          </div>

          <span className='sm:ml-[10px] lg:ml-[30px] text-[8px] lg:text-[14px] font-[800] font-nexa whitespace-nowrap'>
            ₹ 2000
          </span>
        </div> */}
        {showCustomAmount && (
          <div className='h-[35px] w-[139px] lg:min-w-[160px] max-w-[200px] lg:h-[42px] rounded-[5px] dark:bg-[#18191D] border-[#CCC5FF] dark:border-[0] text-center relative border-[1px] border-primary'>
            <input
              className='absolute inset-0 bg-transparent outline-none text-center text-blackWhite text-[10px] lg:text-xs font-[300] placeholder:text-[8px]'
              onChange={(value) => {
                setCustomAmount(value.target.value as unknown as number);
              }}
              placeholder='Add a Custom Amount'
              type='number'
              value={customAmount}
            />
          </div>
        )}
      </div>
      <div className='bg-white dark:bg-[#2d2e32] h-[43px] lg:h-[55px] flex justify-end items-center absolute left-0 bottom-0 right-0'>
        <div>
          <span className='text-[10px] lg:text-[14px] text-[#515151] lg:text-white font-normal'>
            Total
          </span>
          <span className='text-xs lg:text-base font-[800] font-nexa ml-[30px] mr-[60px]'>
            ₹ {totalAmount}
          </span>
        </div>
      </div>
    </div>
  );
};

export default AddGiftcardAmountForm;
