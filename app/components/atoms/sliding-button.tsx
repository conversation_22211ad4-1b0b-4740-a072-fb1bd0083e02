'use client';

import clsx from 'clsx';
import React, { useEffect } from 'react';

const SlidingButton = ({
  buttonDetails,
  uniqueId,
  onChange,
  defaultSelectedBtn,
  rootClassName,
}: {
  buttonDetails: Array<{
    title: string;
    value: string;
  }>;
  uniqueId: string;
  onChange: (e: any) => void;
  defaultSelectedBtn: number;
  rootClassName?: string;
}) => {
  useEffect(() => {
    const ele = document.getElementById(
      `radio${defaultSelectedBtn}-${uniqueId}`
    );
    if (ele && ele instanceof HTMLInputElement) {
      ele.checked = true;
    }
  }, [defaultSelectedBtn, uniqueId]);
  return (
    <div className={clsx(rootClassName, 'slidingBtnContainer')}>
      <div className='slidingBtnTabs dark:bg-[#3E424C]'>
        {buttonDetails.map((item, index) => (
          <React.Fragment key={index}>
            <input
              id={`radio${index + 1}-${uniqueId}`}
              name={uniqueId}
              onChange={onChange}
              type='radio'
              value={item.value}
            />
            <label
              className='slidingBtnTab'
              htmlFor={`radio${index + 1}-${uniqueId}`}
            >
              {item.title}
            </label>
          </React.Fragment>
        ))}
        <span className='glider' />
      </div>
    </div>
  );
};

export default SlidingButton;
