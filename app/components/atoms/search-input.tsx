import clsx from 'clsx';
import CrossSVG from '../svg/cross';
import SearchSVG from '../svg/search';
import { FocusEvent } from 'react';

const SearchInput = ({
  value,
  onChange,
  rootClass,
  onClose,
  placeholder,
}: {
  value: string;
  onChange: (value: string) => void;
  onClose?: () => void;
  rootClass?: string;
  placeholder?: string;
}) => {
  return (
    <div
      className={clsx(
        rootClass,
        'bg-white dark:bg-[#18191D] rounded-[5px] h-[32px] lg:h-[44px] w-full lg:w-[358px] shadow-md relative pl-[20px] pr-[35px]'
      )}
      style={{ boxShadow: '0px 4px 16px 0px rgba(0, 0, 0, 0.04)' }}
    >
      <input
        className='h-full w-full text-blackWhite bg-transparent text-[9px] lg:text-xs font-medium outline-none rounded-[5px]'
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder || 'Search...'}
        type='text'
        value={value}
      />
      <div className='h-full flex-center w-[30px] pr-[5px] shrink-0 cursor-pointer absolute right-0 top-0'>
        {value ? (
          <CrossSVG
            className='w-[10px] lg:w-[12px] text-primary dark:text-white'
            onClick={onClose}
          />
        ) : (
          <SearchSVG className='w-[14px] lg:w-[18px] text-primary dark:text-white' />
        )}
      </div>
    </div>
  );
};

export const SearchCategoriesMobile = ({
  value,
  onChange,
  selectedCategory,
  OnClickCross,
  onBlur,
}: {
  value: string;
  onChange: (value: string) => void;
  selectedCategory: string;
  OnClickCross?: () => void;
  onBlur?: (event?: FocusEvent<HTMLInputElement>) => void;
}) => {
  return (
    <div
      className='bg-white dark:bg-[#18191D] rounded-[5px] h-[35px] w-full flex lg:w-[358px] shadow-md relative pr-[35px] overflow-hidden mt-[20px]'
      style={{ boxShadow: '0px 2px 6px 0px rgba(0, 0, 0, 0.05)' }}
    >
      <div className='h-full px-[4px] bg-primary max-w-[100px] shrink-0 flex-center pr-[5px]'>
        <span className='truncate text-white text-[10px]'>
          {selectedCategory}
        </span>
      </div>
      <input
        className='h-full w-full text-blackWhite pl-[10px] bg-transparent text-[9px] lg:text-xs font-medium outline-none rounded-[5px]'
        onBlur={(e) => onBlur && onBlur(e)}
        onChange={(e) => onChange(e.target.value)}
        placeholder={`Search inside ${selectedCategory}...`}
        type='text'
        value={value}
      />
      <div className='h-full flex-center w-[30px] pr-[5px] shrink-0 absolute right-0 top-0'>
        <CrossSVG
          className='w-[10px] text-primary dark:text-white'
          onClick={OnClickCross}
        />
      </div>
    </div>
  );
};

export default SearchInput;
