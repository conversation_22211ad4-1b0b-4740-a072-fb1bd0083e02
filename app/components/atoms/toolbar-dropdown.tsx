import { Dropdown, type MenuProps } from 'antd';

function ToolbarDropdown({
  name,
  items,
  onClick,
  children,
  className,
}: {
  name: string;
  items: MenuProps['items'];
  onClick: MenuProps['onClick'];
  children?: React.ReactNode;
  className?: string;
}) {
  return (
    <Dropdown className={className} menu={{ items, onClick }}>
      <button
        className='relative flex bg-white dark:bg-[#3D4049] h-[40px] w-[65px] lg:w-[80px] rounded-[5px] text-[12px] lg:text-[12px] text-[#1C132E] dark:text-white font-medium lg:font-semibold justify-evenly items-center lg:dark:bg-[#515662]'
        style={{ boxShadow: '0px 4px 4px 0px rgba(0, 0, 0, 0.07)' }}
        type='button'
      >
        {children}
        <span>{name}</span>
      </button>
    </Dropdown>
  );
}

export default ToolbarDropdown;
