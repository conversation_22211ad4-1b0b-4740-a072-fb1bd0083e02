import React from 'react';

const AboutHighlightCard = ({
  icon,
  highlightedText,
  caption,
}: {
  icon: React.ReactNode;
  highlightedText: string;
  caption: string;
}) => {
  return (
    <div className='w-[89px] min-h-[62px] lg:w-[197px] lg:min-h-[129px] py-[10px] bg-[#FFDC97] rounded-[5px] flex-center flex-col'>
      {icon}
      <span className='text-[10px] lg:text-[22px] text-black  font-black font-nexa mt-[7px] lg:mt-[19px]'>
        {highlightedText}
      </span>
      <span className='text-[8px] lg:text-xs text-black lg:mt-[2px]'>
        {caption}
      </span>
    </div>
  );
};

export default AboutHighlightCard;
