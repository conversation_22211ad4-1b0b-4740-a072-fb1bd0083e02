'use client';
import clsx from 'clsx';
import type React from 'react';

const ThemeButton = ({
  text,
  icon,
  onClick,
  className,
  isDisabled,
}: {
  text: string;
  icon?: React.ReactNode;
  onClick: () => void;
  className?: string;
  isDisabled?: boolean;
}) => {
  return (
    <button
      className={clsx(
        className,
        isDisabled && 'opacity-40',
        'w-full h-[35px] lg:h-[41px] text-white bg-primary flex items-center justify-center text-[8px] sm:text-[9px] font-semibold lg:text-xs lg:font-bold cursor-pointer rounded-[5px] hover:bg-primary/80 hover:scale-105 active:scale-90 transition-all duration-200'
      )}
      disabled={isDisabled}
      onClick={onClick}
      style={{ boxShadow: '0px 4px 4px 0px rgba(0, 0, 0, 0.10)' }}
      type='button'
    >
      {text}
      {icon ? icon : <></>}
    </button>
  );
};

export default ThemeButton;
