import { useState } from 'react';

function CustomSwitch({
  switchDirection,
  leftIcon,
  rightIcon,
}: {
  switchDirection?: (direction: 'left' | 'right') => void;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}) {
  const [switchState, setSwitchState] = useState(false);

  return (
    <div
      className={` bg-white duration-500 relative w-[40px] h-[20px] rounded-full flex justify-center gap-1 items-center cursor-pointer`}
      onClick={() => {
        setSwitchState(!switchState);
        if (switchDirection) {
          switchDirection(switchState ? 'left' : 'right');
        }
      }}
      style={{ boxShadow: '0px 1px 7px 0px rgba(0, 0, 0, 0.25)' }}
    >
      {switchState === true && leftIcon}
      <span
        className={` ${
          switchState ? 'translate-x-0' : '-translate-x-5'
        } bg-[#6D56CF]  absolute right-[3px] w-[15px] h-[15px] rounded-full  duration-300 `}
      />
      {rightIcon}
    </div>
  );
}

export default CustomSwitch;
