'use client';
import clsx from 'clsx';
import React from 'react';
import SmartLink from '../common/smart-link';
import { LinkType } from '@/utils/link-utils';

export type TabsType = Array<{
  id: number;
  label: string;
  link?: string;
}>;

const TabsContainer = ({
  items,
  setActiveId,
  activeId,
  rootClass,
  hideCbRates = false,
}: {
  items: TabsType;
  setActiveId: (id: number) => void;
  activeId: number;
  rootClass?: string;
  hideCbRates?: boolean;
}) => {
  return (
    <div
      className={clsx(
        rootClass,
        'w-max h-[44px] bg-[#f5f5f5] dark:bg-[#2d2e32] flex flex-nowrap items-center gap-x-[13px] lg:gap-x-[22px] pl-[16px] lg:pl-[40px] pr-[20px]'
      )}
    >
      {items?.map((item) =>
        item.id === 3 && hideCbRates ? (
          <div key={item.id} />
        ) : (
          <div
            className={clsx(
              activeId === item?.id ? 'gap-y-[6px]' : 'pb-[10px]',
              'h-full flex flex-col justify-end shrink-0 items-center cursor-pointer group px-[5px]'
            )}
            key={item?.id}
            onClick={() => setActiveId(item?.id)}
          >
            {item?.link ? (
              <SmartLink
                className={clsx(
                  item?.id === activeId && '!font-bold',
                  `text-[10px] lg:text-xs text-blackWhite font-medium group-hover:!text-primary `
                )}
                href={`/${item?.link || ''}`}
                linkType={LinkType.INTERNAL}
              >
                {item?.label || 'Tab'}
              </SmartLink>
            ) : (
              <span
                className={clsx(
                  item?.id === activeId && '!font-bold',
                  `text-[10px] lg:text-xs text-blackWhite font-medium group-hover:!text-primary`
                )}
              >
                {item?.label || 'Tab'}
              </span>
            )}

            {item?.id === activeId && (
              <div className='activeTabDash h-[3px] w-[120%] lg:h-[4px] rounded-t-[2px] bg-primary' />
            )}
          </div>
        )
      )}
    </div>
  );
};

export default TabsContainer;
