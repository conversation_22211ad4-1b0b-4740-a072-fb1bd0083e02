import React, { useEffect, useState } from 'react';
import { ShareSVG } from '../svg/save';
import { Breadcrumb } from 'antd';
import clsx from 'clsx';
import { RWebShare } from 'react-web-share';
import { APP_URL } from '@/config';
import { LinkType } from '@/utils/link-utils';
import SmartLink from '../common/smart-link';
// import { useRouter } from 'next/router';

type breadCrumbs = Array<{
  title: string;
  link?: string;
}>;

const BreadcrumbSaveShare = ({
  breadCrumbs,
  rootClass,
}: {
  breadCrumbs: breadCrumbs;
  rootClass?: string;
}) => {
  const [url, setUrl] = useState('');

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const url = `https://${APP_URL}${window.location.pathname}`;
      setUrl(url);
    }
  }, []);

  return (
    <div
      className={clsx(
        rootClass,
        'hidden lg:flex bg-container h-[43px] justify-between items-center px-[15px] sm:px-[22px] pr-[30px] sm:pr-[48px] transition-all duration-300 shadow-sm'
      )}
    >
      <Breadcrumb
        items={breadCrumbs?.map((item, index) => ({
          title: (
            <SmartLink
              className={clsx(
                breadCrumbs.length - 1 === index
                  ? '!text-primary dark:!text-primary !font-semibold'
                  : '!text-[#636363] dark:!text-white',
                ` !text-[10px] md:!text-[11px] font-normal transition-colors duration-200 hover:!opacity-80`
              )}
              href={item?.link || ''}
              linkType={LinkType.INTERNAL}
            >
              {item?.title || 'Cashback Home'}
            </SmartLink>
          ),
        }))}
        separator='>'
      />

      <div className='flex items-center gap-x-[30px]'>
        <RWebShare
          data={{
            text: 'Found a great resource on Indian Cashback! 💸  \nCheck out this page, it might help you save some money while you shop. 👉 ',
            url: url,
            title: 'Share this page with your friends!',
          }}
          key={'share_page_1'}
        >
          <div className='flex items-center cursor-pointer select-none transition-transform duration-300 hover:scale-105 active:scale-95'>
            <ShareSVG
              className='text-primary w-[13px] h-[15px] transition-colors duration-200'
              fill='transparent'
            />
            <span className='text-blackWhite text-xs font-medium ml-[10px]'>
              Share Page
            </span>
          </div>
        </RWebShare>
      </div>
    </div>
  );
};

export default BreadcrumbSaveShare;
