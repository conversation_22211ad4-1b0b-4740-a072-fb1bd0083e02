import React from 'react';
import SearchInput from './search-input';
import { SelectMultipleOptionsProps } from '@/types/global-types';
import { ArrowDown } from '../svg/arrow-up-down';
import { Checkbox } from 'antd';
import clsx from 'clsx';

const SelectMultipleOptions = (props: SelectMultipleOptionsProps) => {
  return (
    <div className='selectMultipleOptions flex items-center justify-between px-[16px] relative h-[43px] w-full lg:w-[244px] rounded-[5px] border border-primary text-[#A4A4A4] dark:text-white text-[10px] lg:text-xs font-medium cursor-pointer group bg-white dark:bg-container'>
      <span>{props.placeholder || 'Select Options'}</span>
      <ArrowDown className='w-[10px] lg:w-[13px] text-blackWhite' />
      <div className='hidden group-hover:block absolute top-[43px] left-0 right-0 pt-[6px] overflow-hidden rounded-[5px] shadow-lg'>
        <div
          className={clsx(
            'relative bg-white dark:bg-container py-[10px] w-full max-h-[315px] px-[10px] rounded-[5px] overflow-hidden'
          )}
        >
          {props.isSearchable && (
            <>
              {/* <div className='sticky top-[-1px] pt-[5px] pb-[10px] left-[10px] right-[10px] bg-container z-[1]'> */}
              <SearchInput
                onChange={props.onChangeSearch}
                onClose={props?.onClose}
                rootClass='!w-full border border-primary'
                value={props.searchValue}
              />
              {/* </div> */}
              {/* <div className='mb-[10px]' /> */}
            </>
          )}

          <div className='flex flex-col gap-y-[15px] items-start pl-[5px] max-h-[250px] overflow-y-auto mt-[15px] pb-[10px] customScrollbar'>
            {props.data.map((item, index) => {
              const isSelected =
                props?.selectedItems && props?.selectedItems.includes(item);
              return (
                <div
                  className='cursor-pointer'
                  key={index + 1}
                  onClick={() => {
                    props?.onSelectItem && props?.onSelectItem(item);
                  }}
                >
                  <Checkbox checked={isSelected} key={index + 1} />
                  <button className='text-[#404040] dark:text-white text-xs font-medium ml-[10px] lg:ml-[15px] truncate max-w-[180px] capitalize'>
                    {item}
                  </button>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SelectMultipleOptions;
