import Image from 'next/image';
import React from 'react';
import clsx from 'clsx';
import leftArrowBtn from '@/public/img/arrow-left.png';
import rightArrowBtn from '@/public/img/arrow-right.png';

const LeftRoundButton = ({
  classCont,
  onClick,
  classBtn,
}: {
  classCont?: string;
  classBtn?: string;
  onClick?: () => void;
}) => {
  return (
    <div
      className={clsx(
        classCont,
        'hidden lg:inline-block cursor-pointer absolute z-[9] left-[162px] top-[50%] translate-y-[-50%] rounded-full w-[42px] h-[42px]'
      )}
      onClick={onClick}
    >
      <Image
        alt='left'
        className={clsx(classBtn, 'w-[42px] h-[42px]')}
        src={leftArrowBtn}
      />
    </div>
  );
};

const RightRoundButton = ({
  classCont,
  classBtn,
  onClick,
}: {
  classCont?: string;
  classBtn?: string;
  onClick?: () => void;
}) => {
  return (
    <div
      className={clsx(
        classCont,
        'hidden lg:inline-block cursor-pointer absolute z-[9] right-[10px] top-[50%] translate-y-[-50%] rounded-full w-[42px] h-[42px]'
      )}
      onClick={onClick}
    >
      <Image
        alt='left'
        className={clsx(classBtn, 'w-[42px] h-[42px]')}
        src={rightArrowBtn}
      />
    </div>
  );
};

export { LeftRoundButton, RightRoundButton };
