import React, { ReactNode } from 'react';
import { motion } from 'framer-motion';

interface TooltipProps {
  children: ReactNode;
  content: string;
}

export const Tooltip = ({ children, content }: TooltipProps) => {
  return (
    <div className="relative group">
      {children}
      <motion.div
        animate={{ y: 0, opacity: 1 }}
        className="absolute z-10 invisible group-hover:visible opacity-0 group-hover:opacity-100 bottom-full left-1/2 transform -translate-x-1/2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg mb-2 min-w-max"
        initial={{ y: 10, opacity: 0 }}
        transition={{ duration: 0.2 }}
      >
        {content}
        <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900" />
      </motion.div>
    </div>
  );
};