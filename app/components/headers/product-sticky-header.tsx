import Image from 'next/image';
import React from 'react';
import ThemeButton from '../atoms/theme-btn';
import clsx from 'clsx';
import { useAppSelector } from '@/redux/hooks';
import { LoadingGif } from '../misc/loading-components';

const ProductStickyHeader = ({
  productImg,
  storeImg,
  title,
  isSticky,
  offerProductHandler,
}: {
  productImg: string;
  storeImg: string;
  title: React.ReactNode;
  isSticky: boolean;
  offerProductHandler: () => void;
}) => {
  const { isGlobalLoading } = useAppSelector((state) => state.global);
  return (
    <div
      className={clsx(
        isSticky ? 'top-[65px]' : 'top-[-65px]',
        'h-[56px] w-full transition-all	duration-500 ease-in-out bg-[#E7E9EB] dark:bg-[#2D313A] px-[16px] flex lg:hidden items-center justify-between shadow-md fixed z-[99]'
      )}
    >
      <div className='grow-0 flex items-center'>
        <div className='relative'>
          <div className='shrink-0 w-[35px] h-[35px] overflow-hidden rounded-[2.5px] border-[1px] border-[#C8C8C8]'>
            <Image
              alt='product'
              className='w-[35px] h-[35px]'
              height={100}
              quality={100}
              src={productImg}
              width={100}
            />
          </div>
          <div className='bg-[#FDFDFE] w-[24px] h-[8px] flex-center rounded-[6px] shadow absolute bottom-[-4px] left-[50%] translate-x-[-50%]'>
            <Image
              alt='store'
              className='w-[20px] h-[8px] object-scale-down'
              height={8}
              src={storeImg}
              width={24}
            />
          </div>
        </div>
        <div className='ml-[10px] text-left text-[10px] text-[#292B31] dark:text-white maxLines3'>
          {title}
        </div>
      </div>
      {isGlobalLoading ? (
        <LoadingGif className='!h-[30px] ml-[10px] shrink-0 mr-[10px]' />
      ) : (
        <ThemeButton
          className='max-w-[83px] ml-[10px] shrink-0'
          onClick={offerProductHandler}
          text='Grab Deal'
        />
      )}
    </div>
  );
};

export default ProductStickyHeader;
