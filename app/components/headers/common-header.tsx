'use client';
import React, { useEffect, useRef, useState } from 'react';
import RightArrow from '../svg/right-arrow';
import MenuDotsSVG from '../svg/menu-three-dots';
import clsx from 'clsx';
import OfferCardMenu from '../dropdowns/offer-card-menu';
import { useOnClickOutside } from 'usehooks-ts';
import SmartLink from '../common/smart-link';
import { LinkType } from '@/utils/link-utils';

const CommonHeader = ({
  headline,
  subHeading,
  showMenuBtn,
  children,
  onClickBack,
  backRoute,
}: {
  headline: string;
  subHeading?: string | React.ReactNode;
  showMenuBtn?: boolean;
  children?: React.ReactElement;
  onClickBack?: () => void;
  backRoute?: string;
}) => {
  const [showMenuDropdown, setShowMenuDropdown] = useState(false);
  const offerCardMenuRef = useRef<HTMLDivElement>(null);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);
  useOnClickOutside(offerCardMenuRef, () => setShowMenuDropdown(false));
  return (
    <>
      <div className='flex items-center justify-between px-[16px] bg-[#574ABE] h-[65px] fixed z-[999] top-0 w-full lg:hidden transition-all duration-300 shadow-md'>
        <div className='flex items-center'>
          <SmartLink href={backRoute || '/'} linkType={LinkType.INTERNAL}>
            <RightArrow
              className='text-white rotate-180 w-[20px] shrink-0 transition-transform duration-300 hover:scale-110 active:scale-95'
              onClick={onClickBack}
            />
          </SmartLink>
          <div className='flex flex-col items-baseline'>
            <h4
              className='font-normal text-sm font-pat text-white ml-[14px] capitalize transition-colors duration-200'
              style={{
                display: '-webkit-box',
                WebkitLineClamp: 1,
                WebkitBoxOrient: 'vertical',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              }}
            >
              {headline}
            </h4>

            {subHeading && (
              <div className='text-[8px] sm:text-[9px] text-white font-normal transition-opacity duration-200'>
                {subHeading}
              </div>
            )}
          </div>
        </div>
        <div className='pr-[8px] pl-[10px]' ref={offerCardMenuRef}>
          {children}
          {showMenuBtn && (
            <div
              className='p-[3px] transition-transform duration-300 hover:scale-110 active:scale-95'
              onClick={() => {
                setShowMenuDropdown(!showMenuDropdown);
              }}
              style={{ cursor: 'pointer' }}
            >
              <MenuDotsSVG className='text-white' />
            </div>
          )}
          <OfferCardMenu
            cardType='offer'
            hiddenItems={['Save', 'Info']}
            onClickItem={() => {
              // handleMenuItemClick(key, value); // Your existing menu item click handler
              setShowMenuDropdown(false); // Close the menu after an item is clicked
            }}
            rootClass={clsx(
              showMenuDropdown ? 'block' : 'hidden',
              'transition-opacity duration-300'
            )}
            saved={false}
            shareUrl={isClient ? window.location.pathname : ''}
          />
        </div>
      </div>
      <div className='h-[65px] lg:hidden' />
    </>
  );
};

export default CommonHeader;
