'use client';
import { createPortal } from 'react-dom';

const Portal = ({
  disable,
  children,
}: {
  disable?: boolean;
  children: any;
}) => {
  if (typeof window === 'undefined') {
    return;
  }

  if (disable) {
    return children;
  }

  return createPortal(
    <div className='fixed z-[9999999] inset-0 top-0 left-0 right-0 bottom-0 w-full h-full'>
      <div className='bg-black opacity-50 blur-sm h-full w-full absolute' />
      {children}
    </div>,
    document.body
  );
};

export default Portal;
