'use client';
import React from 'react';
import loadingGif from '@/public/img/icb-loading.gif';
import loadingGif2 from '@/public/img/icb-loading2.gif';
import Image from 'next/image';
import clsx from 'clsx';
import { usePathname } from 'next/navigation';

const PageLoader = ({ isMainLoader }: { isMainLoader: boolean }) => {
  const pathname = usePathname();
  return (
    <div className='fixed z-[9999999] inset-0 w-full h-full flex-center'>
      <div
        className={clsx(
          'bg-white dark:bg-black w-full h-full top-0 absolute',
          !isMainLoader &&
            'opacity-90 lg:h-[calc(100vh-104px)] lg:top-[104px] ',
          isMainLoader && 'opacity-100 top-0'
        )}
      />

      {pathname === '/' ? (
        <div className='flex-center flex-col relative'>
          <div className='relative w-[148px] h-[122px] lg:w-[206px] lg:h-[169px]'>
            <Image
              alt='loader'
              className='object-contain'
              fill
              src={loadingGif}
            />
          </div>
          <h4 className='text-[#4B3DB8] text-sm lg:text-[18px] font-semibold mt-[20px] lg:mt-[30px]'>
            Your savings gateway is loading...
          </h4>
        </div>
      ) : (
        <div className='relative w-[60px] h-[60px]'>
          <Image
            alt='loader'
            className='object-contain'
            fill
            src={loadingGif2}
          />
        </div>
      )}
    </div>
  );
};

// export const CoinInHandPageLoader = () => {
//   return (
//     <div className='fixed z-[9999999] inset-0 w-full h-full flex-center'>
//       <div className='bg-black opacity-50 blur-sm h-full w-full absolute' />
//       <div className='relative w-[300px] h-[300px] lg:w-[490px] lg:h-[505px]'>
//         <Image alt='loader' className='object-contain' fill src={loadingGif} />
//       </div>
//     </div>
//   );
// };

export const LoadingGif = ({ className }: { className?: string }) => {
  return (
    <div className={clsx(className, 'relative w-[50px] h-[50px] mx-auto')}>
      <Image
        alt='loading gif'
        className='object-contain'
        fill
        src={loadingGif2}
      />
    </div>
  );
};

export default PageLoader;
