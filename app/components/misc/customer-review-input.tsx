import { ConfigProvider, Rate, theme } from 'antd';
import React, { useState } from 'react';
import ThemeButton from '../atoms/theme-btn';
import { useTheme } from 'next-themes';

const CustomerReviewInputCont = ({
  onClickSubmit,
}: {
  onClickSubmit: (review: string, rating: number) => Promise<boolean>;
}) => {
  const [review, setReview] = useState('');
  const [rating, setRating] = useState(0);
  const { resolvedTheme } = useTheme();

  const handleReviewChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setReview(e.target.value);
  };

  const handleSubmit = async () => {
    const success = await onClickSubmit(review, rating);
    if (success) {
      setReview(''); // Clear the review value if the submission is successful
    }
  };

  return (
    <div className='flex flex-col items-center justify-center lg:px-[14px] mt-[20px] lg:border-[1px] border-[#bdc3bd7d] dark:border-[#e6e6e624] rounded-[10px] border-[0px] lg:pt-[10px] lg:pb-[20px]'>
      <textarea
        className='w-full min-h-[109px] lg:min-h-[50px] bg-white dark:bg-[#18191D] text-blackWhite text-[8px] sm:text-[9px] lg:text-xs font-normal py-[8px] lg:py-[12px] px-[8px] outline-none border-[1px] lg:border-0 border-primary rounded-[5px] order-1 lg:order-2 lg:mt-[17px]'
        name='review'
        onChange={handleReviewChange}
        placeholder='Write your review about the seller!'
        style={{ boxShadow: '0px 4px 16px 0px rgba(0, 0, 0, 0.04)' }}
        value={review}
      />

      <div className='order-2 lg:order-1 flex items-center lg:self-start mt-[15px] lg:mt-0'>
        <span className='text-blackWhite text-[8px] sm:text-[9px] lg:text-xs font-[300] lg:font-normal'>
          Add your Rating
        </span>
        <ConfigProvider
          theme={{
            algorithm:
              resolvedTheme === 'dark'
                ? theme.darkAlgorithm
                : theme.defaultAlgorithm,
          }}
        >
          <Rate
            className='ml-[10px] !text-[17px] lg:!text-[22px]'
            onChange={setRating}
            value={rating}
          />
        </ConfigProvider>
      </div>

      <ThemeButton
        className='mt-[23px] !w-[96px] order-3'
        onClick={() => {
          handleSubmit();
          setReview('');
        }}
        text='Submit'
      />
    </div>
  );
};

export default CustomerReviewInputCont;
