'use client';
import type React from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';
import { useAppDispatch } from '@/redux/hooks';
import { setLoading } from '@/redux/slices/auth-slice';
import { toast } from 'react-toastify';
import { useSearchParams } from 'next/navigation';

interface GoogleLoginButtonProps {
  className?: string;
}

const GoogleLoginButton: React.FC<GoogleLoginButtonProps> = ({
  className = '',
}) => {
  const dispatch = useAppDispatch();
  const searchParams = useSearchParams();

  const handleGoogleLogin = async () => {
    dispatch(setLoading(true));
    try {
      const referralCode =
        searchParams.get('r') || localStorage.getItem('referralCode') || '';
      // Redirect to the Google OAuth flow
      // The backend will handle the authentication process
      // We need to include any referral code that might be present
      window.location.href = `/api/proxy/auth/google?referralCode=${referralCode}`;

      // Note: We don't need to call onSuccess here as the page will redirect
      // The success handling will happen when the user returns from Google OAuth
    } catch (error) {
      console.error('Google login error:', error);
      toast.error('Failed to initiate Google login. Please try again.');
      dispatch(setLoading(false));
    }
  };

  return (
    <motion.button
      className={`flex items-center justify-center gap-2 w-full py-2 px-4 border border-gray-300 rounded-md bg-white text-gray-700 hover:bg-gray-50 transition-all duration-200 ${className}`}
      onClick={handleGoogleLogin}
      type='button'
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
    >
      <Image
        alt='Google logo'
        height={20}
        src='/svg/google-logo.svg'
        width={20}
      />
      <span className='text-sm font-medium'>Continue with Google</span>
    </motion.button>
  );
};

export default GoogleLoginButton;
