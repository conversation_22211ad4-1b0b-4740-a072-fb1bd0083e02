'use client';
import React, { useMemo, useEffect } from 'react';
import Portal from '../react-portal';
import Image from 'next/image';
import CrossSVG from '../svg/cross';
import ICBLogo from '../svg/icb-logo';
import ICBLogoPrimary from '../svg/icb-logo-primary';
import SlidingButton from '../atoms/sliding-button';
import {
  setLoginModalOpen,
  setLoginSignupScreen,
} from '@/redux/slices/auth-slice';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import LoginForm from './login-form';
import SignupForm from './signup-form';
import RightArrow from '../svg/right-arrow';
// import { useGenerateClickData } from '@/utils/custom-hooks';
import { usePathname } from 'next/navigation';
import { ProtectedPages } from '@/static-data/constants';
import clsx from 'clsx';
import { useWindowSize } from 'usehooks-ts';
import { useGenerateClickData } from '@/utils/custom-hooks';
import { motion } from 'framer-motion';
import { useTheme } from 'next-themes';

const LoginSignUpModal = ({ isRootLayout }: { isRootLayout?: boolean }) => {
  const { isLoginModalOpen, isLoginModalClosable, screen } = useAppSelector(
    (state) => state.auth
  );
  const { isGlobalLoading, selectedOffer, hasProceedWithoutCb } =
    useAppSelector((state) => state.global);
  const dispatch = useAppDispatch();
  const generateClickData = useGenerateClickData();
  // const router = useRouter();
  const pathname = usePathname();
  const memoizedProtectedPages = useMemo(() => ProtectedPages, []);
  const { width = 0 } = useWindowSize();
  const { resolvedTheme } = useTheme();

  let isLoginPortalDisable = false;
  // const { clickGeneratedData } = useAppSelector((state) => state.global);

  // useEffect(() => {
  //   setTimeout(() => {
  //     if (clickGeneratedData?.url) {
  //       dispatch(setShowClickRegisModal(true));
  //     }
  //   }, 1000);
  // }, [clickGeneratedData, dispatch]);

  useEffect(() => {
    // Disable body scroll when modal is open
    if (isLoginModalOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }

    return () => {
      document.body.style.overflow = 'auto';
    };
  }, [isLoginModalOpen]);

  if (width > 1023) {
    isLoginPortalDisable = true;
  }

  if (isRootLayout && memoizedProtectedPages.includes(pathname)) {
    return;
  }

  if (!isLoginModalOpen) {
    return;
  }

  // Animation variants
  const overlayVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.3 } },
  };

  const modalVariants = {
    hidden: { opacity: 0, scale: 0.95 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.4,
        type: 'spring',
        stiffness: 300,
        damping: 30,
      },
    },
  };

  return (
    <Portal
      disable={
        isLoginPortalDisable && memoizedProtectedPages.includes(pathname)
      }
    >
      <motion.div
        animate='visible'
        className={clsx(
          isLoginPortalDisable &&
            memoizedProtectedPages.includes(pathname) &&
            '!relative inset-auto',
          'signInWrapper absolute inset-0 flex-center backdrop-blur-sm bg-black/30 dark:bg-black/50 z-50'
        )}
        exit='hidden'
        initial='hidden'
        variants={overlayVariants}
      >
        <motion.div
          animate='visible'
          className='signInModalContainer relative flex flex-col lg:flex-row w-full h-auto lg:w-[964px] lg:h-[547px] lg:rounded-[15px] overflow-auto lg:overflow-hidden pb-[50px] lg:pb-0 shadow-2xl'
          style={{
            background:
              'linear-gradient(155deg, #4D3EC1 4.31%, #2A1C92 90.03%)',
          }}
          variants={modalVariants}
        >
          <div className='w-full lg:w-[515px] lg:h-full flex-center flex-col pt-[15px] pb-[20px] lg:py-0 relative'>
            {isLoginModalClosable && (
              <motion.button
                aria-label='Close modal'
                className='flex-center lg:hidden w-[30px] h-[30px] ml-auto cursor-pointer mr-[16px] transition-all duration-200 hover:bg-white/10 rounded-full'
                onClick={() => {
                  dispatch(setLoginSignupScreen({ for: 'login', step: 1 }));
                  dispatch(setLoginModalOpen(false));
                }}
                type='button'
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.95 }}
              >
                <CrossSVG className='w-[11px] h-[11px] text-white' />
              </motion.button>
            )}
            <motion.div
              animate={{ y: 0, opacity: 1 }}
              className='lg:hidden mb-2'
              initial={{ y: 10, opacity: 0 }}
              transition={{ delay: 0.2 }}
            >
              <Image
                alt='icb-logo'
                className='w-[70px] h-[50px]'
                height={50}
                src='/svg/icb-logo.svg'
                width={70}
              />

              <div className='flex text-[6px] mt-[5px]'>
                <span className='font-light text-[#E2E2E2]'>Indian</span>
                <span className='font-medium text-white'>cashback.com</span>
              </div>
            </motion.div>
            <motion.div
              animate={{ y: 0, opacity: 1 }}
              className='flex items-center lg:flex-col mt-[15px] lg:mt-0 px-4 lg:px-0'
              initial={{ y: 20, opacity: 0 }}
              transition={{ delay: 0.3, duration: 0.5 }}
            >
              <motion.div
                animate={{
                  y: [0, -8, 0],
                  transition: {
                    y: {
                      duration: 2,
                      repeat: Number.POSITIVE_INFINITY,
                      repeatType: 'reverse',
                      ease: 'easeInOut',
                    },
                  },
                }}
                className='relative w-[70px] h-[70px] lg:w-[187px] lg:h-[191px]'
                whileHover={{
                  scale: 1.08,
                  rotate: [0, -2, 2, -2, 0],
                  transition: {
                    scale: { duration: 0.3 },
                    rotate: {
                      duration: 0.5,
                      repeat: Number.POSITIVE_INFINITY,
                      repeatType: 'reverse',
                    },
                  },
                }}
              >
                <motion.div
                  animate={{
                    opacity: [0, 1, 0],
                    scale: [0.5, 1.2, 0.5],
                    transition: {
                      duration: 3,
                      delay: 1,
                      repeat: Number.POSITIVE_INFINITY,
                      repeatDelay: 4,
                    },
                  }}
                  className='absolute -top-6 -right-4 lg:-top-10 lg:-right-8 z-10 hidden lg:block'
                  initial={{ opacity: 0, scale: 0 }}
                >
                  <div className='bg-yellow-400 text-primary text-xs lg:text-sm font-bold px-3 py-1 rounded-full transform rotate-12'>
                    100% Real!
                  </div>
                </motion.div>
                <Image
                  alt='real-money'
                  className='object-contain drop-shadow-xl'
                  fill
                  src='/img/real-money.png'
                />
                <motion.div
                  animate={{
                    opacity: [0, 1, 0],
                    scale: [0.5, 1.2, 0.5],
                    transition: {
                      duration: 3,
                      delay: 3,
                      repeat: Number.POSITIVE_INFINITY,
                      repeatDelay: 4,
                    },
                  }}
                  className='absolute -bottom-3 -left-3 lg:-bottom-5 lg:-left-5 z-10'
                  initial={{ opacity: 0, scale: 0 }}
                >
                  <div className='bg-green-400 text-primary text-xs lg:text-sm font-bold px-3 py-1 rounded-full transform -rotate-12'>
                    Cashback!
                  </div>
                </motion.div>
              </motion.div>
              <motion.div
                className='max-w-[189px] lg:max-w-[331px] lg:mt-[10px] text-white ml-[15px] lg:ml-0'
                whileHover={{ scale: 1.03 }}
              >
                <motion.h1
                  animate={{
                    color: ['#ffffff', '#ffeb3b', '#ffffff'],
                    textShadow: [
                      '0 0 0px rgba(255,255,255,0)',
                      '0 0 10px rgba(255,255,255,0.5)',
                      '0 0 0px rgba(255,255,255,0)',
                    ],
                    transition: {
                      duration: 3,
                      repeat: Number.POSITIVE_INFINITY,
                      repeatType: 'reverse',
                    },
                  }}
                  className='text-base lg:text-[27px] font-bold'
                  initial={{ opacity: 0.9 }}
                >
                  Get real money!
                </motion.h1>
                <motion.p
                  animate={{
                    opacity: [0.8, 1, 0.8],
                    transition: {
                      duration: 2,
                      repeat: Number.POSITIVE_INFINITY,
                      repeatType: 'reverse',
                    },
                  }}
                  className='text-[10px] lg:text-sm font-medium mt-[5px] lg:mt-[8px]'
                  initial={{ opacity: 0.8 }}
                  whileHover={{ opacity: 1 }}
                >
                  Get real money back to your bank from all your favorite online
                  shopping sites listed with us
                </motion.p>
                <motion.p
                  animate={{
                    opacity: [0.8, 1, 0.8],
                    transition: {
                      duration: 2,
                      repeat: Number.POSITIVE_INFINITY,
                      repeatType: 'reverse',
                    },
                  }}
                  className='text-[8px] text-white/80 font-medium mt-[5px] lg:mt-[8px]'
                  initial={{ opacity: 0.8 }}
                  whileHover={{ opacity: 1 }}
                >
                  *T&C Apply
                </motion.p>
                <motion.div
                  animate={{
                    opacity: 1,
                    x: 0,
                    transition: {
                      delay: 0.8,
                      duration: 0.5,
                    },
                  }}
                  className='mt-3 hidden lg:flex items-center'
                  initial={{ opacity: 0, x: -20 }}
                >
                  <motion.div
                    animate={{
                      scale: [1, 1.03, 1],
                      transition: {
                        duration: 2,
                        repeat: Number.POSITIVE_INFINITY,
                        repeatType: 'reverse',
                      },
                    }}
                    className='flex items-center gap-1 bg-white/10 px-3 py-1 rounded-full'
                    whileHover={{
                      scale: 1.05,
                      backgroundColor: 'rgba(255,255,255,0.2)',
                    }}
                  >
                    <span className='w-2 h-2 rounded-full bg-green-400 animate-pulse' />
                    <span className='text-xs text-white'>
                      Trusted by 100K+ users
                    </span>
                  </motion.div>
                </motion.div>
              </motion.div>
            </motion.div>
          </div>

          <div className='px-[10px] lg:px-0 h-full'>
            <motion.div
              animate={{ x: 0, opacity: 1 }}
              className='relative w-full h-fit min-h-[430px] mx-auto lg:mx-0 rounded-[10px] lg:rounded-none lg:w-[449px] lg:h-full bg-[#F3F3F3] dark:bg-container lg:p-[29px] p-[15px] grow shrink-0 max-w-[500px] lg:max-w-none'
              initial={{ x: 20, opacity: 0 }}
              transition={{ delay: 0.4, duration: 0.5 }}
            >
              {isLoginModalClosable && (
                <motion.button
                  aria-label='Close modal'
                  className='hidden lg:flex-center w-[30px] h-[30px] ml-auto cursor-pointer transition-all duration-200 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-full'
                  onClick={() => {
                    dispatch(setLoginSignupScreen({ for: 'login', step: 1 }));
                    dispatch(setLoginModalOpen(false));
                  }}
                  type='button'
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <CrossSVG className='w-[11px] h-[11px] text-[#06020E] dark:text-white' />
                </motion.button>
              )}
              <div className='hidden lg:flex lg:justify-center'>
                {resolvedTheme === 'light' ? (
                  <ICBLogoPrimary className='w-auto h-[23px] text-primary dark:text-white' />
                ) : (
                  <ICBLogo className='w-auto h-[23px] text-primary dark:text-white' />
                )}
                <div className='flex items-end leading-[1] font-nexa text-[20px] text-primary dark:text-white'>
                  <span className='font-light ml-[5px]'>indian</span>
                  <span className='font-medium leading-[0.8] ml-[1px]'>
                    cashback.com
                  </span>
                </div>
              </div>

              {/* ---------------------Main Content-------------------------- */}
              <div className='mainContent py-[10px] lg:py-[30px] lg:pt-[17px]'>
                <SlidingButton
                  buttonDetails={[
                    { title: 'Login', value: 'login' },
                    { title: 'SignUp', value: 'signup' },
                  ]}
                  defaultSelectedBtn={screen.for === 'login' ? 1 : 2}
                  onChange={(e) =>
                    dispatch(
                      setLoginSignupScreen({ for: e.target.value, step: 1 })
                    )
                  }
                  rootClassName='mb-4'
                  uniqueId='auth'
                />
                <LoginForm />
                <SignupForm />
                {/* ------------Proceed Without Cashback and Navigation---------------- */}
                {screen.step !== 2 && (
                  <motion.div
                    animate={{ y: 0, opacity: 1 }}
                    className='flex flex-col gap-3 mt-4 lg:mt-8'
                    initial={{ y: 10, opacity: 0 }}
                    transition={{ delay: 0.6 }}
                  >
                    {screen.step === 1 &&
                      selectedOffer.uid &&
                      hasProceedWithoutCb && (
                        <motion.button
                          className='text-[10px] text-[#4E3FC5] font-semibold flex-center mx-auto mb-1 py-2 px-3 rounded-md hover:bg-indigo-50 dark:hover:bg-indigo-900/20 transition-all duration-200'
                          onClick={async () => {
                            // window.open(
                            //   `/redirecting?uid=${selectedOffer.uid}&type=${selectedOffer.type}`
                            // );
                            const actions = [];

                            actions.push(
                              generateClickData({
                                uid: selectedOffer.uid,
                                type: selectedOffer.type,
                              })
                            );
                            actions.push(
                              window.open(
                                '/redirecting',
                                '_blank',
                                'noreferrer'
                              )
                            );
                            await Promise.all(actions);
                          }}
                          type='button'
                          whileHover={{ scale: 1.03 }}
                          whileTap={{ scale: 0.98 }}
                        >
                          {isGlobalLoading ? (
                            'Please wait...'
                          ) : (
                            <>
                              Proceed me to store
                              <span className='font-bold ml-[3px]'>
                                WITHOUT CASHBACK
                              </span>
                              <RightArrow className='text-primary w-[12px] ml-[5px]' />
                            </>
                          )}
                        </motion.button>
                      )}
                    {/* <div className='flex-center gap-x-[15px]'>
                      <motion.button
                        className='text-xs md:text-sm flex-center gap-x-[6px] border border-gray-500 rounded-[5px] h-[32px] md:h-[40px] w-[110px] md:w-[120px] hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200'
                        onClick={() => {
                          if (memoizedProtectedPages.includes(pathname)) {
                            router.back();
                            dispatch(setLoginModalOpen(false));
                          } else {
                            dispatch(setLoginModalOpen(false));
                          }
                        }}
                        type='button'
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <RightArrow className='rotate-180 w-[10px] md:w-[12px]' />{' '}
                        Go Back
                      </motion.button>
                      <motion.button
                        className='text-xs md:text-sm border border-gray-500 rounded-[5px] h-[32px] md:h-[40px] w-[110px] md:w-[120px] flex-center hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-200'
                        onClick={() => {
                          router.push('/');
                          dispatch(setLoginModalOpen(false));
                        }}
                        type='button'
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        Go Home
                      </motion.button>
                    </div> */}
                  </motion.div>
                )}
              </div>
            </motion.div>
          </div>
        </motion.div>
      </motion.div>
    </Portal>
  );
};

export default LoginSignUpModal;
