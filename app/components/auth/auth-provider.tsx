'use client';

import { useAppDispatch } from '@/redux/hooks';
import { useEffect } from 'react';
import { checkAuthStatus, handleAuthError } from '@/utils/auth-utils';
import { setGlobalAuthErrorHandler } from '@/utils/fetch-wrapper';

/**
 * AuthProvider component that checks authentication status on app load
 * and updates the Redux store accordingly
 */
export default function AuthProvider({ children }: { children: React.ReactNode }) {
  const dispatch = useAppDispatch();

  useEffect(() => {
    // Set up global auth error handler
    setGlobalAuthErrorHandler((status: number) => {
      console.log(`Global auth error handler called with status: ${status}`);
      // Don't automatically show login modal - let individual components decide
      handleAuthError(dispatch, false);
    });

    // Check authentication status when the app loads
    const checkAuth = async () => {
      await checkAuthStatus(dispatch);
    };

    checkAuth();
  }, [dispatch]);

  return <>{children}</>;
}
