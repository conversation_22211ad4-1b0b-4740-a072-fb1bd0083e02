'use client';
import { useState, useRef, useEffect } from 'react';
import AllLinks from '../svg/all-links';
import CashbackHandSVG from '../svg/cashback-hand';
import ICBCardSVG from '../svg/icb-card';
import { useRouter } from 'next/navigation';
import { Coins } from 'lucide-react';
import { useForesightBatch } from '@/utils/foresight-hooks';

function BottomNavbar() {
  const router = useRouter();
  const [active, setActive] = useState(1);
  const { registerElements } = useForesightBatch();

  // Refs for each navigation item
  const homeRef = useRef<HTMLDivElement>(null);
  const cardRef = useRef<HTMLDivElement>(null);
  const referralRef = useRef<HTMLDivElement>(null);
  const allLinksRef = useRef<HTMLDivElement>(null);

  // Register all navigation elements with ForesightJS
  useEffect(() => {
    const elements = [
      { element: homeRef.current, url: '/', options: { hitSlop: 30, name: 'bottom-nav-home' } },
      { element: referralRef.current, url: '/referral', options: { hitSlop: 30, name: 'bottom-nav-referral' } },
      { element: allLinksRef.current, url: '/all-links', options: { hitSlop: 30, name: 'bottom-nav-all-links' } },
    ];

    registerElements(elements);
  }, [registerElements]);

  return (
    <div
      className={`bottomNavbarGradient lg:hidden fixed -bottom-[1px] w-full mx-auto bg-[#FCFCFC] z-50 flex justify-around items-center`}
    >
      <div
        className='text-center relative h-full flex flex-col justify-center items-center py-4'
        onClick={() => {
          setActive(1);
          router.push('/');
        }}
        ref={homeRef}
      >
        {active === 1 && (
          <span className='bg-[#7366D9] h-[3px] flex rounded-b w-full absolute top-0 ' />
        )}
        <CashbackHandSVG className='!text-[#7366D9] w-[25px] dark:text-[#63697A]' />
        <span className='text-[#AEB5C9] text-[8px] dark:text-[#63697A] mt-[6px]'>
          CASHBACK
        </span>
      </div>
      {/* <div className=' text-center relative h-full flex flex-col justify-center items-center' onClick={()=>setActive(2)}>
      {active === 2 && (
          <span className='bg-[#7366D9] h-[3px] flex rounded-b w-full absolute top-0 ' />
        )}
        <GiftCardSVG className='w-[25px] text-[#AEB5C9] dark:text-[#63697A]' />
        <span className='text-[#AEB5C9] text-[8px] dark:text-[#63697A] mt-[6px]'>
          GIFT CARD
        </span>
      </div> */}
      <div
        className='text-center relative h-full flex flex-col justify-center items-center py-4'
        onClick={() => {
          setActive(3);
          router.push('https://card.indiancashback.com/');
        }}
        ref={cardRef}
      >
        {active === 3 && (
          <span className='bg-[#7366D9] h-[3px] flex rounded-b w-full absolute top-0 ' />
        )}
        <ICBCardSVG className='w-[22px] text-[#AEB5C9] dark:text-[#63697A]' />
        <span className='text-[#7366D9] text-[8px] dark:text-[#63697A] mt-[4px]'>
          ICB CARD
        </span>
      </div>
      <div
        className='text-center relative h-full flex flex-col justify-center items-center py-4'
        onClick={() => {
          setActive(4);
          router.push('/referral');
        }}
        ref={referralRef}
      >
        {active === 4 && (
          <span className='bg-[#7366D9] h-[3px] flex rounded-b w-full absolute top-0 ' />
        )}
        <Coins className='w-[22px] text-[#AEB5C9] dark:text-[#63697A]' />
        <span className='text-[#7366D9] text-[8px] dark:text-[#63697A] mt-[4px]'>
          Refer & Earn
        </span>
      </div>
      <div
        className='text-center relative h-full flex flex-col justify-center items-center py-4'
        onClick={() => {
          setActive(5);
          router.push('/all-links');
        }}
        ref={allLinksRef}
      >
        {active === 5 && (
          <span className='bg-[#7366D9] h-[3px] flex rounded-b w-full absolute top-0 ' />
        )}
        <AllLinks className='w-[20px] text-[#AEB5C9] dark:text-[#63697A]' />
        <span className='text-[#AEB5C9] text-[8px] dark:text-[#63697A] mt-[4px]'>
          ALL LINKS
        </span>
      </div>
    </div>
  );
}

export default BottomNavbar;
