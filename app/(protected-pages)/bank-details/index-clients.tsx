'use client';
import React, { useState } from 'react';
import CommonHeader from '../../components/headers/common-header';
import BreadcrumbSaveShare from '../../components/atoms/breadcrumb-container';
import Image from 'next/image';
import {
  InputFieldNormal,
  InputFieldWithEdit,
  TextAreaNormal,
} from '../../components/atoms/form-inputs';
import ThemeButton from '../../components/atoms/theme-btn';
import { GetBankAccountDataResponse } from '@/services/api/data-contracts';
import { useForm } from 'react-hook-form';
import { toast } from 'react-toastify';
import fetchWrapper from '@/utils/fetch-wrapper';
import { motion } from 'framer-motion';

const IndexClientsBankDetails = ({
  data,
}: {
  data: GetBankAccountDataResponse;
}) => {
  const [isFormDisabled, setFormDisabled] = useState(true);
  const [isUpiFormDisabled, setUpiFormDisabled] = useState(true);

  const {
    reset: resetBankDetails,
    register: registerBankDetails,
    handleSubmit: submitBankDetails,
    formState: {
      errors: updateBankDetailsError,
      isDirty: isBankDetailFormModified,
    },
  } = useForm({
    defaultValues: {
      holderName: data?.holderName,
      accountNumber: data?.accountNumber,
      bankName: data?.bankName,
      branchName: data?.branchName,
      postcode: data?.postcode,
      address: data?.address,
      ifsc: data?.ifsc,
    },
  });

  const {
    reset: resetUpiData,
    register: registerUpiDetails,
    handleSubmit: submitUpiDetails,
    formState: { errors: updateUpiError, isDirty: isUpiModified },
  } = useForm({
    defaultValues: {
      upi: data?.upi,
    },
  });

  const setUpiInputVisibilty = () => {
    setUpiFormDisabled(!isUpiFormDisabled);
  };
  async function updateBankDetails(params: any) {
    if (isBankDetailFormModified) {
      await fetchWrapper('/api/proxy/users/update-bank-details', {
        method: 'POST',
        body: JSON.stringify(params),
      });
      setFormDisabled(true);
      resetBankDetails(params);
      return toast.info('Bank Details updated successfully.');
    }

    return toast.info('There are no changes to save.');
  }

  async function updateUpi(params: any) {
    if (isUpiModified) {
      const res = await fetchWrapper('/api/proxy/users/update-upi', {
        method: 'PATCH',
        body: JSON.stringify(params),
      });
      setUpiFormDisabled(true);
      resetUpiData(params);
      toast.info('Upi updated successfully.');
      return await res;
    } else {
      return toast.info('There are no changes to save.');
    }
  }

  return (
    <>
      <CommonHeader headline='Update Bank/UPI Details' />
      <motion.section
        animate={{ opacity: 1 }}
        className='max-w-[1280px] min-[1280px]:mx-auto'
        initial={{ opacity: 0 }}
        transition={{ duration: 0.5 }}
      >
        <motion.div
          animate={{ opacity: 1, y: 0 }}
          initial={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <BreadcrumbSaveShare
            breadCrumbs={[
              { title: 'Cashback Home', link: '/' },
              { title: 'Payments Page', link: '/payments' },
              { title: 'Update Bank / UPI Details' },
            ]}
          />
        </motion.div>
        <motion.div
          animate={{ opacity: 1 }}
          className='lg:bg-[#E0E0E0] lg:dark:bg-container pb-[60px]'
          initial={{ opacity: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <motion.div
            animate={{ opacity: 1, y: 0 }}
            className='bg-container lg:bg-transparent rounded-b-[10px] pb-[20px] lg:pb-0 mx-[6px] lg:mx-0 px-[8px] lg:px-[40px] xl:px-[50px]'
            initial={{ opacity: 0, y: 20 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <motion.div
              animate={{ opacity: 1, scale: 1 }}
              className='flex-center w-full pt-[58px] lg:pt-[26px]'
              initial={{ opacity: 0, scale: 0.9 }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              <Image
                alt='bank image'
                className='w-[120px] lg:w-[146px] h-auto'
                height={349}
                src='/img/bank.png'
                width={510}
              />
            </motion.div>
            <motion.div
              animate={{ opacity: 1, y: 0 }}
              className='flex flex-col items-center mt-[28px] w-full lg:mt-[22px] lg:bg-container lg:dark:bg-[#2d2e32] lg:py-[21px] lg:px-[30px] lg:rounded-[10px]'
              initial={{ opacity: 0, y: 20 }}
              transition={{ duration: 0.5, delay: 0.5 }}
              whileHover={{
                boxShadow: '0px 8px 30px rgba(0, 0, 0, 0.1)',
                transition: { duration: 0.3 },
              }}
            >
              <motion.h4
                animate={{ opacity: 1 }}
                className='hidden lg:inline-block text-blackWhite text-xs font-normal font-pat'
                initial={{ opacity: 0 }}
                transition={{ duration: 0.5, delay: 0.6 }}
              >
                Add Bank Info
              </motion.h4>
              <motion.div
                animate={{ opacity: 1 }}
                className='w-full lg:flex lg:flex-wrap lg:justify-center gap-x-[30px] lg:mt-[16px] justify-center'
                initial={{ opacity: 0 }}
                transition={{ duration: 0.5, delay: 0.7 }}
              >
                <motion.div
                  animate={{ opacity: 1, x: 0 }}
                  className='flex flex-col gap-y-[15px] lg:gap-y-[20px] mx-auto lg:mx-0 w-full sm:w-[360px] md:w-[400px]'
                  initial={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.5, delay: 0.8 }}
                >
                  <InputFieldNormal
                    defaultValue={data?.holderName}
                    isDisabled={isFormDisabled}
                    label='Full Name as per Bank details'
                    name='holderName'
                    placeholder='Full Name'
                    register={registerBankDetails}
                    validationSchema={{
                      required: 'Full Name is required',
                      pattern: {
                        value: /^[a-zA-Z\s,.'-]{3,}$/,
                        message: 'Invalid full name',
                      },
                    }}
                  />
                  {updateBankDetailsError.holderName?.message && (
                    <span className='text-[#F00] text-[10px] font-light'>
                      {updateBankDetailsError?.holderName?.message?.toString()}
                    </span>
                  )}

                  <InputFieldNormal
                    defaultValue={data?.bankName}
                    isDisabled={isFormDisabled}
                    label='Bank Name'
                    name='bankName'
                    placeholder='Bank Name'
                    register={registerBankDetails}
                    validationSchema={{
                      required: 'Bank Name is required',
                      pattern: {
                        value: /^[a-zA-Z\s,.'-]{3,}$/,
                        message: 'Invalid bank name',
                      },
                    }}
                  />
                  {updateBankDetailsError?.bankName?.message && (
                    <span className='text-[#F00] text-[10px] font-light'>
                      {updateBankDetailsError?.bankName?.message?.toString()}
                    </span>
                  )}

                  <InputFieldNormal
                    defaultValue={data?.branchName}
                    isDisabled={isFormDisabled}
                    label='Bank Branch'
                    name='branchName'
                    placeholder='Branch Name'
                    register={registerBankDetails}
                    validationSchema={{
                      required: 'Branch Name is required',
                      pattern: {
                        value: /^[a-zA-Z\s,.'-]{3,}$/,
                        message: 'Invalid branch name',
                      },
                    }}
                  />

                  {updateBankDetailsError?.branchName?.message && (
                    <span className='text-[#F00] text-[10px] font-light'>
                      {updateBankDetailsError?.branchName?.message?.toString()}
                    </span>
                  )}
                </motion.div>

                <motion.div
                  animate={{ opacity: 1, x: 0 }}
                  className='flex flex-col gap-y-[15px] lg:gap-y-[20px] mx-auto lg:mx-0 w-full sm:w-[360px] md:w-[400px]'
                  initial={{ opacity: 0, x: 20 }}
                  transition={{ duration: 0.5, delay: 0.9 }}
                >
                  <InputFieldNormal
                    defaultValue={data?.accountNumber}
                    isDisabled={isFormDisabled}
                    label='Account Number'
                    name='accountNumber'
                    placeholder='Account Number'
                    register={registerBankDetails}
                    validationSchema={{
                      required: 'Account Number is required',
                      pattern: {
                        value: /^\d{8,18}$/,
                        message: 'Invalid account number',
                      },
                    }}
                  />
                  {updateBankDetailsError?.accountNumber?.message && (
                    <span className='text-[#F00] text-[10px] font-light'>
                      {updateBankDetailsError?.accountNumber?.message?.toString()}
                    </span>
                  )}

                  <InputFieldNormal
                    defaultValue={data?.ifsc}
                    isDisabled={isFormDisabled}
                    label='Branch IFS Code'
                    name='ifsc'
                    placeholder='IFSC'
                    register={registerBankDetails}
                    validationSchema={{
                      required: 'IFSC is required',
                      pattern: {
                        value: /^[A-Za-z]{4}0[A-Z0-9a-z]{6}$/,
                        message: 'Invalid IFSC',
                      },
                    }}
                  />

                  {updateBankDetailsError?.ifsc?.message && (
                    <span className='text-[#F00] text-[10px] font-light'>
                      {updateBankDetailsError?.ifsc?.message?.toString()}
                    </span>
                  )}

                  <InputFieldNormal
                    defaultValue={data?.postcode}
                    isDisabled={isFormDisabled}
                    label='Your Address PIN Code'
                    name='postcode'
                    placeholder='Pincode'
                    register={registerBankDetails}
                    validationSchema={{
                      required: 'Pincode is required',
                      pattern: {
                        value: /^\d{6}$/,
                        message: 'Invalid pincode',
                      },
                    }}
                  />
                  {updateBankDetailsError?.postcode?.message && (
                    <span className='text-[#F00] text-[10px] font-light'>
                      {updateBankDetailsError?.postcode?.message?.toString()}
                    </span>
                  )}
                </motion.div>

                <motion.div
                  animate={{ opacity: 1, y: 0 }}
                  className='flex flex-col flex-center gap-y-[15px] lg:gap-y-[20px] w-full sm:max-w-[360px] md:max-w-[400px] lg:max-w-[830px] mx-auto lg:mx-0 mt-[18px]'
                  initial={{ opacity: 0, y: 20 }}
                  transition={{ duration: 0.5, delay: 1 }}
                >
                  <TextAreaNormal
                    defaultValue={data?.address}
                    inputClass='!w-full max-w-none !h-[120px]'
                    inputHeight={90}
                    isDisabled={isFormDisabled}
                    label='Your Address'
                    name='address'
                    placeholder='Your Address'
                    register={registerBankDetails}
                    rootClass='w-full'
                    validationSchema={{
                      required: 'Address is required',
                      pattern: {
                        value: /^[a-zA-Z0-9\s,.'-]+$/,
                        message: 'Invalid Address',
                      },
                    }}
                  />
                  {updateBankDetailsError?.address?.message && (
                    <span className='text-[#F00] text-[10px] font-light'>
                      {updateBankDetailsError?.address?.message?.toString()}
                    </span>
                  )}
                </motion.div>
              </motion.div>

              <motion.div
                animate={{ opacity: 1, y: 0 }}
                className='mt-[26px] flex-center w-full'
                initial={{ opacity: 0, y: 20 }}
                transition={{ duration: 0.5, delay: 1.1 }}
              >
                {isFormDisabled ? (
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <ThemeButton
                      className='!w-[90px]'
                      onClick={() => setFormDisabled(false)}
                      text='EDIT'
                    />
                  </motion.div>
                ) : (
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <ThemeButton
                      className='!w-[90px]'
                      onClick={submitBankDetails(updateBankDetails)}
                      text='SAVE'
                    />
                  </motion.div>
                )}
              </motion.div>
            </motion.div>
          </motion.div>
          <motion.div
            animate={{ opacity: 1, y: 0 }}
            className='mx-[6px] px-[8px] lg:w-[572px] lg:mx-auto bg-container lg:dark:bg-[#2d2e32] rounded-[10px] mt-[28px] lg:mt-[22px] py-[21px] lg:px-[30px]'
            initial={{ opacity: 0, y: 20 }}
            transition={{ duration: 0.5, delay: 1.2 }}
            whileHover={{
              boxShadow: '0px 8px 30px rgba(0, 0, 0, 0.1)',
              transition: { duration: 0.3 },
            }}
          >
            <motion.h4
              animate={{ opacity: 1 }}
              className='hidden lg:inline-block text-blackWhite text-xs font-normal font-pat'
              initial={{ opacity: 0 }}
              transition={{ duration: 0.5, delay: 1.3 }}
            >
              Add UPI ID
            </motion.h4>
            <InputFieldWithEdit
              defaultValue={data?.upi}
              isDisabled={isUpiFormDisabled}
              label='Enter Your UPI ID'
              name='upi'
              placeholder='Enter Your UPI ID'
              register={registerUpiDetails}
              rootClass='lg:mt-[16px] flex-center'
              setDisabled={setUpiInputVisibilty}
              validationSchema={{
                required: 'UPI ID is required',
                pattern: {
                  value: /^[a-zA-Z0-9._\-]{2,256}@[a-zA-Z]{2,64}$/,
                  message: 'Invalid upi id',
                },
              }}
            />

            {updateUpiError?.upi?.message && (
              <span className='text-[#F00] text-[10px] font-light'>
                {updateUpiError?.upi?.message?.toString()}
              </span>
            )}

            <motion.div
              animate={{ opacity: 1, y: 0 }}
              className='flex-center w-full mt-[26px] gap-x-[20px] relative'
              initial={{ opacity: 0, y: 20 }}
              transition={{ duration: 0.5, delay: 1.4 }}
            >
              {/* <span className='text-[#594CCC] text-[10px] font-semibold uppercase cursor-pointer absolute left-[20%] md:left-[30%]'>
                                Cancel
                            </span>{' '}*/}
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <ThemeButton
                  className='!w-[90px]'
                  onClick={submitUpiDetails(updateUpi)}
                  text='SAVE UPI'
                />
              </motion.div>
            </motion.div>
          </motion.div>
        </motion.div>
      </motion.section>
    </>
  );
};

export default IndexClientsBankDetails;
