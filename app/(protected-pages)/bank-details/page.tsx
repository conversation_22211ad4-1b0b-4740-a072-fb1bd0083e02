import IndexClientsUpdateBankUPI from './index-clients';
import { cookies } from 'next/headers';
import { BASE_URL } from '@/config';
import fetchWrapper from '@/utils/fetch-wrapper';
import { GetBankAccountDataResponse } from '@/services/api/data-contracts';

const emptyBankAccountDataResponse: GetBankAccountDataResponse = {
  bankName: '',
  accountNumber: '',
  ifsc: '',
  branchName: '',
  holderName: '',
  address: '',
  postcode: '',
  upi: '',
};

async function getBankDetails() {
  const cookieStore = cookies();
  const token = cookieStore.get('accessToken');
  try {
    const res = await fetchWrapper<GetBankAccountDataResponse>(
      `${BASE_URL}/users/get-bank-details`,
      {
        token: token?.value,
        cache: 'no-store',
      }
    );
    return res;
  } catch (e: any) {
    if (e.status === 400) {
      return emptyBankAccountDataResponse;
    } else {
      return e;
    }
  }
}

const Page = async () => {
  let resData;
  try {
    resData = await getBankDetails();
  } catch (err: any) {
    console.log({ err });
    return err;
  }

  return <IndexClientsUpdateBankUPI data={resData} />;
};

export default Page;
export const dynamic = 'force-dynamic';
