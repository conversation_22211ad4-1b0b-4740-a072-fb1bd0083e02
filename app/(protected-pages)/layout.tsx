import { getCookie } from 'cookies-next';
import { cookies } from 'next/headers';

import type React from 'react';
import OpenAuthModal from '../components/atoms/open-auth-modal';

const Layout = ({ children }: { children: React.ReactNode }) => {
  const accessToken = getCookie('accessToken', { cookies }) as string;
  return <>{accessToken ? children : <OpenAuthModal open={!accessToken} />}</>;
};

export default Layout;
