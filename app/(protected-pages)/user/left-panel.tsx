import { useAppSelector } from '@/redux/hooks';
import IndexClientsUser from './index-clients';
import { Drawer } from 'antd';

const LeftPanel = () => {
  const { isShowLeftPanel } = useAppSelector((state) => state.mainHeader);

  return (
    <Drawer
      className='font-pop'
      classNames={{ body: 'bg-container !p-0 scrollbarNone' }}
      closable={false}
      destroyOnClose={false}
      height={'100vh'}
      maskClosable={false}
      open={isShowLeftPanel}
      placement={'left'}
      rootClassName='outline-none'
      styles={{
        mask: { background: 'rgba(0, 0, 0, 0.65)' },
        wrapper: { width: '100%' },
      }}
    >
      <IndexClientsUser />
    </Drawer>
  );
};

export default LeftPanel;
