'use client';
import type React from 'react';
import BreadcrumbSaveShare from '../../components/atoms/breadcrumb-container';
import Image from 'next/image';
import ProfileSVG from '../../components/svg/profile';
import WalletAddSVG from '../../components/svg/wallet-add';
import CrossSVG from '../../components/svg/cross';
import InfoHighligtedCard from '../../components/user-page-components/info-highligted-card';
import RightArrow from '../../components/svg/right-arrow';
import PendingSVG from '../../components/svg/pending';
import CancelledSVG from '../../components/svg/cancelled';
import ApprovedSVG from '../../components/svg/approved';
import LinkContainer from '../../components/user-page-components/link-container';
import clsx from 'clsx';
import TelegramSVG from '../../components/svg/social/telegram';
import InstagramSVG from '../../components/svg/social/instagram';
import FacebookSVG from '../../components/svg/social/facebook';
import YoutubeSVG from '../../components/svg/social/youtube';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import { setShowLeftPanel } from '@/redux/slices/main-header-slice';
import LoginLogoutSVG from '../../components/svg/login-logout-icon';
import { setIsUserLogin, setLoginModalOpen } from '@/redux/slices/auth-slice';
import { deleteCookie } from 'cookies-next';
import { toast } from 'react-toastify';
import { usePathname, useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import SmartLink from '@/app/components/common/smart-link';
import { LinkType } from '@/utils/link-utils';
import WhatsappSVG from '@/app/components/svg/social/whatsapp';

const IndexClientsUser = () => {
  const dispatch = useAppDispatch();
  const { isShowLeftPanel } = useAppSelector((state) => state.mainHeader);
  const { isUserLogin, userDetails } = useAppSelector((state) => state.auth);
  const router = useRouter();
  const pathname = usePathname();

  const logoutUser = async () => {
    try {
      await fetch('/api/proxy/auth/logout', {
        method: 'DELETE',
        headers: {
          credentials: 'include',
        },
      });
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('isUserLogin');
      deleteCookie('accessToken');
      toast.info('Logout Successfully');
      dispatch(setIsUserLogin(false));
      dispatch(setShowLeftPanel(false));
      router.push('/');
    } catch (error) {
      console.error({ error });
    }
  };

  return (
    <motion.section
      animate={{ opacity: 1 }}
      className='max-w-[1280px] min-[1280px]:mx-auto pb-[50px] lg:pb-0'
      initial={{ opacity: 0 }}
      transition={{ duration: 0.5 }}
    >
      <BreadcrumbSaveShare
        breadCrumbs={[
          { title: 'Cashback Home', link: '/' },
          { title: 'User Page' },
        ]}
      />
      <div className='w-full flex lg:justify-center bg-primary lg:bg-[#E2E2E2] dark:lg:bg-[#35383E] py-[14px]'>
        {isUserLogin ? (
          <motion.div
            animate={{ opacity: 1, y: 0 }}
            className='flex flex-col items-start lg:flex-row lg:items-center lg:justify-center w-full lg:w-auto'
            initial={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <motion.div className='flex justify-between w-full'>
              <motion.div
                animate={{ opacity: 1, x: 0 }}
                className='userProfile flex items-center ml-[18px]'
                initial={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.5, delay: 0.3 }}
              >
                <Image
                  alt='profile'
                  className='w-[50px] h-[50px] lg:w-[112px] lg:h-[112px] rounded-full'
                  height={50}
                  quality={100}
                  sizes='100vw'
                  src={userDetails?.avatar || '/temp/profile.png'}
                  width={50}
                />
                <div className='ml-[12px] lg:ml-[18px] flex flex-col gap-y-[5px]'>
                  <span className='text-[12px] lg:text-sm font-medium text-white lg:text-blackWhite'>
                    {userDetails.name}
                  </span>
                  <div className='flex items-end'>
                    <span className='text-[8px] sm:text-[9px] lg:text-[11px] font-light leading-[1] text-white lg:text-blackWhite whitespace-nowrap'>
                      {userDetails.email}
                    </span>
                    <SmartLink
                      href='/profile-setting'
                      linkType={LinkType.INTERNAL}
                    >
                      <ProfileSVG className='text-white lg:text-blackWhite ml-[9px] w-[14px] lg:w-[17px]' />
                    </SmartLink>
                  </div>
                  <button
                    className='text-blackWhite text-xs gap-x-[10px] hidden lg:flex mt-[8px]'
                    onClick={logoutUser}
                    type='button'
                  >
                    <LoginLogoutSVG className='w-[15px] text-blackWhite' /> Log
                    Out
                  </button>
                </div>
              </motion.div>
              <motion.div
                animate={{ opacity: 1, x: 0 }}
                className='lg:hidden flex items-center mr-[10px]'
                initial={{ opacity: 0, x: 20 }}
                transition={{ duration: 0.5, delay: 0.3 }}
              >
                <div className=' h-auto bg-white w-max rounded-[5px] flex text-primary px-[8px] py-[4px]'>
                  <span className='pe-2 pt-1'>
                    <WalletAddSVG className='text-primary w-[10px]' />
                  </span>
                  <div className=''>
                    <p className='text-[10px] font-bold font-nexa'>
                      ₹ {userDetails.balance}
                    </p>
                    <p className='text-[5px]'>Ready to withdraw</p>
                  </div>
                </div>
                {pathname !== '/user' && (
                  <button
                    className='w-[24px] h-[24px] flex-center ml-[18px]'
                    onClick={() => dispatch(setShowLeftPanel(!isShowLeftPanel))}
                    type='button'
                  >
                    <CrossSVG className='text-white w-[15px]' />
                  </button>
                )}
              </motion.div>
            </motion.div>
            <motion.div
              animate={{ opacity: 1, y: 0 }}
              className='mt-[13px] lg:mt-0 lg:ml-[42px] flex items-center gap-x-[6px]'
              initial={{ opacity: 0, y: 20 }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              <div className='w-[96px] h-[72px] bg-[#FFC554] rounded-r-[5px] lg:rounded-[5px] flex-center relative after:w-[92px] after:left-0 after:h-[72px] after:rounded-r-[5px] after:opacity-50 after:bg-[#FFC554] after:absolute after:top-[4px] lg:after:content-none'>
                <span className='text-[10px] font-bold relative z-[1] text-black'>
                  Cashback
                </span>
              </div>
              <InfoHighligtedCard
                caption='Pending'
                icon={
                  <PendingSVG className='text-black dark:text-white w-[15px]' />
                }
                number={userDetails.pendingCount}
                onClick={() => router.push('/cashback-history?status=pending')}
                stripColorClass='bg-[#FFC554]'
              />
              <InfoHighligtedCard
                caption='Approved'
                icon={
                  <CancelledSVG className='text-black dark:text-white w-[15px]' />
                }
                number={userDetails.confirmedCount}
                onClick={() =>
                  router.push('/cashback-history?status=confirmed')
                }
                stripColorClass='bg-[#69C8B4]'
              />
              <InfoHighligtedCard
                caption='Cancelled'
                icon={
                  <ApprovedSVG className='text-black dark:text-white w-[15px]' />
                }
                number={userDetails?.cancelledCount}
                onClick={() =>
                  router.push('/cashback-history?status=cancelled')
                }
                stripColorClass='bg-[#F06B6B]'
              />
              <SmartLink
                className='flex items-center bg-[#FFC554] backdrop-blur-sm h-full lg:h-[72px] rounded-sm'
                href={'/my-earnings-overview'}
                linkType={LinkType.INTERNAL}
              >
                <motion.div
                  animate={{ opacity: 1, y: 0 }}
                  className='flex items-center  px-2 lg:px-4 py-1 rounded-full transition-all duration-300 group'
                  initial={{ opacity: 0, y: 10 }}
                  transition={{ duration: 0.3 }}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <motion.div
                    animate={{ x: [0, 3, 0] }}
                    transition={{
                      repeat: Number.POSITIVE_INFINITY,
                      repeatType: 'reverse',
                      duration: 1.5,
                      ease: 'easeInOut',
                    }}
                  >
                    <RightArrow className='text-black ml-[2px] w-[11px] lg:w-[17px] group-hover:translate-x-1 transition-transform duration-300 stroke-2' />
                  </motion.div>
                </motion.div>
              </SmartLink>
            </motion.div>
          </motion.div>
        ) : (
          <motion.div
            animate={{ opacity: 1 }}
            className='flex justify-between items-center w-full px-[16px] min-h-[50px]'
            initial={{ opacity: 0 }}
            transition={{ duration: 0.5 }}
          >
            <button
              className='flex items-center text-[12px] font-medium text-white'
              onClick={() => {
                dispatch(setShowLeftPanel(false)),
                  dispatch(setLoginModalOpen(true));
              }}
              type='button'
            >
              <LoginLogoutSVG className='text-white w-[12px] mr-[7px]' />
              Login/Sign Up
              <RightArrow className='text-white w-[10px] ml-[10px]' />
            </button>
            <button
              className='w-[24px] h-[24px] flex-center ml-[18px]'
              onClick={() => dispatch(setShowLeftPanel(!isShowLeftPanel))}
              type='button'
            >
              <CrossSVG className='text-white w-[15px]' />
            </button>
          </motion.div>
        )}
      </div>
      <motion.div
        animate={{ opacity: 1, y: 0 }}
        className='lg:bg-container px-[6px] lg:px-[40px] lg:pt-[10px]'
        initial={{ opacity: 0, y: 20 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      >
        {/* --------------for phase 1 hide the search-------- */}
        {/* <SearchInput
          onChange={(v) => setValue(v)}
          rootClass='hidden lg:block !w-full'
          value={value}
        /> */}
        <LinkContainer
          caption='You can find offers from your desired categories'
          iconClass='w-[13px]'
          iconUrl='/svg/categories/all.svg'
          onClick={() => router.push('/categories')}
          rootClass='mt-[22px] mx-[8px] mb-[19px] lg:!hidden cursor-pointer'
          title='Categories'
        />

        <div className='lg:mt-[20px]'>
          <div className='xl:flex items-center'>
            <div>
              <ContainerBlock title='My Activities'>
                <div className='flex flex-col lg:flex-row gap-y-[5px] lg:gap-x-[19px] mt-[11px]'>
                  {/* <LinkContainer
                    caption='Bill Payments, Investments, Cahback, Giftcards, Withdrawals'
                    iconClass='w-[20px] lg:w-[28px] '
                    iconUrl='/svg/user-page/transaction.svg'
                    onClick={() => router.push('/transactions')}
                    rootClass=' cursor-pointer'
                    title='Transactions'
                  /> */}
                  <LinkContainer
                    caption='Saved Products, Offers, Segments...Etc'
                    iconClass='w-[16px] lg:w-[28px]'
                    iconUrl='/svg/user-page/saved-items.svg'
                    onClick={() => router.push('/saved-items')}
                    rootClass=' cursor-pointer'
                    title='Saved Items'
                  />
                  {/* <LinkContainer
                    caption='You can find all notifications for you here.'
                    iconClass='w-[13px] lg:w-[20px]'
                    iconUrl='/svg/user-page/notification.svg'
                    title='Notifications'
                  /> */}
                </div>
              </ContainerBlock>
              <SmartLink href={'/refer-friends'} linkType={LinkType.INTERNAL}>
                <Image
                  alt='refer-a-friend-illustration'
                  className='w-full cursor-pointer h-auto xl:hidden mt-[10px] mb-[5px] hover:scale-[1.01] transition-all duration-500'
                  height={282}
                  src='/img/user-page/refer-friend-mobile.png'
                  width={831}
                />
                <p className='text-[8px] xl:hidden mb-[10px] w-full text-right text-white/80 font-medium'>
                  *T&C Apply
                </p>
              </SmartLink>
              <ContainerBlock title='Settings'>
                <div className='flex flex-col lg:flex-row gap-y-[5px] lg:gap-x-[19px] mt-[11px]'>
                  <LinkContainer
                    caption='Update Profile Data, Security Settings'
                    iconClass='w-[20px] lg:w-[28px]'
                    iconUrl='/svg/user-page/setting.svg'
                    onClick={() => router.push('/profile-setting')}
                    rootClass=' cursor-pointer'
                    title='Profile Setting'
                  />
                  <LinkContainer
                    caption='Update your bank account and UPI for using different services across the app'
                    iconClass='w-[14px] lg:w-[28px]'
                    iconUrl='/svg/user-page/bank.svg'
                    onClick={() => router.push('/bank-details')}
                    rootClass=' cursor-pointer'
                    title='Update Bank / UPI  Details'
                  />
                </div>
              </ContainerBlock>
            </div>
            <div className='grow-0 ml-[19px] overflow-hidden'>
              <SmartLink
                className='gap-y-1'
                href={'/refer-friends'}
                linkType={LinkType.INTERNAL}
              >
                <Image
                  alt='refer-a-freind-illustration'
                  className='hidden cursor-pointer xl:block h-auto hover:scale-[1.01] transition-all duration-300'
                  height={739}
                  src='/img/user-page/refer-friend-desktop.png'
                  width={929}
                />
                <p className='text-[8px] -mt-2 xl:flex hidden mb-[10px] w-full text-right text-white/80 font-medium ml-4'>
                  *T&C Apply
                </p>
              </SmartLink>
            </div>
          </div>
          <ContainerBlock rootClass='my-[10px] lg:pt-0' title='Knowledge Base'>
            <div className='flex flex-col lg:flex-row lg:flex-wrap gap-y-[5px] lg:gap-x-[19px] lg:gap-y-[21px] mt-[11px]'>
              <LinkContainer
                caption='Create tickets and check status'
                iconClass='w-[16px] lg:w-[31px]'
                iconUrl='/svg/user-page/ticket.svg'
                onClick={() => router.push('/report-missing-cashback-history')}
                rootClass=' cursor-pointer'
                title='My Tickets'
              />
              <LinkContainer
                caption='Chat with our executives, if you have any queries'
                iconClass='w-[19px] lg:w-[30px]'
                iconUrl='/svg/user-page/support.svg'
                onClick={() => router.push('https://tawk.to/indiancashback')}
                rootClass=' cursor-pointer'
                title='Support'
              />
              <LinkContainer
                caption='Frequently asked questions'
                iconClass='w-[17px] lg:w-[31px]'
                iconUrl='/svg/user-page/faq.svg'
                onClick={() => router.push('/')} //FIXME - route update
                rootClass=' cursor-pointer'
                title='FAQ'
              />
              <LinkContainer
                caption='Saved Products, Offers, Segments...Etc'
                iconClass='w-[17px] lg:w-[28px]'
                iconUrl='/svg/user-page/terms.svg'
                onClick={() => router.push('/terms-and-conditions')}
                rootClass=' cursor-pointer'
                title='Terms and Conditions'
              />
              <LinkContainer
                caption='Saved Products, Offers, Segments...Etc'
                iconClass='w-[12px] lg:w-[18px]'
                iconUrl='/svg/user-page/privacy.svg'
                onClick={() => router.push('/privacy-policies')}
                rootClass=' cursor-pointer'
                title='Privacy Policy'
              />
              <LinkContainer
                caption='Rate App, Tell us how to improve your services more!'
                iconClass='w-[20px] lg:w-[34px]'
                iconUrl='/svg/user-page/feedback.svg'
                onClick={() => router.push('/')} //FIXME - route update
                rootClass=' cursor-pointer'
                title='Feedback Corner'
              />
              <LinkContainer
                caption='Blogs related to all segments'
                iconClass='w-[15px] lg:w-[31px]'
                iconUrl='/svg/user-page/blogs.svg'
                onClick={() => router.push('https://indiancashback.com/blogs')}
                rootClass=' cursor-pointer'
                title='Blogs'
              />
            </div>
          </ContainerBlock>
          {/* <ContainerBlock rootClass='lg:pt-0' title='Subscriptions'>
            <div className='lg:grid lg:grid-cols-2 xl:grid-cols-3 gap-y-[20px] lg:mt-[14px] lg:bg-white dark:lg:bg-[#3E424C] lg:rounded-[10px] lg:py-[24px] lg:px-[20px] lg:shadow-sm'>
              <div className='flex items-center lg:border-r-[1px] border-[#BFBFBF] dark:border-[#595E6C] lg:pr-[15px] lg:mr-[15px] w-full lg:w-auto'>
                <Subscriptioncontainer
                  caption='Notification through SMS'
                  icon={
                    <SmsSVG className='text-primary w-[15px] lg:w-[29px]' />
                  }
                  title='SMS Notification'
                />
              </div>
              <div className='flex items-center xl:border-r-[1px] border-[#BFBFBF] dark:border-[#595E6C] xl:pr-[15px] xl:mr-[15px] w-full lg:w-auto'>
                <Subscriptioncontainer
                  caption='Notifications, Newsletters, Offers through mail'
                  icon={
                    <EmailSVG className='text-primary w-[15px] lg:w-[25px]' />
                  }
                  title='Email Notification'
                />
              </div>
              <div className='w-full lg:w-auto flex items-center lg:border-r-[1px] border-[#BFBFBF] dark:border-[#595E6C] lg:pr-[15px] lg:mr-[15px] xl:border-none xl:pr-0 xl:mr-0'>
                <Subscriptioncontainer
                  caption='Exclusive offers through Telegram'
                  icon={
                    <TelegramSVG className='text-primary w-[13px] lg:w-[22px]' />
                  }
                  title='Telegram Notification'
                />
              </div>
              <Subscriptioncontainer
                caption='Exclusive offers through WhatsApp '
                icon={
                  <TelegramSVG className='text-primary w-[15px] lg:w-[24px]' />
                }
                rootClass='xl:max-w-[355px]'
                title='WhatsApp Notification'
              />
            </div>
          </ContainerBlock> */}
          <ContainerBlock
            rootClass='mt-[10px] mb-[25px] lg:my-0 lg:pt-0 lg:pb-[100px]'
            title='Follow Us On'
            titleClass='lg:text-center'
          >
            <div className='mt-[14px] lg:h-[50px] lg:bg-white dark:lg:bg-[#3E424C] lg:rounded-[10px] lg:shadow-sm'>
              <motion.div
                animate={{ opacity: 1, y: 0 }}
                className='flex items-center pl-[10px] lg:justify-center gap-x-[20px] cursor-pointer h-full'
                initial={{ opacity: 0, y: 20 }}
                transition={{ duration: 0.5 }}
              >
                <motion.div
                  transition={{ type: 'spring', stiffness: 400, damping: 10 }}
                  whileHover={{ scale: 1.2, rotate: 5 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <SmartLink
                    href='https://t.me/indiancashbackofficial'
                    linkType={LinkType.EXTERNAL}
                    target='_blank'
                  >
                    <TelegramSVG className='text-primary w-[26px]' />
                  </SmartLink>
                </motion.div>
                <motion.div
                  transition={{ type: 'spring', stiffness: 400, damping: 10 }}
                  whileHover={{ scale: 1.2, rotate: -5 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <SmartLink
                    href='https://whatsapp.com/channel/0029VaLW0IoCcW4zFzzcqY23'
                    linkType={LinkType.EXTERNAL}
                    target='_blank'
                  >
                    <WhatsappSVG className='text-primary w-[26px]' />
                  </SmartLink>
                </motion.div>
                <motion.div
                  transition={{ type: 'spring', stiffness: 400, damping: 10 }}
                  whileHover={{ scale: 1.2, rotate: 5 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <SmartLink
                    href='https://www.instagram.com/indian_cashback/'
                    linkType={LinkType.EXTERNAL}
                    target='_blank'
                  >
                    <InstagramSVG className='text-primary w-[26px]' />
                  </SmartLink>
                </motion.div>
                <motion.div
                  transition={{ type: 'spring', stiffness: 400, damping: 10 }}
                  whileHover={{ scale: 1.2, rotate: -5 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <SmartLink
                    href='https://www.facebook.com/indiancashback'
                    linkType={LinkType.EXTERNAL}
                    target='_blank'
                  >
                    <FacebookSVG className='text-primary w-[26px]' />
                  </SmartLink>
                </motion.div>
                <motion.div
                  transition={{ type: 'spring', stiffness: 400, damping: 10 }}
                  whileHover={{ scale: 1.2, rotate: 5 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <SmartLink
                    href='https://www.youtube.com/indiancashback'
                    linkType={LinkType.EXTERNAL}
                    target='_blank'
                  >
                    <YoutubeSVG className='text-primary w-[28px]' />
                  </SmartLink>
                </motion.div>
              </motion.div>
            </div>
          </ContainerBlock>

          {isUserLogin && (
            <motion.button
              animate={{ opacity: 1 }}
              className='mt-[30px] text-blackWhite text-xs flex gap-x-[10px] ml-[8px] lg:hidden'
              initial={{ opacity: 0 }}
              onClick={logoutUser}
              transition={{ duration: 0.5, delay: 0.5 }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <LoginLogoutSVG className='w-[15px] text-blackWhite' /> Log Out
            </motion.button>
          )}
        </div>
      </motion.div>
    </motion.section>
  );
};

const ContainerBlock = ({
  title,
  children,
  rootClass,
  titleClass,
}: {
  title: string;
  children: React.ReactNode;
  rootClass?: string;
  titleClass?: string;
}) => {
  return (
    <div
      className={clsx(
        rootClass,
        'bg-container lg:bg-transparent lg:border-none pt-[15px] pb-[20px] px-[8px] lg:px-0 rounded-[10px] border-[0.5px] border-white dark:border-[#353943] shadow-sm lg:shadow-none'
      )}
    >
      <h4
        className={clsx(
          titleClass,
          'font-pat text-blackWhite text-[12px] ml-[14px] lg:text-[14px] font-normal capitalize'
        )}
      >
        {title}
      </h4>
      {children}
    </div>
  );
};

export default IndexClientsUser;
