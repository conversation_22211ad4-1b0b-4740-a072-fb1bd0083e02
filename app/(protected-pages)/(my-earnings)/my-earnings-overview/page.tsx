import React from 'react';
import IndexClientsMyEarnings from './index-clients';
import fetchWrapper from '@/utils/fetch-wrapper';
import { cookies } from 'next/headers';
import { BASE_URL } from '@/config';
import type { UserOverviewResponse } from '@/services/api/data-contracts';

const getEarningsOverview = () => {
  const cookieStore = cookies();
  const token = cookieStore.get('accessToken');
  return fetchWrapper<UserOverviewResponse>(`${BASE_URL}/users/overview`, {
    token: token?.value,
  });
};

const Page = async () => {
  let data: UserOverviewResponse;
  try {
    data = await getEarningsOverview();
  } catch (error) {
    return error;
  }
  return <IndexClientsMyEarnings data={data} />;
};

export default Page;
