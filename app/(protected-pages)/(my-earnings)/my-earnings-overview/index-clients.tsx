'use client';
import React from 'react';
import { motion } from 'framer-motion';
import CommonHeader from '@/app/components/headers/common-header';
import BreadcrumbSaveShare from '@/app/components/atoms/breadcrumb-container';
import MyEarningsSidenav from '@/app/components/my-earnings/my-earnings-sidenav';
import InfoHighligtedCard from '@/app/components/user-page-components/info-highligted-card';
import ApprovedSVG from '@/app/components/svg/approved';
import LinkContainerWithoutCaption, {
  LinkContainerMyEarnings,
} from '@/app/components/my-earnings/link-container';
import Support24SVG from '@/app/components/svg/support24';
import FaqSVG from '@/app/components/svg/faq';
import ReferFriendSVG from '@/app/components/svg/refer-freind';
import WalletAddSVG from '@/app/components/svg/wallet-add';
import MyEarningsChart from '@/app/components/my-earnings/my-earnings-chart';
import type { UserOverviewResponse } from '@/services/api/data-contracts';
import { formatIndRs } from '@/utils/helpers';
import { useRouter } from 'next/navigation';
import { CircleX, Gem, HandCoins } from 'lucide-react';
import PendingSVG from '@/app/components/svg/pending';

const IndexClientsMyEarnings = ({ data }: { data: UserOverviewResponse }) => {
  const router = useRouter();
  return (
    <>
      <CommonHeader headline='My Earnings' subHeading={<span>Overview</span>} />

      <motion.section
        animate={{ opacity: 1 }}
        className='max-w-[1280px] min-[1280px]:mx-auto'
        initial={{ opacity: 0 }}
        transition={{ duration: 0.5 }}
      >
        <motion.div
          animate={{ opacity: 1, y: 0 }}
          className='sticky top-[104px] z-[30]'
          initial={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <BreadcrumbSaveShare
            breadCrumbs={[
              { title: 'Cashback Home', link: '/' },
              { title: 'User Page', link: '/my-profile' },
              { title: 'My Earnings', link: '/' },
            ]}
          />
        </motion.div>
        <motion.div
          animate={{ opacity: 1 }}
          className='w-full flex'
          initial={{ opacity: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <MyEarningsSidenav activeNavId={1} />
          <motion.div
            animate={{ opacity: 1, x: 0 }}
            className='bg-[#E0E0E0] dark:bg-[#1F222A] w-full ml-[2px] pb-[40px] px-[6px]'
            initial={{ opacity: 0, x: 20 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <motion.div
              animate={{ opacity: 1, y: 0 }}
              className='bg-container pt-[14px] px-[10px] pb-[30px] lg:pt-[24px] border-[0.5px] border-white dark:border-[#353943] rounded-[10px] max-w-[950px] mx-auto mt-[23px]'
              initial={{ opacity: 0, y: 20 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              whileHover={{ boxShadow: '0px 8px 30px rgba(0, 0, 0, 0.1)' }}
            >
              <motion.div
                animate={{ opacity: 1 }}
                className='flex items-center justify-between px-[7px] lg:px-0 text-blackWhite'
                initial={{ opacity: 0 }}
                transition={{ duration: 0.5, delay: 0.5 }}
              >
                <motion.h4
                  animate={{ opacity: 1, x: 0 }}
                  className='text-xs lg:text-sm font-pat'
                  initial={{ opacity: 0, x: -10 }}
                  transition={{ duration: 0.5, delay: 0.6 }}
                >
                  My Earnings
                </motion.h4>
              </motion.div>

              <motion.div
                animate={{ opacity: 1, y: 0 }}
                className='flex justify-center gap-x-[6px] lg:gap-x-[14px] mt-[13px] lg:mt-[6px]'
                initial={{ opacity: 0, y: 20 }}
                transition={{ duration: 0.5, delay: 0.7 }}
              >
                <InfoHighligtedCard
                  caption='Pending'
                  icon={<PendingSVG className='text-[#FFC554] w-[15px]' />}
                  number={data.totalPendingCount}
                  onClick={() =>
                    router.push('/cashback-history?status=pending')
                  }
                  stripColorClass='bg-[#FFC554]'
                />
                <InfoHighligtedCard
                  caption='Approved'
                  icon={<ApprovedSVG className='text-[#69C8B4] w-[15px]' />}
                  number={data.totalApprovedCount}
                  onClick={() =>
                    router.push('/cashback-history?status=approved')
                  }
                  stripColorClass='bg-[#69C8B4]'
                />
                <InfoHighligtedCard
                  caption='Cancelled'
                  icon={<CircleX className='text-[#F06B6B] w-[15px]' />}
                  number={data.totalCancelledCount}
                  onClick={() =>
                    router.push('/cashback-history?status=cancelled')
                  }
                  stripColorClass='bg-[#F06B6B]'
                />
              </motion.div>
              <motion.div
                animate={{ opacity: 1 }}
                className='flex flex-col items-center md:grid md:grid-cols-2 justify-items-center mt-[20px] gap-y-[7px] md:gap-[15px]'
                initial={{ opacity: 0 }}
                transition={{ duration: 0.5, delay: 0.8 }}
              >
                <LinkContainerMyEarnings
                  amount={formatIndRs(data.totalCashbackEarned)}
                  icon={
                    <HandCoins className='w-[12px] lg:w-[18px] text-[#818181] dark:text-white' />
                  }
                  onClick={() => router.push('/cashback-history')}
                  title='Total Cashback Earned'
                />
                <LinkContainerMyEarnings
                  amount={formatIndRs(data.flipkartRewardPoints)}
                  icon={
                    <Gem className='w-[12px] lg:w-[25px] text-[#818181] dark:text-white' />
                  }
                  title='Total Reward Points Earned'
                />
                <LinkContainerMyEarnings
                  amount={formatIndRs(data.readyToWithdraw)}
                  icon={
                    <WalletAddSVG className='w-[12px] lg:w-[25px] text-[#818181] dark:text-white' />
                  }
                  onClick={() =>
                    router.push('/cashback-history?status=confirmed')
                  }
                  title='Ready to Withdraw'
                />
                <LinkContainerMyEarnings
                  amount={formatIndRs(data.totalReferralCommission)}
                  icon={
                    <ReferFriendSVG className='w-[14px] lg:w-[19px] text-[#818181] dark:text-white' />
                  }
                  onClick={() => router.push('/referral-history')}
                  title='Total Referral Commission Earned'
                />
              </motion.div>
            </motion.div>
            <motion.div
              animate={{ opacity: 1, y: 0 }}
              initial={{ opacity: 0, y: 20 }}
              transition={{ duration: 0.5, delay: 0.9 }}
              whileHover={{ boxShadow: '0px 8px 30px rgba(0, 0, 0, 0.1)' }}
            >
              <MyEarningsChart
                monthlyCashback={data.totalCashback}
                monthlyClicks={data.totalClicks}
                monthlyOrderAmount={data.totalOrderAmount}
              />
            </motion.div>
            <motion.div
              animate={{ opacity: 1, y: 0 }}
              className='flex flex-col md:flex-row md:justify-center gap-y-[13px] md:gap-x-[20px] mt-[30px]'
              initial={{ opacity: 0, y: 20 }}
              transition={{ duration: 0.5, delay: 1 }}
            >
              <LinkContainerWithoutCaption
                icon={
                  <FaqSVG className='w-[15px] lg:w-[24px] text-[#818181] dark:text-white' />
                }
                onClick={() => router.push('/faqs')}
                title='Related Question and Answers'
              />
              <LinkContainerWithoutCaption
                icon={
                  <Support24SVG className='w-[12px] lg:w-[20px] text-[#818181] dark:text-white' />
                }
                onClick={() => {
                  router.push('https://tawk.to/indiancashback/');
                }}
                title='Support'
              />
            </motion.div>
          </motion.div>
        </motion.div>
      </motion.section>
    </>
  );
};

export default IndexClientsMyEarnings;
