import React from 'react';
import IndexClientsReferralHistory from './index-clients';
import { GetUsersByReferralCodeResponse } from '@/services/api/data-contracts';
import { CustomSearchParamsTypes } from '@/types/global-types';
import { cookies } from 'next/headers';
import fetchWrapper from '@/utils/fetch-wrapper';
import { BASE_URL } from '@/config';

async function getReferralHistoryData(searchParams: CustomSearchParamsTypes) {
  const {
    searchParam,
    sortType = 'newest',
    status = 'active',
    page,
    pageSize,
    startDate,
    endDate,
  } = searchParams;

  const queryParams = Object.entries({
    searchParam,
    sortType,
    status,
    page,
    pageSize,
    startDate,
    endDate,
  })
    .filter(([_, value]) => value) //eslint-disable-line
    .map(([key, value]) => `${key}=${value}`)
    .join('&');

  const cookieStore = cookies();
  const token = cookieStore.get('accessToken');
  return await fetchWrapper<GetUsersByReferralCodeResponse>(
    `${BASE_URL}/users/referral-history?${queryParams}`,
    {
      token: token?.value,
    }
  );
}

const Page = async ({
  searchParams,
}: {
  searchParams: CustomSearchParamsTypes;
}) => {
  let resData: GetUsersByReferralCodeResponse;
  //TO DO - add types for clickedStores later
  try {
    resData = await getReferralHistoryData(searchParams);
  } catch (err: any) {
    console.log({ err });
    return err;
  }

  return <IndexClientsReferralHistory data={resData} />;
};

export default Page;

export const dynamic = 'force-dynamic';
