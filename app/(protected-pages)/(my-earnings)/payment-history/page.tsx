import React from 'react';
import IndexClientsPaymentHistory from './index-clients';
import { cookies } from 'next/headers';
import fetchWrapper from '@/utils/fetch-wrapper';
import { BASE_URL } from '@/config';
import { GetPaymentListResponse } from '@/services/api/data-contracts';

const getPaymentHistory = async (searchParams: any) => {
  const {
    searchParam,
    sortType = 'newest',
    status,
    page,
    pageSize,
    startDate,
    endDate,
  } = searchParams;

  const queryParams = Object.entries({
    searchParam,
    sortType,
    status,
    page,
    pageSize,
    startDate,
    endDate,
  })
    .filter(([_, value]) => value) //eslint-disable-line
    .map(([key, value]) => `${key}=${value}`)
    .join('&');

  const cookieStore = cookies();
  const token = cookieStore.get('accessToken');
  return fetchWrapper<GetPaymentListResponse>(
    `${BASE_URL}/payment?${queryParams}`,
    {
      token: token?.value,
      suppressToast: true,
    }
  );
};

const Page = async ({ searchParams }: { searchParams: any }) => {
  let data: GetPaymentListResponse;
  try {
    data = await getPaymentHistory(searchParams);
  } catch (error) {
    return error;
  }
  return <IndexClientsPaymentHistory data={data} />;
};

export default Page;
