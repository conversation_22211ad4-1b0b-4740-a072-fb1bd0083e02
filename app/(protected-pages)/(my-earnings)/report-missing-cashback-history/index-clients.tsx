'use client';
import BreadcrumbSaveShare from '@/app/components/atoms/breadcrumb-container';
import CommonHeader from '@/app/components/headers/common-header';
import MyEarningsSidenav from '@/app/components/my-earnings/my-earnings-sidenav';
import MyEarningsToolbar from '@/app/components/my-earnings/my-earnings-toolbar';
import ReportMissingCbHistory from '@/app/components/my-earnings/report-missing-cb-history';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import { setSelectedSort } from '@/redux/slices/common-filters-slice';
import {
  setHideSearchFilter,
  setHideSortFilter,
  setShowStoresFilter,
  setSingleDatePicker,
  setSortItems,
  setStatusList,
  setStoresList,
  setTitle,
  setTotalFiltersApplied,
} from '@/redux/slices/earnings-toolbar-slice';
import { setSearchValue } from '@/redux/slices/global-search-slice';
import type {
  MissingCashbackResponse,
  SortTypes,
} from '@/services/api/data-contracts';
import {
  useCreateMultiQueryString,
  useCreateQueryString,
} from '@/utils/custom-hooks';
import { ConfigProvider, type MenuProps, Pagination, theme } from 'antd';
import { useTheme } from 'next-themes';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import type { ClickedStores } from '../click-history/page';
import type { RootState } from '@/redux/store';
import NoData from '@/app/components/no-data';
import { motion, AnimatePresence } from 'framer-motion';

const sortItems = [
  {
    label: 'Newest',
    key: 'newest',
  },
  {
    label: 'Oldest',
    key: 'oldest',
  },
];

const statusList = ['not-solved', 'solved', 'forwarded', 'rejected'];

const IndexClientsReportMissingCBHistory = ({
  data,
  clickedStores,
}: {
  data: MissingCashbackResponse;
  clickedStores?: ClickedStores[];
}) => {
  const { resolvedTheme } = useTheme();
  const dispatch = useAppDispatch();
  const { replace } = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const createMultiQueryString = useCreateMultiQueryString(searchParams);
  const createQueryString = useCreateQueryString(searchParams);

  const [selectedStores, setSelectedStores] = useState<string[]>([]);
  const [selectedStatus, setSelectedStatus] = useState<string[]>([]);

  const { selectedSort, searchValue } = useAppSelector(
    (state: RootState) => state.commonFilters
  );

  const onClickSortBy: MenuProps['onClick'] = ({ key }) => {
    replace(pathname + '?' + createQueryString('sortType', key));
  };

  const onApply = async (options?: {
    selectedStores?: string[];
    selectedStatus?: string[];
  }) => {
    const { selectedStores = [], selectedStatus = [] } = options ?? {};

    const hasSelectedStores = selectedStores.length > 0;
    const hasSelectedStatus = selectedStatus.length > 0;

    if (!hasSelectedStores && !hasSelectedStatus) {
      // Clear existing filters
      replace(
        pathname +
          '?' +
          createMultiQueryString([
            { name: 'stores', value: '' },
            { name: 'status', value: '' },
          ])
      );
      return;
    }

    const selectedStoreUids = hasSelectedStores
      ? selectedStores.map(
          (storeName) =>
            clickedStores
              ?.find((store) => store.name === storeName)
              ?.uid?.toString() || ''
        )
      : [];

    const queries = [
      {
        name: 'stores',
        value: hasSelectedStores ? selectedStoreUids.join(',') : '',
      },
      {
        name: 'status',
        value: hasSelectedStatus
          ? selectedStatus.join(',').toLocaleLowerCase()
          : '',
      },
    ];

    replace(pathname + '?' + createMultiQueryString(queries));
  };

  const onClear = () => {
    const queries = [
      { name: 'stores', value: '' },
      { name: 'status', value: '' },
    ];

    const queryString = createMultiQueryString(queries);
    replace(pathname + '?' + queryString);
  };

  useEffect(() => {
    let filterCount = 0;
    if (selectedStores?.length > 0) {
      filterCount += selectedStores.length;
    }
    if (selectedStatus.length > 0) {
      filterCount += selectedStatus.length;
    }

    dispatch(setTotalFiltersApplied(filterCount));
  }, [selectedStores, selectedStatus, dispatch]);

  //set earnings toolbar config
  useEffect(() => {
    dispatch(setSortItems(sortItems));
    dispatch(setTitle('Report Missing Cb History'));
    dispatch(setStoresList(clickedStores ? clickedStores : []));
    dispatch(setStatusList(statusList));
    dispatch(setShowStoresFilter(true));
    dispatch(setHideSearchFilter(false));
    dispatch(setHideSortFilter(false));
    dispatch(setSingleDatePicker(false));
  }, [dispatch, clickedStores, pathname]);

  useEffect(() => {
    const searchParam = searchParams.get('searchParam');
    if (searchParam) {
      dispatch(setSearchValue(searchParam));
    }

    dispatch(setSelectedSort(searchParams.get('sortType') as SortTypes) ?? '');

    const storesSelected = searchParams.get('stores')?.split(',');

    const selectedStoreNames = storesSelected?.length
      ? storesSelected.map((storeId) => {
          const store =
            clickedStores &&
            clickedStores.find(
              (store) => store.uid === Number.parseInt(storeId)
            );
          return store ? store.name : '';
        })
      : [];

    setSelectedStores(selectedStoreNames);

    setSelectedStatus(searchParams.get('status')?.split(',') ?? []);
  }, [searchParams, dispatch, clickedStores, pathname]);

  return (
    <>
      <CommonHeader
        headline='Report Missing Cashback History'
        subHeading={<span>Overview</span>}
      />

      <motion.section
        animate={{ opacity: 1 }}
        className='max-w-[1280px] min-[1280px]:mx-auto'
        initial={{ opacity: 0 }}
        onClick={(e) => {
          e.stopPropagation();
        }}
        transition={{ duration: 0.5 }}
      >
        <motion.div
          animate={{ opacity: 1, y: 0 }}
          className='sticky top-[104px] z-[30]'
          initial={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <BreadcrumbSaveShare
            breadCrumbs={[
              { title: 'Cashback Home', link: '/' },
              { title: 'User Page', link: '/my-profile' },
              { title: 'Report Missing Cashback History', link: '/' },
            ]}
          />
        </motion.div>
        <motion.div
          animate={{ opacity: 1 }}
          className='w-full flex z-[0] relative'
          initial={{ opacity: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <motion.div
            animate={{ opacity: 1, x: 0 }}
            initial={{ opacity: 0, x: -30 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <MyEarningsSidenav activeNavId={6} />
          </motion.div>
          <motion.div
            animate={{ opacity: 1, x: 0 }}
            className='bg-[#E0E0E0] dark:bg-[#1F222A] ml-[2px] pb-[80px] w-[calc(100%-75px)] lg:w-[calc(100%-273px)]'
            initial={{ opacity: 0, x: 30 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <motion.div
              animate={{ opacity: 1, y: 0 }}
              initial={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.5, delay: 0.5 }}
            >
              <MyEarningsToolbar
                onApply={onApply}
                onClear={onClear}
                onClickSortBy={onClickSortBy}
                searchKey={searchValue}
                selectedSort={selectedSort}
                selectedStatusArray={selectedStatus}
                selectedStoreArray={selectedStores}
              />
            </motion.div>
            <motion.div
              animate={{ opacity: 1 }}
              className='pt-[12px] px-[6px] lg:px-[15px] xl:px-[30px] flex flex-col gap-y-[10px]'
              initial={{ opacity: 0 }}
              transition={{ duration: 0.5, delay: 0.6 }}
            >
              <AnimatePresence>
                {data && data?.pagination?.pageSize > 0 ? (
                  data.missingCashbacks.map((item, index) => (
                    <motion.div
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      initial={{ opacity: 0, y: 20 }}
                      key={item.uid}
                      transition={{ duration: 0.3, delay: 0.7 + index * 0.05 }}
                    >
                      <ReportMissingCbHistory data={item} key={item.uid} />
                    </motion.div>
                  ))
                ) : (
                  <motion.div
                    animate={{ opacity: 1 }}
                    initial={{ opacity: 0 }}
                    transition={{ duration: 0.5, delay: 0.7 }}
                  >
                    <NoData imgClass='!w-[200px] !h-[200px] lg:!w-[300px] lg:!h-[300px]' />
                  </motion.div>
                )}
              </AnimatePresence>

              {data && data?.pagination?.pageSize > 0 && (
                <motion.div
                  animate={{ opacity: 1, y: 0 }}
                  className='flex-center w-full my-[50px]'
                  initial={{ opacity: 0, y: 20 }}
                  transition={{ duration: 0.5, delay: 0.8 }}
                >
                  <ConfigProvider
                    theme={{
                      algorithm:
                        resolvedTheme === 'dark'
                          ? theme.darkAlgorithm
                          : theme.defaultAlgorithm,
                    }}
                  >
                    <Pagination
                      defaultCurrent={1}
                      defaultPageSize={15}
                      onChange={(pageNumber, pageSize) =>
                        replace(
                          pathname +
                            '?' +
                            createMultiQueryString([
                              {
                                name: 'page',
                                value: pageNumber.toString(),
                              },
                              {
                                name: 'pageSize',
                                value: pageSize.toString(),
                              },
                            ])
                        )
                      }
                      total={data.pagination.total}
                    />
                  </ConfigProvider>
                </motion.div>
              )}
            </motion.div>
          </motion.div>
        </motion.div>
      </motion.section>
    </>
  );
};

export default IndexClientsReportMissingCBHistory;
