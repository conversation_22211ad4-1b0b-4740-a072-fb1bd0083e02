import React from 'react';
import IndexClientsReportMissingCBHistory from './index-clients';
import {
  MissingCashbackControllerListMissingCashbackParams,
  MissingCashbackResponse,
} from '@/services/api/data-contracts';
import { cookies } from 'next/headers';
import fetchWrapper from '@/utils/fetch-wrapper';
import { BASE_URL } from '@/config';
import { ClickedStores } from '../click-history/page';

interface customSearchParams
  extends MissingCashbackControllerListMissingCashbackParams {
  page: number;
  pageSize: number;
}

const getCashbackHistory = async (searchParams: customSearchParams) => {
  const {
    searchParam = '',
    sortType = 'newest',
    status = '',
    stores = '',
    page = '1',
    pageSize = '15',
    startDate = '',
    endDate = '',
  } = searchParams;

  let queryParams = `page=${page}&pageSize=${pageSize}&sortType=${sortType}&searchParam=${searchParam}`;
  if (status !== '') {
    queryParams += `&status=${status}`;
  }
  if (startDate && endDate) {
    queryParams += `&startDate=${startDate}&endDate=${endDate}`;
  }
  if (stores !== '') {
    queryParams += `&stores=${stores}`;
  }
  const cookieStore = cookies();
  const token = cookieStore.get('accessToken');
  return fetchWrapper<MissingCashbackResponse>(
    `${BASE_URL}/click/missing-cashback/list?${queryParams}`,
    {
      token: token?.value,
    }
  );
};

async function getClickedStores() {
  const cookieStore = cookies();
  const token = cookieStore.get('accessToken');
  return await fetchWrapper<ClickedStores[]>(
    `${BASE_URL}/click/clicked_stores`,
    {
      token: token?.value,
    }
  );
}

const Page = async ({ searchParams }: { searchParams: any }) => {
  let data: MissingCashbackResponse;
  let clickedStores: ClickedStores[];
  try {
    data = await getCashbackHistory(searchParams);
    clickedStores = await getClickedStores();
  } catch (error) {
    return error;
  }
  return (
    <IndexClientsReportMissingCBHistory
      clickedStores={clickedStores}
      data={data}
    />
  );
};

export default Page;
