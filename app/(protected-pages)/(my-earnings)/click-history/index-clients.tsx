'use client';
import BreadcrumbSaveShare from '@/app/components/atoms/breadcrumb-container';
import CommonHeader from '@/app/components/headers/common-header';
import ClickHistoryComponent from '@/app/components/my-earnings/click-history-cont';
import MyEarningsSidenav from '@/app/components/my-earnings/my-earnings-sidenav';
import MyEarningsToolbar from '@/app/components/my-earnings/my-earnings-toolbar';
import type { ClicksResponse, SortTypes } from '@/services/api/data-contracts';
import {
  useCreateMultiQueryString,
  useCreateQueryString,
} from '@/utils/custom-hooks';
import { ConfigProvider, type MenuProps, Pagination, theme } from 'antd';
import { useTheme } from 'next-themes';
import { usePathname, useSearchParams } from 'next/navigation';
import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import type { ClickedStores } from './page';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import NoData from '@/app/components/no-data';
import { setSelectedSort } from '@/redux/slices/common-filters-slice';
import {
  setHideSearchFilter,
  setHideSortFilter,
  setSingleDatePicker,
  setSortItems,
  setStatusList,
  setStoresList,
  setTitle,
  setTotalFiltersApplied,
} from '@/redux/slices/earnings-toolbar-slice';
import { setSearchValue } from '@/redux/slices/common-filters-slice';
import { motion, AnimatePresence } from 'framer-motion';

export const sortItems = [
  {
    label: 'Newest',
    key: 'newest',
  },
  {
    label: 'Discount',
    key: 'discount',
  },
];

const statusList = [
  'confirmed',
  'pending',
  'cancelled',
  'tracked',
  'report missing cb',
];

const IndexClientsClickHistory = ({
  data,
  clickedStores,
}: {
  data?: ClicksResponse;
  clickedStores?: ClickedStores[];
}) => {
  const { resolvedTheme } = useTheme();
  const dispatch = useAppDispatch();
  const { replace } = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const createMultiQueryString = useCreateMultiQueryString(searchParams);
  const createQueryString = useCreateQueryString(searchParams);

  const [selectedStores, setSelectedStores] = useState<string[]>([]);
  const [selectedStatus, setSelectedStatus] = useState<string[]>([]);

  const { selectedSort, searchValue } = useAppSelector(
    (state) => state.commonFilters
  );

  const onClickSortBy: MenuProps['onClick'] = ({ key }) => {
    replace(pathname + '?' + createQueryString('sortType', key));
  };

  const onApply = async (options?: {
    selectedStores?: string[];
    selectedStatus?: string[];
  }) => {
    const { selectedStores = [], selectedStatus = [] } = options ?? {};

    const hasSelectedStores = selectedStores.length > 0;
    const hasSelectedStatus = selectedStatus.length > 0;

    if (!hasSelectedStores && !hasSelectedStatus) {
      // Clear existing filters
      replace(
        pathname +
          '?' +
          createMultiQueryString([
            { name: 'stores', value: '' },
            { name: 'status', value: '' },
          ])
      );
      return;
    }

    const selectedStoreUids = hasSelectedStores
      ? selectedStores.map(
          (storeName) =>
            clickedStores
              ?.find((store) => store.name === storeName)
              ?.uid?.toString() || ''
        )
      : [];

    const queries = [
      {
        name: 'stores',
        value: hasSelectedStores ? selectedStoreUids.join(',') : '',
      },
      {
        name: 'status',
        value: hasSelectedStatus
          ? selectedStatus.join(',').toLocaleLowerCase()
          : '',
      },
    ];

    replace(pathname + '?' + createMultiQueryString(queries));
  };

  const onClear = () => {
    const queries = [
      { name: 'stores', value: '' },
      { name: 'status', value: '' },
    ];

    const queryString = createMultiQueryString(queries);
    replace(pathname + '?' + queryString);
  };

  useEffect(() => {
    let filterCount = 0;
    if (selectedStores?.length > 0) {
      filterCount += selectedStores.length;
    }
    if (selectedStatus.length > 0) {
      filterCount += selectedStatus.length;
    }

    dispatch(setTotalFiltersApplied(filterCount));
  }, [selectedStores, selectedStatus, dispatch, searchParams]);

  //set earnings toolbar config
  useEffect(() => {
    dispatch(setSortItems(sortItems));
    dispatch(setTitle('Click History'));
    dispatch(setStoresList(clickedStores ? clickedStores : []));
    dispatch(setStatusList(statusList));
    dispatch(setHideSearchFilter(false));
    dispatch(setHideSortFilter(false));
    dispatch(setSingleDatePicker(false));
  }, [dispatch, clickedStores]);

  useEffect(() => {
    const searchParam = searchParams.get('searchParam');
    if (searchParam) {
      dispatch(setSearchValue(searchParam));
    }

    dispatch(setSelectedSort(searchParams.get('sortType') as SortTypes) ?? '');

    const storesSelected = searchParams.get('stores')?.split(',');

    const selectedStoreNames = storesSelected?.length
      ? storesSelected.map((storeId) => {
          const store =
            clickedStores &&
            clickedStores.find(
              (store) => store.uid === Number.parseInt(storeId)
            );
          return store ? store.name : '';
        })
      : [];

    setSelectedStores(selectedStoreNames);

    setSelectedStatus(searchParams.get('status')?.split(',') ?? []);
  }, [searchParams, dispatch, clickedStores, pathname]);

  const formatTime = (isoString: string) => {
    const date = new Date(isoString);
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    });
  };

  return (
    <>
      <CommonHeader
        headline='Click History'
        subHeading={<span>Overview</span>}
      />

      <motion.section
        animate={{ opacity: 1 }}
        className='max-w-[1280px] min-[1280px]:mx-auto'
        initial={{ opacity: 0 }}
        onClick={(e) => {
          e.stopPropagation();
        }}
        transition={{ duration: 0.5 }}
      >
        <motion.div
          animate={{ opacity: 1, y: 0 }}
          className='sticky top-[104px] z-[30]'
          initial={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <BreadcrumbSaveShare
            breadCrumbs={[
              { title: 'Cashback Home', link: '/' },
              { title: 'User Page', link: '/user' },
              { title: 'Payment History', link: '/' },
            ]}
          />
        </motion.div>
        <motion.div
          animate={{ opacity: 1 }}
          className='w-full flex z-[0] relative'
          initial={{ opacity: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <motion.div
            animate={{ opacity: 1, x: 0 }}
            initial={{ opacity: 0, x: -30 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <MyEarningsSidenav activeNavId={7} />
          </motion.div>
          <motion.div
            animate={{ opacity: 1, x: 0 }}
            className='bg-[#E0E0E0] dark:bg-[#1F222A] ml-[2px] pb-[80px] w-[calc(100%-75px)] lg:w-[calc(100%-273px)]'
            initial={{ opacity: 0, x: 30 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <motion.div
              animate={{ opacity: 1, y: 0 }}
              initial={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.5, delay: 0.5 }}
            >
              <MyEarningsToolbar
                onApply={onApply}
                onClear={onClear}
                onClickSortBy={onClickSortBy}
                searchKey={searchValue}
                selectedSort={selectedSort}
                selectedStatusArray={selectedStatus}
                selectedStoreArray={selectedStores}
              />
            </motion.div>
            <AnimatePresence>
              {data && data?.clicks?.length > 0 ? (
                <>
                  <motion.div
                    animate={{ opacity: 1 }}
                    className='pt-[12px] px-[6px] lg:px-[15px] xl:px-[30px] flex flex-col gap-y-[10px]'
                    initial={{ opacity: 0 }}
                    transition={{ duration: 0.5, delay: 0.6 }}
                  >
                    {data && data?.clicks?.length > 0 ? (
                      data?.clicks?.map((item, index) => (
                        <motion.div
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -20 }}
                          initial={{ opacity: 0, y: 20 }}
                          key={index}
                          transition={{
                            duration: 0.3,
                            delay: 0.7 + index * 0.05,
                          }}
                          whileHover={{
                            scale: 1.01,
                            boxShadow: '0px 4px 15px rgba(0, 0, 0, 0.1)',
                          }}
                        >
                          <ClickHistoryComponent
                            clickDocId={item?.id}
                            clickId={item.referenceId}
                            date={item.date}
                            fromPreviousMonth={item.fromPreviousMonth}
                            key={index}
                            name={item.name}
                            status={item.status}
                            storeLogo={item.logo}
                            time={formatTime(item.createdAt)}
                          />
                        </motion.div>
                      ))
                    ) : (
                      <motion.div
                        animate={{ opacity: 1 }}
                        initial={{ opacity: 0 }}
                        transition={{ duration: 0.5, delay: 0.7 }}
                      >
                        <NoData imgClass='!w-[200px] !h-[200px] lg:!w-[300px] lg:!h-[300px]' />
                      </motion.div>
                    )}

                    {data && data?.pagination?.pageSize > 0 && (
                      <motion.div
                        animate={{ opacity: 1, y: 0 }}
                        className='flex-center w-full my-[50px]'
                        initial={{ opacity: 0, y: 20 }}
                        transition={{ duration: 0.5, delay: 0.8 }}
                      >
                        <ConfigProvider
                          theme={{
                            algorithm:
                              resolvedTheme === 'dark'
                                ? theme.darkAlgorithm
                                : theme.defaultAlgorithm,
                          }}
                        >
                          <Pagination
                            defaultCurrent={1}
                            defaultPageSize={15}
                            onChange={(pageNumber, pageSize) =>
                              replace(
                                pathname +
                                  '?' +
                                  createMultiQueryString([
                                    {
                                      name: 'page',
                                      value: pageNumber.toString(),
                                    },
                                    {
                                      name: 'pageSize',
                                      value: pageSize.toString(),
                                    },
                                  ])
                              )
                            }
                            total={data.pagination.total}
                          />
                        </ConfigProvider>
                      </motion.div>
                    )}
                  </motion.div>
                </>
              ) : (
                <motion.div
                  animate={{ opacity: 1 }}
                  className='pt-[12px] px-[6px] lg:px-[15px] xl:px-[30px] gap-y-[10px]'
                  initial={{ opacity: 0 }}
                  transition={{ duration: 0.5, delay: 0.6 }}
                >
                  <NoData imgClass='!w-[200px] !h-[200px] lg:!w-[300px] lg:!h-[300px]' />
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        </motion.div>
      </motion.section>
    </>
  );
};

export default IndexClientsClickHistory;
