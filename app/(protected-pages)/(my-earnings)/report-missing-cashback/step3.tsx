import { TextAreaNormal } from '@/app/components/atoms/form-inputs';
import { useAppSelector } from '@/redux/hooks';
import React from 'react';

const RmcStep3 = ({
  register,
  validationError,
}: {
  register: any;
  validationError: any;
}) => {
  const { reportMissingStep } = useAppSelector(
    (state) => state.reportMissingCbStep
  );

  if (reportMissingStep !== 3) {
    return;
  }

  return (
    <div className='pl-[8px]'>
      <h4 className='text-[11px] font-medium text-blackWhite'>Message</h4>

      <TextAreaNormal
        inputClass='!w-full max-w-none !h-[190px]'
        inputHeight={90}
        name='message'
        placeholder='Please tell us more about the order, like category, purchase type and ...etc'
        register={register}
        rootClass='w-full mt-[18px]'
        validationSchema={{
          required: 'Message is required',
          pattern: {
            value: /^[a-zA-Z0-9\s,.'-]+$/,
            message: 'Invalid  Message',
          },
        }}
      />
      {validationError?.message?.message && (
        <span className='text-[#F00] text-[10px] font-light'>
          {validationError?.message?.message?.toString()}
        </span>
      )}
    </div>
  );
};

export default RmcStep3;
