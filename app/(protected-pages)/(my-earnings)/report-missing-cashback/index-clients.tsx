'use client';
import BreadcrumbSaveShare from '@/app/components/atoms/breadcrumb-container';
import ThemeButton from '@/app/components/atoms/theme-btn';
import CommonHeader from '@/app/components/headers/common-header';
import CustomSteps from '@/app/components/misc/custom-steps';
import MyEarningsSidenav from '@/app/components/my-earnings/my-earnings-sidenav';
import WarningIcon from '@/app/components/svg/warning-icon';
import Image from 'next/image';
import React, { useEffect, useState } from 'react';
import RmcStep1 from './step1';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import { motion, AnimatePresence } from 'framer-motion';
import RmcStep2 from './step2';
import DateIcon from '@/app/components/svg/date-icon';
import Edit from '@/app/components/svg/edit';
import {
  setClickId,
  setComplaintId,
  setIsCouponUsed,
  setReportMissingStep,
  setReportingPlatform,
  setReportingUserType,
} from '@/redux/slices/report-missing-cb-slice';
import RightArrow from '@/app/components/svg/right-arrow';
import RmcStep3 from './step3';
import RmcStep4 from './step4';
import type { ClickedStores } from '../click-history/page';
import MyEarningsToolbar from '@/app/components/my-earnings/my-earnings-toolbar';
import dayjs from 'dayjs';
import { useForm } from 'react-hook-form';
import type { DatePickerProps, MenuProps, UploadFile, UploadProps } from 'antd';
import fetchWrapper from '@/utils/fetch-wrapper';
import { LoadingGif } from '@/app/components/misc/loading-components';
import {
  setHideSearchFilter,
  setHideSortFilter,
  setSingleDatePicker,
  setSortItems,
  setStatusList,
  setStoresList,
  setTitle,
  setTotalFiltersApplied,
} from '@/redux/slices/earnings-toolbar-slice';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import {
  useCreateMultiQueryString,
  useCreateQueryString,
} from '@/utils/custom-hooks';
import {
  setSelectedDate,
  setSelectedSort,
} from '@/redux/slices/common-filters-slice';
import type { SortTypes } from '@/services/api/data-contracts';
export type ReportMissingErrorsState = {
  isCouponUsed: string;
  invoice: string;
  platform: string;
  userType: string;
  [key: string]: string;
};

const allowedExtensions = ['png', 'jpg', 'jpeg', 'webp', 'pdf', 'docx', 'doc'];
const maxFileSizeMB = 2;

export const sortItems = [
  {
    label: 'Newest',
    key: 'newest',
  },
  {
    label: 'Discount',
    key: 'discount',
  },
];

const statusList = [
  'Confirmed',
  'Pending',
  'Cancelled',
  'Tracked',
  'Report Missing CB',
];

const IndexClientsReportMissingCashback = ({
  data,
  clickedStores,
}: {
  data: any;
  clickedStores?: ClickedStores[];
}) => {
  const [isShowSteps, setShowSteps] = useState(true);
  const [isLoading, setIsLoading] = useState(false);

  const [selectedStores, setSelectedStores] = useState<string[]>([]);
  const [selectedStatus, setSelectedStatus] = useState<string[]>([]);

  const { replace } = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();

  const createQueryString = useCreateQueryString(searchParams);
  const createMultiQueryString = useCreateMultiQueryString(searchParams);

  const [errors, setErrors] = useState<ReportMissingErrorsState>({
    isCouponUsed: '',
    invoice: '',
    platform: '',
    userType: '',
  });

  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const {
    reportMissingStep,
    clickId,
    selectedStoreLogo,
    clickTime,
    clickDate,
    reportingUserType,
    reportingPlatform,
    isCouponUsed,
  } = useAppSelector((state) => state.reportMissingCbStep);

  const {
    watch: watchOrderDetails,
    reset: resetOrderDetails,
    register: registerOrderDetails,
    trigger: triggerOrderValidation,
    formState: { errors: orderDetailsError },
  } = useForm({
    defaultValues: {
      orderId: '',
      orderAmount: null,
    },
  });

  const {
    watch: watchMessage,
    reset: resetMessage,
    register: registerMessage,
    trigger: triggerMessageValidation,
    formState: { errors: messageError },
  } = useForm({
    defaultValues: {
      message: '',
    },
  });

  const props: UploadProps = {
    onRemove: (file) => {
      const index = fileList.indexOf(file);
      const newFileList = fileList.slice();
      newFileList.splice(index, 1);
      setFileList(newFileList);
    },
    beforeUpload: (file: any) => {
      const fileExtension = file?.name?.split('.').pop().toLowerCase() ?? '';
      const isItValidType = allowedExtensions.includes(fileExtension);
      if (!isItValidType) {
        setErrors((prevErrors: ReportMissingErrorsState) => ({
          ...prevErrors,
          invoice:
            'File type not allowed. Only PNG, JPEG, JPG, WEBP, DOC, DOCX and PDF are allowed.',
        }));
      }
      const isLt2M = file.size / 1024 / 1024 < maxFileSizeMB;
      if (!isLt2M) {
        setErrors((prevErrors: ReportMissingErrorsState) => ({
          ...prevErrors,
          invoice: 'File must smaller than 2MB!',
        }));
      }
      if (isItValidType && isLt2M) {
        setFileList([file]);
      }

      return false;
    },

    fileList,
  };

  const reportMissingCashback = async () => {
    try {
      const formData = new FormData();
      formData.append('invoice', fileList[0] as any);

      const reportData = {
        click: clickId,
        userType: reportingUserType,
        coupon: isCouponUsed,
        platform: reportingPlatform,
        type: 'offer',
        orderId: watchOrderDetails('orderId'),
        paidAmount: watchOrderDetails('orderAmount'),
        message: watchMessage('message'),
      };
      formData.append('reportData', JSON.stringify(reportData));

      const url = '/api/proxy/click/missing-cashback/report';
      const res = await fetchWrapper(url, {
        body: formData,
        method: 'POST',
        deleteContentType: true,
        responseType: 'text',
      });
      dispatch(setComplaintId(res as string));
      resetMessage();
      resetOrderDetails();
      dispatch(setReportingUserType(''));
      dispatch(setReportingPlatform(''));
      dispatch(setIsCouponUsed(false));
      dispatch(setClickId(''));
      setFileList([]);
      dispatch(setReportMissingStep(reportMissingStep + 1));
    } catch (e) {
      console.log(e);
    } finally {
      setIsLoading(false);
    }
  };

  const validateOrderDetails = async () => {
    let hasError = false;
    hasError = !(await triggerOrderValidation());

    let userTypeError = '';
    let platformError = '';
    let invoiceError = '';

    if (!reportingUserType) {
      userTypeError = 'User Type is Required';
      hasError = true;
    } else if (!(reportingUserType === 'new' || reportingUserType === 'old')) {
      userTypeError = 'Invalid User Type';
      hasError = true;
    }

    if (!reportingPlatform) {
      platformError = 'Platform is Required';
      hasError = true;
    } else if (
      !(reportingPlatform === 'mobile' || reportingPlatform === 'web')
    ) {
      platformError = 'Invalid Platform Type';
      hasError = true;
    }

    if (!fileList.length) {
      invoiceError = 'Invoice File is Required';
      hasError = true;
    }

    setErrors((prevErrors: ReportMissingErrorsState) => {
      return {
        ...prevErrors,
        userType: userTypeError,
        platform: platformError,
        invoice: invoiceError,
      };
    });

    if (!hasError) {
      return dispatch(setReportMissingStep(reportMissingStep + 1));
    }
    return;
  };

  const onDateChange: DatePickerProps['onChange'] = (date) => {
    if (date) {
      const formattedDate = dayjs(date).format('YYYY-MM-DD');
      dispatch(setSelectedDate(formattedDate));
      replace(pathname + '?' + createQueryString('date', formattedDate));
    } else {
      replace(pathname + '?' + createQueryString('date', ''));
    }
  };

  const validateMessageAndReport = async () => {
    setIsLoading(true);
    let hasError = false;
    hasError = !(await triggerMessageValidation());

    if (!hasError) {
      await reportMissingCashback();
      return setIsLoading(false);
    }
    setIsLoading(false);
    return;
  };

  const dispatch = useAppDispatch();

  const { selectedSort, dateFilter } = useAppSelector(
    (state) => state.commonFilters
  );

  const onApply = async (options?: {
    selectedStores?: string[];
    selectedStatus?: string[];
  }) => {
    const {
      selectedStores: selectedStoresParam,
      selectedStatus: selectedStatusParam,
    } = options ?? {};

    if (selectedStoresParam?.length === 0) {
      if (selectedStatusParam && selectedStatusParam.length > 0) {
        const selectedStatusString = selectedStatusParam.join(',');

        const queries = [
          { name: 'stores', value: '' },
          { name: 'status', value: selectedStatusString },
        ];

        const queryString = createMultiQueryString(queries);
        replace(pathname + '?' + queryString);
      } else {
        const queries = [
          { name: 'stores', value: '' },
          { name: 'status', value: '' },
        ];
        const queryString = createMultiQueryString(queries);
        replace(pathname + '?' + queryString);
      }
    } else if (selectedStoresParam && selectedStoresParam.length > 0) {
      const selectedStoreIds = selectedStoresParam.map((storeName) => {
        const store =
          clickedStores &&
          clickedStores.find((store) => store.name === storeName);
        return store ? store.uid.toString() : '';
      });

      const selectedStoreUidString = selectedStoreIds.join(',');

      if (selectedStatusParam && selectedStatusParam.length > 0) {
        const selectedStatusString = selectedStatusParam.join(',');

        const queries = [
          { name: 'stores', value: selectedStoreUidString },
          { name: 'status', value: selectedStatusString },
        ];

        const queryString = createMultiQueryString(queries);
        replace(pathname + '?' + queryString);
      } else {
        const queries = [
          { name: 'stores', value: selectedStoreUidString },
          { name: 'status', value: '' },
        ];
        const queryString = createMultiQueryString(queries);
        replace(pathname + '?' + queryString);
      }
    } else if (selectedStatusParam && selectedStatusParam.length > 0) {
      const selectedStatusString = selectedStatusParam.join(',');

      const queries = [{ name: 'status', value: selectedStatusString }];
      const queryString = createMultiQueryString(queries);
      replace(pathname + '?' + queryString);
    }
  };

  const onClear = () => {
    const queries = [
      { name: 'stores', value: '' },
      { name: 'status', value: '' },
    ];

    const queryString = createMultiQueryString(queries);
    replace(pathname + '?' + queryString);
  };

  const onClickSortBy: MenuProps['onClick'] = ({ key }) => {
    replace(pathname + '?' + createQueryString('sortType', key));
  };

  useEffect(() => {
    let filterCount = 0;
    if (selectedStores?.length > 0) {
      filterCount += 1;
    }
    if (selectedStatus.length > 0) {
      filterCount += 1;
    }
    dispatch(setTotalFiltersApplied(filterCount));
  }, [selectedStatus, selectedStores, dispatch, searchParams]);

  useEffect(() => {
    dispatch(setSortItems(sortItems));
    dispatch(setTitle('Report Missing Cashback'));
    dispatch(setStoresList(clickedStores ? clickedStores : []));
    dispatch(setHideSearchFilter(true));
    dispatch(setHideSortFilter(true));
    dispatch(setSingleDatePicker(true));
    dispatch(setStatusList(statusList));
  }, [dispatch, clickedStores, pathname]);

  useEffect(() => {
    dispatch(setSelectedDate(searchParams.get('date') || ''));
    dispatch(setSelectedSort(searchParams.get('sortType') as SortTypes) ?? '');
    const storesSelected = searchParams.get('stores')?.split(',');

    const selectedStoreNames = storesSelected?.length
      ? storesSelected.map((storeId) => {
          const store =
            clickedStores &&
            clickedStores.find(
              (store) => store.uid === Number.parseInt(storeId)
            );
          return store ? store.name : '';
        })
      : [];

    setSelectedStores(selectedStoreNames);
    setSelectedStatus(searchParams.get('status')?.split(',') ?? []);
  }, [searchParams, dispatch, clickedStores, pathname]);

  return (
    <>
      <CommonHeader
        headline='My Earnings'
        subHeading={<span>Report Missing Cashback</span>}
      />

      <motion.section
        animate={{ opacity: 1 }}
        className='max-w-[1280px] min-[1280px]:mx-auto'
        initial={{ opacity: 0 }}
        onClick={(e) => {
          e.stopPropagation();
        }}
        transition={{ duration: 0.5 }}
      >
        <motion.div
          animate={{ opacity: 1, y: 0 }}
          className='sticky top-[104px] z-[9]'
          initial={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <BreadcrumbSaveShare
            breadCrumbs={[
              { title: 'Cashback Home', link: '/' },
              { title: 'User Page', link: '/my-profile' },
              { title: 'Payment History', link: '/' },
            ]}
          />
        </motion.div>
        <motion.div
          animate={{ opacity: 1 }}
          className='w-full flex z-[0] relative'
          initial={{ opacity: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <MyEarningsSidenav activeNavId={5} />
          <motion.div
            animate={{ opacity: 1 }}
            className='bg-[#E0E0E0] dark:bg-[#1F222A] w-full ml-[2px]'
            initial={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className='pt-[14px]'>
              <AnimatePresence>
                {isShowSteps === false && (
                  <motion.div
                    animate={{ opacity: 1, y: 0 }}
                    className='flex-center flex-col lg:flex-row gap-y-[10px] px-[16px] lg:px-[18px] xl:px-[30px]'
                    exit={{ opacity: 0, y: -20 }}
                    initial={{ opacity: 0, y: 20 }}
                    transition={{ duration: 0.5 }}
                  >
                    <div className='grow-0'>
                      <Image
                        alt='illustration'
                        className='w-[130px] h-[160px] lg:w-[150px] lg:h-[280px] xl:h-[310px] object-contain'
                        height={304}
                        src='/img/sad-boy.png'
                        title='illustration'
                        width={211}
                      />
                    </div>
                    <div className='text-center'>
                      <h2 className='text-sm lg:text-[24px] block text-blackWhite font-bold max-w-[208px] lg:max-w-[370px] lg:leading-[35px] mx-auto'>
                        Did you miss any cashback ? <br />
                        We will help you out!
                      </h2>
                      <p className='text-[7px] sm:text-[8px] lg:text-sm mt mt-[12px] font-medium block max-w-[209px] lg:max-w-[450px] xl:max-w-[607px] mx-auto'>
                        You bought something through us and you couldn't find it
                        tracked to your account then feel free to report us.
                        Please note that proper tracking of cashback takes up to
                        72 hours. Please note: If your order is not tracked, you
                        can Report missing ticket until next month 3rd midnight.
                        Example: If your order was on 12th November, you can
                        Report missing ticket until December 3rd midnight.
                      </p>
                      <motion.div
                        animate={{ opacity: 1, y: 0 }}
                        className='flex gap-x-[10px] max-w-[300px] lg:max-w-[560px] xl:max-w-[690px] mt-[12px] rounded-[5px] bg-[#FFE1A5] dark:bg-[#574ABE]/80 pl-[8px] pr-[16px] py-[20px] mx-auto'
                        initial={{ opacity: 0, y: 10 }}
                        transition={{ duration: 0.3, delay: 0.3 }}
                      >
                        <WarningIcon className='w-[12px] text-[#FF4141] dark:text-[#FF6B6B] shrink-0' />
                        <p className='text-black dark:text-white text-[8px] lg:text-xs font-medium'>
                          Please note: If your order is not tracked, you can
                          Report missing ticket until next month 3rd midnight.
                          Example: If your order was on 12th November, you can
                          Report missing ticket until December 3rd midnight.
                        </p>
                      </motion.div>
                      <div className='mt-[20px] xl:mt-[30px] flex-center flex-col gap-y-[10px] md:flex-row gap-x-[20px]'>
                        <motion.button
                          className='bg-transparent text-primary dark:text-white uppercase text-xs lg:text-[16px] font-semibold !h-[40px] lg:!h-[47px] w-[250px] flex-center border-[2px] border-primary dark:border-white rounded-[5px]'
                          whileHover={{
                            scale: 1.05,
                            boxShadow: '0px 8px 30px rgba(0, 0, 0, 0.1)',
                          }}
                          whileTap={{ scale: 0.97 }}
                        >
                          Ticket Status
                        </motion.button>
                        <motion.div
                          whileHover={{
                            scale: 1.05,
                            boxShadow: '0px 8px 30px rgba(0, 0, 0, 0.1)',
                          }}
                          whileTap={{ scale: 0.97 }}
                        >
                          <ThemeButton
                            className='!w-[250px] !text-xs lg:!text-[16px] !font-semibold !h-[40px] lg:!h-[47px]'
                            onClick={() => setShowSteps(true)}
                            text='Submit Missing Ticket'
                          />
                        </motion.div>
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
              {/* -----------------------------Steps----------------------- */}
              <AnimatePresence>
                {isShowSteps && (
                  <motion.div
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    initial={{ opacity: 0, y: 20 }}
                    transition={{ duration: 0.5 }}
                  >
                    <CustomSteps
                      currentStep={reportMissingStep}
                      steps={[
                        {
                          step: 1,
                          content: 'Find your click',
                        },
                        { step: 2, content: 'Fill the details' },
                        { step: 3, content: 'Comments' },
                        { step: 4, content: 'Success' },
                      ]}
                    />

                    {reportMissingStep === 1 && (
                      <div className='mt-1 mb-4'>
                        <MyEarningsToolbar
                          disableDate={true}
                          onApply={onApply}
                          onClear={onClear}
                          onClickSortBy={onClickSortBy}
                          onDateChange={onDateChange}
                          selectedDate={dateFilter}
                          selectedSort={selectedSort}
                          selectedStatusArray={selectedStatus}
                          selectedStoreArray={selectedStores}
                        />
                      </div>
                    )}

                    <RmcStep1 data={data} />
                    {[2, 3].includes(reportMissingStep) && (
                      <motion.div
                        animate={{ opacity: 1, y: 0 }}
                        className='mx-[6px] lg:mx-[30px] mb-[30px] rounded-[10px] mt-[12px] lg:mt-[17px] overflow-hidden shadow-sm'
                        initial={{ opacity: 0, y: 20 }}
                        transition={{ duration: 0.5 }}
                        whileHover={{
                          boxShadow: '0px 8px 30px rgba(0, 0, 0, 0.1)',
                        }}
                      >
                        <motion.div
                          animate={{ opacity: 1 }}
                          className='px-[10px] lg:px-[20px] flex justify-between items-center gap-[5px] h-[50px] lg:h-[51px] cursor-pointer font-medium bg-white dark:bg-[#3E424C] hover:scale-105'
                          initial={{ opacity: 0 }}
                          style={{
                            boxShadow: '0px 4px 16px 0px rgba(0, 0, 0, 0.04)',
                          }}
                          transition={{ duration: 0.3, delay: 0.2 }}
                        >
                          <div className='flex items-center'>
                            <div className='relative w-[50px] h-[45px] lg:w-[66px] lg:h-[37px] mr-[10px]'>
                              <Image
                                alt='storeImg'
                                className='object-contain'
                                fill
                                src={selectedStoreLogo}
                                title='storeImg'
                              />
                            </div>
                            <div className='lg:ml-[20px] flex items-center text-[10px] lg:text-xs'>
                              <DateIcon className='w-[12px] lg:w-[16px] text-blackWhite' />
                              <span className='font-semibold ml-[8px] text-blackWhite'>
                                {dayjs(clickDate).format(`DD MMMM YYYY`)}
                              </span>
                              <span className='font-medium ml-[5px] text-[#999] dark:text-[#8C94A7]'>
                                {dayjs(clickTime, 'HH:mm').format('hh:mm A')}
                              </span>
                            </div>
                          </div>
                          <motion.div
                            className='w-[25px] h-[25px] rounded-full bg-primary flex-center'
                            whileHover={{ scale: 1.2, rotate: 15 }}
                            whileTap={{ scale: 0.9 }}
                          >
                            <Edit className='w-[12px] text-white' />
                          </motion.div>
                        </motion.div>
                        <motion.div
                          animate={{ opacity: 1 }}
                          className='bg-container dark:bg-[#31343D] h-full px-[9px] lg:px-[27px] pt-[18px] lg:pt-[27px] pb-[30px]'
                          initial={{ opacity: 0 }}
                          transition={{ duration: 0.3, delay: 0.3 }}
                        >
                          <RmcStep2
                            orderErrorDetails={orderDetailsError}
                            register={registerOrderDetails}
                            uploadProps={props}
                            validationErrors={errors}
                          />
                          <RmcStep3
                            register={registerMessage}
                            validationError={messageError}
                          />
                          <motion.div
                            animate={{ opacity: 1, y: 0 }}
                            className='flex-center mt-[30px]'
                            initial={{ opacity: 0, y: 20 }}
                            transition={{ duration: 0.3, delay: 0.4 }}
                          >
                            {!isLoading && (
                              <motion.button
                                className='flex items-center'
                                onClick={() =>
                                  dispatch(
                                    setReportMissingStep(reportMissingStep - 1)
                                  )
                                }
                                whileHover={{ x: -5 }}
                                whileTap={{ scale: 0.95 }}
                              >
                                <RightArrow className='w-[11px] text-blackWhite rotate-180' />
                                <span className='text-[10px] font-semibold ml-[5px] text-blackWhite'>
                                  Back
                                </span>
                              </motion.button>
                            )}

                            {isLoading ? (
                              <LoadingGif className='!h-[40px] ' />
                            ) : (
                              <motion.div
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                              >
                                <ThemeButton
                                  className='!text-[10px] !w-[95px] ml-[20px]'
                                  onClick={() => {
                                    if (reportMissingStep === 2) {
                                      return validateOrderDetails();
                                    }
                                    if (reportMissingStep === 3) {
                                      return validateMessageAndReport();
                                    }
                                    dispatch(
                                      setReportMissingStep(
                                        reportMissingStep + 1
                                      )
                                    );
                                  }}
                                  text='NEXT'
                                />
                              </motion.div>
                            )}
                          </motion.div>
                        </motion.div>
                      </motion.div>
                    )}

                    <RmcStep4 />
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </motion.div>
        </motion.div>
      </motion.section>
    </>
  );
};

export default IndexClientsReportMissingCashback;
