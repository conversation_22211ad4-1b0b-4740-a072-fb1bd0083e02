import ReportMissingCbAccordian from '@/app/components/accordians/report-missing-cb-accordian';
import NoData from '@/app/components/no-data';
import { useAppSelector } from '@/redux/hooks';
import { useCreateMultiQueryString } from '@/utils/custom-hooks';
import { ConfigProvider, Pagination, theme } from 'antd';
import { useTheme } from 'next-themes';
import { usePathname, useSearchParams } from 'next/navigation';
import { useRouter } from 'next/navigation';
import React, { useState } from 'react';

const RmcStep1 = ({ data }: { data: any }) => {
  const { reportMissingStep } = useAppSelector(
    (state) => state.reportMissingCbStep
  );

  const { resolvedTheme } = useTheme();
  const { replace } = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const createMultiQueryString = useCreateMultiQueryString(searchParams);
  const [activeId, setActiveId] = useState(0);

  if (reportMissingStep !== 1) {
    return;
  }

  const handleToggle = (id: number) => {
    if (activeId === id) {
      setActiveId(0);
    } else {
      setActiveId(id);
    }
  };
  return (
    <div className='bg-container mx-[6px] pt-[12px] lg:pt-[27px] pb-[27px] px-[9px] lg:px-[27px] lg:mx-[30px] rounded-[4px] mt-[10px] lg:mt-[17px] min-h-[70vh] lg:min-h-fit'>
      <div className='flex flex-col gap-y-[8px] lg:gap-y-[14px]'>
        {data?.stores?.length ? (
          data?.stores?.map((item: any) => (
            <ReportMissingCbAccordian
              activeId={activeId}
              data={item}
              key={item?.store?.uid}
              onClick={handleToggle}
            />
          ))
        ) : (
          <NoData imgClass='!w-[200px] !h-[200px] lg:!w-[300px] lg:!h-[300px]' />
        )}
      </div>

      {data && data?.pagination?.pageSize > 0 && (
        <div className='flex-center w-full my-[50px]'>
          <ConfigProvider
            theme={{
              algorithm:
                resolvedTheme === 'dark'
                  ? theme.darkAlgorithm
                  : theme.defaultAlgorithm,
            }}
          >
            <Pagination
              defaultCurrent={1}
              defaultPageSize={15}
              onChange={(pageNumber, pageSize) =>
                replace(
                  pathname +
                    '?' +
                    createMultiQueryString([
                      { name: 'page', value: pageNumber.toString() },
                      { name: 'pageSize', value: pageSize.toString() },
                    ])
                )
              }
              total={data.pagination.total}
            />
          </ConfigProvider>
        </div>
      )}
    </div>
  );
};

export default RmcStep1;
