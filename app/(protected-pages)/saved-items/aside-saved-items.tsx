'use client';
import React from 'react';
import CommonFilterSidebar from '@/app/components/misc/common-filter-sidebar';
import { SidebarFilterProps } from '@/types/global-types';
import { motion } from 'framer-motion';

const AsideSavedItems = ({ tabId }: { tabId: number }) => {
  let filterOptions: SidebarFilterProps['filterProps'] = [];

  if (tabId === 1 || tabId === 2) {
    filterOptions = [{ filter: 'user' }];
  }

  if (tabId === 3) {
    filterOptions = [{ filter: 'percentage' }];
  }
  if (tabId === 5) {
    filterOptions = [];
  }
  if (tabId === 6) {
    return;
  }
  return (
    <motion.aside
      animate={{ opacity: 1, x: 0 }}
      className='shrink-0 lg:w-[230px] xl:w-[280px] lg:max-h-[calc(100svh-148px)] sticky top-[148px] overflow-hidden hidden lg:block scrollbarNone'
      initial={{ opacity: 0, x: -30 }}
      transition={{ duration: 0.5, ease: 'easeOut' }}
    >
      <CommonFilterSidebar filterProps={filterOptions} rootClass='!mt-0' />
    </motion.aside>
  );
};

export default AsideSavedItems;
