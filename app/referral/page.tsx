import React from 'react';

// Section components (to be implemented in app/components/referral/)
import IndexClientsReferEarn from './index-clients';
import type { GetReferralLeaderboardResponse } from '@/services/api/data-contracts';
import fetchWrapper from '@/utils/fetch-wrapper';
import { BASE_URL } from '@/config';

async function getReferralLeaderBoardData({
  timeFrame = 'all',
}: {
  timeFrame: 'weekly' | 'monthly' | 'all';
}) {
  return await fetchWrapper<GetReferralLeaderboardResponse[]>(
    `${BASE_URL}/campaign/referral-earnings-leaderboard?timeFrame=${timeFrame}`
  );
}

const ReferralPage = async ({
  searchParams,
}: {
  searchParams: {
    timeFrame: 'weekly' | 'monthly' | 'all';
  };
}) => {
  let data: any;
  try {
    data = await getReferralLeaderBoardData({
      timeFrame: searchParams.timeFrame,
    });
  } catch (error) {
    console.log({ error });
  }
  return <IndexClientsReferEarn referralData={data} />;
};

export default ReferralPage;

// ISR Configuration
// Revalidate every 30 minutes (1800 seconds)
// Referral leaderboard data changes more frequently as users earn referrals
export const revalidate = 1800;
