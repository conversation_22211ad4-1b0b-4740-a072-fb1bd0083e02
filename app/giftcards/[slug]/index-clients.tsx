'use client';
import BreadcrumbSaveShare from '@/app/components/atoms/breadcrumb-container';
import GiftCardAccordian from '@/app/components/accordians/giftcard-accordian';
// import SlidingButton from '@/app/components/atoms/sliding-button';
import ThemeButton from '@/app/components/atoms/theme-btn';
import Giftcard from '@/app/components/cards/giftcard';
import GiftcardBanner from '@/app/components/cards/giftcard-banner';
import AddGiftcardAmountForm from '@/app/components/forms/add-giftcard-amount-form';
import SendGiftcardForm from '@/app/components/forms/send-giftcard-form';
import CommonHeader from '@/app/components/headers/common-header';
import SelectGiftcardAmount from '@/app/components/misc/select-giftcard-amount-pills';
import Image from 'next/image';
import { useEffect, useState } from 'react';
import { Navigation } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css';
import 'swiper/css/navigation';
import {
  LeftRoundButton,
  RightRoundButton,
} from '@/app/components/atoms/rounded-buttons';
import type { GetGiftCardResponse } from '@/services/api/data-contracts';
import React from 'react';
import { useForm } from 'react-hook-form';
import type { GiftCardAmountsList } from '@/types/global-types';
import { Checkbox } from 'antd';
import { useAppSelector } from '@/redux/hooks';

const IndexClients = ({ data }: { data: GetGiftCardResponse }) => {
  const { userDetails } = useAppSelector((state) => state.auth);
  const cardBgStyle = {
    background: 'linear-gradient(180deg, #F2F2F2 -5.1%, #E5E5E5 83.44%)',
    border: '0.5px solid #A7A7A7',
  };
  const [activeId, setActiveId] = useState(0);
  const [customAmount, setCustomAmount] = useState(0);
  const [addCustomAmount, setAddCustomAmount] = useState(false);
  const [cardsList, setCardsList] = useState<GiftCardAmountsList[]>([]);
  const [isIcbPayment, setIsIcbPayment] = useState(true);
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm();

  function totalAmount() {
    const amount = cardsList.reduce(
      (acc, item) => acc + item.amount * item.quantity,
      0
    );
    return Number(amount) + Number(customAmount);
  }

  useEffect(() => {
    if (data.giftCard.denominations) {
      const denominationsList = data.giftCard.denominations.map((item, id) => {
        return {
          amount: item,
          quantity: !id ? 1 : 0,
          isSelected: !id,
        };
      });
      setCardsList(denominationsList);
    }
  }, [data.giftCard.denominations]);

  const addGiftCardAmount = (value: GiftCardAmountsList) => {
    const newDenominationsList: GiftCardAmountsList[] = cardsList.map(
      (denomination) => {
        if (denomination.amount === value.amount) {
          // Find if at least one denomination is selected
          const filteredCardList = cardsList.filter(
            (value) => value.isSelected
          );
          const isSelected =
            filteredCardList.length === 1 &&
            filteredCardList[0].amount === value.amount;
          return {
            ...denomination,
            isSelected: isSelected ? true : !denomination.isSelected,
            quantity: isSelected
              ? denomination.quantity
              : denomination.isSelected
              ? 0
              : 1,
          };
        }
        return {
          ...denomination,
        };
      }
    );
    setCardsList(newDenominationsList);
  };
  const updateGiftCardQuantity = (
    value: GiftCardAmountsList,
    action: string
  ) => {
    const newDenominationsList: GiftCardAmountsList[] = cardsList.map(
      (denomination) => {
        if (denomination.amount === value.amount) {
          if (action === 'add') {
            return {
              ...denomination,
              quantity: denomination.quantity + 1,
            };
          }
          return {
            ...denomination,
            quantity: denomination.quantity - 1,
          };
        }
        return {
          ...denomination,
        };
      }
    );
    console.log(
      '🚀 ~ addGiftCardQuantity ~ newDenominationsList:',
      newDenominationsList
    );
    setCardsList(newDenominationsList);
  };

  const buyGiftCardHandler = async () => {};

  const handleToggle = (id: number) => {
    if (activeId === id) {
      setActiveId(0);
    } else {
      setActiveId(id);
    }
  };
  return (
    <>
      <CommonHeader backRoute='/stores' headline='Flipkart' showMenuBtn />
      <div className='h-full lg:min-h-[calc(100svh-112px)] lg:m-[8px] mb-0 max-w-[1280px] min-[1280px]:mx-auto'>
        <BreadcrumbSaveShare
          breadCrumbs={[
            { title: 'Cashback Home', link: '/' },
            {
              title: 'Giftcards',
              link: '/giftcards',
            },
            { title: data?.giftCard?.name },
          ]}
        />
        {/* <div className='mt-[12px] lg:hidden bg-transparent'>
          <SlidingButton
            buttonDetails={buttonDetails}
            defaultSelectedBtn={1}
            onChange={() => console.log('sliding button')}
            uniqueId='storeByCB'
          />
        </div> */}
        <GiftcardBanner data={data} />
        <div className='bg-container overflow-hidden'>
          <SelectGiftcardAmount
            addCustomAmount={() => setAddCustomAmount(true)}
            addGiftCardAmount={addGiftCardAmount}
            cardsList={cardsList}
            rootClassName='hidden lg:flex'
          />
          <div className='grid grid-cols-1 lg:grid-cols-2 gap-[11px] lg:px-[13px] lg:pb-[23px] pt-[20px] lg:pt-0'>
            <AddGiftcardAmountForm
              addCustomAmount={() => setAddCustomAmount(true)}
              addGiftCardAmount={addGiftCardAmount}
              cardsList={cardsList}
              customAmount={customAmount}
              setCustomAmount={(value) => setCustomAmount(value)}
              showCustomAmount={addCustomAmount}
              totalAmount={totalAmount()}
              updateGiftCardQuantity={updateGiftCardQuantity}
            />
            <SendGiftcardForm errors={errors} register={register} />
          </div>
          <div className='px-2'>
            <h3 className='text-[14px] mt-4 text-left lg:text-center w-full font-bold font-pat'>
              Make Payment
            </h3>
            <div className='flex flex-col lg:flex-row gap-x-6 justify-start items-center w-full lg:w-[900px] mx-auto my-6'>
              <h3 className='text-[12px] font-semibold lg:font-medium font-pop pr-4 mb-2 lg:mt-0 w-[130px]'>
                Use ICB Balance
              </h3>
              <div
                className='px-2 py-4 lg:p-4 w-full lg:w-[779px] h-[77px] flex justify-between items-center rounded-[5px]'
                style={cardBgStyle}
              >
                <div className='flex justify-start items-center' items-center>
                  <Checkbox
                    checked={isIcbPayment}
                    onChange={() => setIsIcbPayment(true)}
                  />
                  <Image
                    alt=''
                    className='h-[18px] lg:h-[26px] w-[28px] lg:w-[36px] ml-3 md:ml-4 lg:ml-8 mr-2'
                    height={24}
                    priority={true}
                    src='/svg/icb-logo-primary.svg'
                    width={23}
                  />
                  <span className='text-[14px] lg:text-[16px] text-primary font-nexa font-light'>
                    indian
                  </span>
                  <span className='text-[14px] lg:text-[16px] text-primary font-nexa font-medium'>
                    cashback.com
                  </span>
                </div>
                <div className='flex justify-end items-center' items-center>
                  <div className='text-[10px] lg:text-[12px] text-primary font-pop font-medium ml-2'>
                    ICB Balance:
                  </div>
                  <div className='text-[14px] lg:text-[16px] mt-1.5 ml-2 text-primary font-nexa font-semibold'>
                    ₹ {userDetails.balance}
                  </div>
                </div>
              </div>
            </div>
            <div className='flex flex-col lg:flex-row gap-x-6 justify-start items-center w-full lg:w-[900px] mx-auto my-6'>
              <h3 className='text-[12px] font-semibold lg:font-medium font-pop pr-4 mb-2 lg:mt-0 w-[130px]'>
                Payment Options
              </h3>
              <div
                className='px-2 py-4 lg:p-4 w-full lg:w-[779px] h-[77px] flex justify-between items-center rounded-[5px]'
                style={cardBgStyle}
              >
                <div className='flex justify-start items-center' items-center>
                  <Checkbox
                    checked={!isIcbPayment}
                    onChange={() => setIsIcbPayment(false)}
                  />
                  <div className='flex flex-wrap justify-start items-center gap-1 lg:gap-2 ml-3 md:ml-4 lg:ml-8 mr-2'>
                    {[...Array(10)].map((_, index) => (
                      <Image
                        alt=''
                        className='h-[18px] sm:h-[23px] w-[39px] sm:w-[46px]'
                        height={23}
                        key={index}
                        priority={true}
                        src={`/img/payment-methods/${index + 1}.png`}
                        width={46}
                      />
                    ))}
                  </div>
                </div>
              </div>
            </div>

            <div className='hidden lg:flex justify-center w-full'>
              <button
                className='flex items-center px-8'
                onClick={() => console.log('cancel')}
                type='button'
              >
                <span className='text-[10px] font-semibold ml-[5px]'>
                  Cancel
                </span>
              </button>
              <ThemeButton
                className='!w-[146px]'
                onClick={handleSubmit(buyGiftCardHandler)}
                text='PAY NOW'
              />
            </div>
          </div>

          {/* -------------------------Accordians------------------------ */}
          <div className='px-[6px] lg:px-[13px] mt-[16px]'>
            <GiftCardAccordian
              activeId={activeId}
              data={{ id: 1, question: 'How to redeem Giftcard?' }}
              onClick={handleToggle}
            >
              <div className='flex justify-center items-start md:items-center flex-col px-[16px] pt-[14px] lg:pt-[33px] pb-[17px] lg:pb-[22px] bg-[#EEE] sk:bg-[#35383E] overflow-auto'>
                <div className='flex justify-start items-center gap-x-[25px] lg:gap-x-[35px] pl-[160px] lg:pl-0 mx-auto'>
                  {data?.giftCard?.howToUse?.[1] && (
                    <>
                      <div className='flex-center rounded-full bg-[#FFC554] text-black w-[22px] h-[22px] lg:w-[33px] lg:h-[33px] shrink-0'>
                        1
                      </div>
                      <div className='shrink-0'>
                        <Image
                          alt='arrow'
                          className='w-[230px] lg:w-[250px] h-full'
                          height={1}
                          src='/svg/giftcard/long-arrow.svg'
                          width={250}
                        />
                      </div>
                      <div className='flex-center rounded-full bg-[#FFC554] text-black w-[22px] h-[22px] lg:w-[33px] lg:h-[33px] shrink-0'>
                        2
                      </div>
                    </>
                  )}
                  {data?.giftCard?.howToUse?.[1] && (
                    <div className='shrink-0'>
                      <Image
                        alt='arrow'
                        className='w-[250px] lg:w-[250px] h-full'
                        height={1}
                        src='/svg/giftcard/long-arrow.svg'
                        width={250}
                      />
                    </div>
                  )}
                  {data?.giftCard?.howToUse?.[1] && (
                    <div className='flex-center rounded-full bg-[#FFC554] text-black w-[22px] h-[22px] lg:w-[33px] lg:h-[33px] shrink-0'>
                      3
                    </div>
                  )}
                </div>
                <div className='flex-center gap-x-[25px] lg:gap-x-[200px] mt-[10px]'>
                  {data?.giftCard?.howToUse?.[0] && (
                    <div className='flex flex-col gap-y-[15px] w-[300px] lg:w-[150px] shrink-0'>
                      <Image
                        alt='giftcard'
                        className='w-full h-[215px] lg:w-[150px] lg:h-[150px] shrink-0'
                        height={150}
                        src={'/svg/giftcard/step1.svg'}
                        width={150}
                      />
                      <p className='break-words text-[10px] lg:text-xs font-normal text-blackWhite text-center'>
                        {data?.giftCard?.howToUse[0]}
                      </p>
                    </div>
                  )}
                  {data?.giftCard?.howToUse?.[1] && (
                    <div className='flex flex-col gap-y-[15px] w-[300px] lg:w-[150px] shrink-0'>
                      <Image
                        alt='giftcard'
                        className='w-full h-[215px] lg:w-[150px] lg:h-[150px] shrink-0'
                        height={150}
                        src={'/svg/giftcard/step2.svg'}
                        width={150}
                      />
                      <p className='break-words text-[10px] lg:text-xs font-normal text-blackWhite text-center'>
                        {data?.giftCard?.howToUse[1]}
                      </p>
                    </div>
                  )}
                  {data?.giftCard?.howToUse?.[2] && (
                    <div className='flex flex-col gap-y-[15px] w-[300px] lg:w-[150px] shrink-0'>
                      <Image
                        alt='giftcard'
                        className='w-full h-[215px] lg:w-[150px] lg:h-[150px] shrink-0'
                        height={150}
                        src={'/svg/giftcard/step3.svg'}
                        width={150}
                      />
                      <p className='break-words text-[10px] lg:text-xs font-normal text-blackWhite text-center'>
                        {data?.giftCard?.howToUse[2]}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </GiftCardAccordian>
            <GiftCardAccordian
              activeId={activeId}
              data={{ id: 2, question: 'Terms and Conditions' }}
              onClick={handleToggle}
              rootClassName='mt-[10px] lg:mt-[18px]'
            >
              <div
                className='disc px-[10px] !list-disc pt-[11px] pb-[20px] lg:pt-[34px] lg:px-[46px] lg:pb-[44px] bg-[#EEE] dark:bg-[#35383E]'
                dangerouslySetInnerHTML={{ __html: data?.giftCard?.terms }}
              />
            </GiftCardAccordian>
          </div>

          {/*----------------------Similar Giftcards---------------  */}
          <div className='bg-container pt-[18px] lg:pt-[22px]'>
            <h4 className='font-pat text-[12px] lg:text-sm font-normal pl-[18px] lg:pl-[36px]'>
              Similar Items
            </h4>
            <div className='lg:hidden overflow-auto px-[18px] pt-[11px] pb-[27px]'>
              <div className='flex justify-start gap-x-[10px] pb-[6px] w-max'>
                {data?.similarGiftCards?.map((giftcard) => (
                  <Giftcard
                    caption={giftcard.caption}
                    imgUrl={giftcard.imageUrl}
                    key={giftcard.uid}
                    saved={giftcard?.saved ?? false}
                    title={giftcard.name}
                    uid={giftcard.uid}
                  />
                ))}
              </div>
              <div className='flex justify-start gap-x-[10px] pt-[6px] w-max'>
                {data?.similarGiftCards?.map((giftcard) => (
                  <Giftcard
                    caption={giftcard.caption}
                    imgUrl={giftcard.imageUrl}
                    key={giftcard.uid}
                    saved={giftcard.saved}
                    title={giftcard.name}
                    uid={giftcard.uid}
                  />
                ))}
              </div>
            </div>
            <div className='relative hidden lg:flex max-w-[100%] justify-between items-center overflow-hidden'>
              <LeftRoundButton classCont='giftcard-button-prev !left-[11px] !top-[40%]' />
              <div className='max-w-[100%] px-[40px]'>
                <Swiper
                  className='testimonialSwiper !pt-[14px] !pb-[44px]'
                  grabCursor
                  modules={[Navigation]}
                  navigation={{
                    nextEl: '.giftcard-button-next',
                    prevEl: '.giftcard-button-prev',
                  }}
                  slidesPerView={'auto'}
                  spaceBetween={20}
                >
                  {data?.similarGiftCards?.map((giftcard) => (
                    <SwiperSlide className='!w-auto' key={giftcard.uid}>
                      <Giftcard
                        caption={giftcard.caption}
                        imgUrl={giftcard.imageUrl}
                        saved={giftcard.saved}
                        title={giftcard.name}
                        uid={giftcard.uid}
                      />
                    </SwiperSlide>
                  ))}
                </Swiper>
              </div>
              <RightRoundButton classCont='giftcard-button-next !right-[11px] !top-[40%]' />
            </div>
          </div>
        </div>
      </div>
      <div className='sticky bottom-[-1px] h-[60px]  bg-container flex lg:hidden justify-between items-center px-8 gap-x-[30px] mt-[40px] z-[9] w-full'>
        <div>
          <div className='text-[10px] font-light font-pop mt-1'>
            Payable Amount
          </div>
          <div className='text-[14px] text-primary font-bold font-nexa'>
            ₹ {totalAmount()}
          </div>
        </div>
        <ThemeButton
          className='!w-[80px] !text-[12px]'
          onClick={() => console.log('reset all')}
          text='PAY'
        />
      </div>
    </>
  );
};

export default IndexClients;
