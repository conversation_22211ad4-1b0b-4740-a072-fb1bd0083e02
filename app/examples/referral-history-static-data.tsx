import {
  type GetUsersByReferralCodeResponse,
  UserDataStatusEnum,
} from '@/services/api/data-contracts';

// Sample static data for referral history
export const staticReferralHistoryData: GetUsersByReferralCodeResponse = {
  users: [
    {
      uid: 1,
      name: '<PERSON>',
      avatar: 'https://randomuser.me/api/portraits/men/1.jpg',
      status: UserDataStatusEnum.Active,
      joined: '2023-01-15T10:30:00Z',
      totalOrders: 12,
      totalPendingReferralCommission: 1500,
      totalConfirmedReferralCommission: 3200,
    },
    {
      uid: 2,
      name: '<PERSON>',
      avatar: 'https://randomuser.me/api/portraits/women/2.jpg',
      status: UserDataStatusEnum.Active,
      joined: '2023-02-20T14:45:00Z',
      totalOrders: 8,
      totalPendingReferralCommission: 950,
      totalConfirmedReferralCommission: 2100,
    },
    {
      uid: 3,
      name: '<PERSON>',
      avatar: 'https://randomuser.me/api/portraits/men/3.jpg',
      status: UserDataStatusEnum.Inactive,
      joined: '2023-03-10T09:15:00Z',
      totalOrders: 3,
      totalPendingReferralCommission: 450,
      totalConfirmedReferralCommission: 0,
    },
    {
      uid: 4,
      name: 'Emily Davis',
      avatar: 'https://randomuser.me/api/portraits/women/4.jpg',
      status: UserDataStatusEnum.Active,
      joined: '2023-04-05T16:20:00Z',
      totalOrders: 15,
      totalPendingReferralCommission: 2200,
      totalConfirmedReferralCommission: 4500,
    },
    {
      uid: 5,
      name: 'Michael Wilson',
      avatar: 'https://randomuser.me/api/portraits/men/5.jpg',
      status: UserDataStatusEnum.Blocked,
      joined: '2023-05-12T11:10:00Z',
      totalOrders: 1,
      totalPendingReferralCommission: 200,
      totalConfirmedReferralCommission: 0,
    },
  ],
  pagination: {
    page: 1,
    pageSize: 15,
    total: 5,
  },
};
