import React from 'react';

// Section components (to be implemented in app/components/referral/)
import HowItWorksMain from '../components/referral-and-link-generator/HowItWorks';
import ReferFriendFAQ from '../components/referral-and-link-generator/ReferFriendFAQ';
import LinkGeneratorHero from '../components/referral-and-link-generator/LinkGeneratorHero';
import LinkGeneratorPerformance from '../components/referral-and-link-generator/LinkGeneratorPerformance';
import CommonHeader from '../components/headers/common-header';

const IndexClientsLinkGenerator = () => {
  return (
    <>
      <CommonHeader headline='Link Generator' />
      <div className='w-full min-h-screen bg-background p-3 flex flex-col justify-start gap-y-4'>
        <LinkGeneratorHero />
        <HowItWorksMain type='link-generator' />
        <LinkGeneratorPerformance />
        <ReferFriendFAQ type='link-generator' />
      </div>
    </>
  );
};

export default IndexClientsLinkGenerator;
