import type {
  OngoingOffersResponse,
  OfferControllerGetOngoingOffersParams,
} from '@/services/api/data-contracts';
import IndexClients from './index-clients';
import { BASE_URL } from '@/config';
import { getCookie } from 'cookies-next';
import { cookies } from 'next/headers';
import fetchWrapper from '@/utils/fetch-wrapper';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Best Ongoing Sale Offers & Discounts – Save More Online',
  description:
    'Discover the latest ongoing sale offers, discounts & deals from top e-commerce stores. Shop now and maximize your savings with IndianCashback!',
  alternates: {
    canonical: 'https://www.indiancashback.com/on-going-sale-offers',
  },
  openGraph: {
    url: 'https://www.indiancashback.com/on-going-sale-offers',
    title: 'Best Ongoing Sale Offers & Discounts – Save More Online',
    description:
      'Discover the latest ongoing sale offers, discounts & deals from top e-commerce stores. Shop now and maximize your savings with IndianCashback!',
  },
};

async function getOngoingOffersData(
  searchParams: OfferControllerGetOngoingOffersParams
) {
  const {
    searchParam = '',
    sortType = 'newest',
    sales = '',
    subCategories = '',
    offerType = 'deals',
    page = '1',
    pageSize = '15',
  } = {
    ...searchParams,
  };

  const accessToken = getCookie('accessToken', { cookies }) as string;
  return await fetchWrapper<OngoingOffersResponse>(
    `${BASE_URL}/offers/ongoing-offers?searchParam=${searchParam}&sortType=${sortType}&offerType=${offerType}&subCategories=${subCategories}&page=${page}&pageSize=${pageSize}&sales=${sales}`,
    {
      token: accessToken,
      // Removed cache: 'no-store' to allow ISR caching
      // ISR will handle revalidation every 180 seconds
    }
  );
}

const Page = async ({
  searchParams,
}: {
  searchParams: OfferControllerGetOngoingOffersParams;
}) => {
  let resData: OngoingOffersResponse;
  try {
    resData = await getOngoingOffersData(searchParams);
  } catch (err: any) {
    console.log({ err });
    return err;
  }
  return <IndexClients data={resData} />;
};

export default Page;

// ISR Configuration
// Revalidate every 3 minutes (180 seconds)
// Ongoing sale offers change frequently with new sales and expiring offers
export const revalidate = 180;
