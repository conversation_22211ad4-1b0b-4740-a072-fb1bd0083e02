'use client';

import { useEffect, useState } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { Card, Button, Input, message } from 'antd';
import { ShareAltOutlined, CopyOutlined } from '@ant-design/icons';

const { TextArea } = Input;

export default function SharePage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [sharedData, setSharedData] = useState({
    title: '',
    text: '',
    url: '',
  });

  useEffect(() => {
    // Get shared data from URL parameters
    const title = searchParams.get('title') || '';
    const text = searchParams.get('text') || '';
    const url = searchParams.get('url') || '';

    setSharedData({ title, text, url });
  }, [searchParams]);

  const handleCopyToClipboard = async (content: string) => {
    try {
      await navigator.clipboard.writeText(content);
      message.success('Copied to clipboard!');
    } catch (err) {
      message.error('Failed to copy to clipboard');
    }
  };

  const handleSearchDeals = () => {
    // Extract domain from URL if available
    if (sharedData.url) {
      try {
        const domain = new URL(sharedData.url).hostname.replace('www.', '');
        router.push(`/search?q=${encodeURIComponent(domain)}`);
      } catch {
        router.push('/online-free-shopping-stores');
      }
    } else {
      router.push('/online-free-shopping-stores');
    }
  };

  const handleGoHome = () => {
    router.push('/');
  };

  if (!sharedData.title && !sharedData.text && !sharedData.url) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <Card className="w-full max-w-md text-center">
          <ShareAltOutlined className="text-4xl text-gray-400 mb-4" />
          <h2 className="text-xl font-semibold mb-2">No Content Shared</h2>
          <p className="text-gray-600 mb-4">
            This page is used when content is shared to IndianCashback from other apps.
          </p>
          <Button onClick={handleGoHome} type="primary">
            Go to Home
          </Button>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-2xl mx-auto">
        <Card className="mb-4">
          <div className="flex items-center mb-4">
            <ShareAltOutlined className="text-2xl text-primary mr-3" />
            <h1 className="text-2xl font-bold">Shared Content</h1>
          </div>
          
          {sharedData.title && (
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Title
              </label>
              <div className="flex gap-2">
                <Input readOnly value={sharedData.title} />
                <Button 
                  icon={<CopyOutlined />}
                  onClick={() => handleCopyToClipboard(sharedData.title)}
                />
              </div>
            </div>
          )}

          {sharedData.text && (
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Text
              </label>
              <div className="flex gap-2">
                <TextArea readOnly rows={3} value={sharedData.text} />
                <Button 
                  icon={<CopyOutlined />}
                  onClick={() => handleCopyToClipboard(sharedData.text)}
                />
              </div>
            </div>
          )}

          {sharedData.url && (
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                URL
              </label>
              <div className="flex gap-2">
                <Input readOnly value={sharedData.url} />
                <Button 
                  icon={<CopyOutlined />}
                  onClick={() => handleCopyToClipboard(sharedData.url)}
                />
              </div>
            </div>
          )}

          <div className="flex gap-3">
            <Button className="flex-1" onClick={handleSearchDeals} type="primary">
              Find Cashback Deals
            </Button>
            <Button onClick={handleGoHome}>
              Go to Home
            </Button>
          </div>
        </Card>

        <Card>
          <h3 className="text-lg font-semibold mb-3">💡 Pro Tip</h3>
          <p className="text-gray-600">
            If you shared a shopping link, we can help you find cashback offers for that store! 
            Click "Find Cashback Deals" to search for available offers and maximize your savings.
          </p>
        </Card>
      </div>
    </div>
  );
}
