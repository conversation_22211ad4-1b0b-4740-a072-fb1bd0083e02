'use client';
import React from 'react';
import CommonFilterSidebar from '@/app/components/misc/common-filter-sidebar';
import { motion } from 'framer-motion';

const AsideStoreByCB = () => {
  return (
    <motion.aside
      animate={{ opacity: 1, x: 0 }}
      className='shrink-0 w-[270px] lg:max-h-[calc(100svh-112px)] sticky top-[112px] overflow-hidden hidden lg:block scrollbarNone'
      initial={{ opacity: 0, x: -20 }}
      transition={{
        duration: 0.5,
        ease: 'easeOut',
        staggerChildren: 0.1,
      }}
    >
      <CommonFilterSidebar
        filterProps={[{ filter: 'percentage' }]}
        rootClass='!mt-0'
      />
    </motion.aside>
  );
};

export default AsideStoreByCB;
