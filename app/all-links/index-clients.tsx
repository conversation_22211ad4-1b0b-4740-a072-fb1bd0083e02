'use client';
import React, { useState } from 'react';
import SearchInput from '../components/atoms/search-input';
import CommonHeader from '../components/headers/common-header';
import LinkContainer from '../components/user-page-components/link-container';
import { useRouter } from 'next/navigation';

const IndexClientsAllLinks = () => {
  const router = useRouter();
  const [search, setSearch] = useState('');
  return (
    <>
      <CommonHeader headline='All Links' />
      <section className='max-w-[1280px] min-[1280px]:mx-auto'>
        <div className='h-[51px] lg:h-[59px] w-full bg-container flex items-center lg:justify-end px-[16px] shadow-sm sticky top-[64px] lg:top-[103px] lg:rounded-b-[10px]'>
          <SearchInput onChange={setSearch} value={search} />
        </div>
        <div className='mx-[6px] px-[8px] lg:mx-0 lg:px-[40px] mt-[12px] pt-[12px] pb-[30px] text-blackWhite bg-container rounded-[10px]'>
          <h4 className='text-[11px] font-pat ml-[23px]'>Cashback</h4>
          <div className='mt-[11px] flex flex-col gap-y-[5px] lg:flex-row lg:flex-wrap lg:gap-x-[19px] lg:gap-y-[21px]'>
            <LinkContainer
              caption='Explore new stores and offers'
              iconClass='w-[18px] h-[18px] lg:w-[23px] lg:h-[23px]'
              iconUrl={'/svg/all-links/shop-add.svg'}
              onClick={() => router.push('/online-free-shopping-stores')}
              rootClass='items-center cursor-pointer'
              title='New Stores'
            />
            <LinkContainer
              caption='Explore trending deals and coupons'
              iconClass='w-[16px] h-[16px] lg:w-[23px] lg:h-[23px]'
              iconUrl={'/svg/all-links/trend-up.svg'}
              onClick={() => router.push('/deals-and-coupons')}
              rootClass='items-center cursor-pointer'
              title='Trending Offers'
            />
            <LinkContainer
              caption='Explore all the categories you need to shop'
              iconClass='w-[16px] h-[16px] lg:w-[23px] lg:h-[23px]'
              iconUrl={'/svg/all-links/bag.svg'}
              onClick={() => router.push('/categories')}
              rootClass='items-center cursor-pointer'
              title='Shopping Categories'
            />
            <LinkContainer
              caption='Percentage wise cashback. Maximum you can save!'
              iconClass='w-[17px] h-[17px] lg:w-[23px] lg:h-[23px]'
              iconUrl={'/svg/cashback.svg'}
              onClick={() => router.push('/deals-and-coupons')}
              rootClass='items-center cursor-pointer'
              title='100% Cashback Sites'
            />
            <LinkContainer
              caption='Cashback related transactions'
              iconClass='w-[17px] h-[17px] lg:w-[23px] lg:h-[23px]'
              iconUrl={'/svg/all-links/repeat-circle.svg'}
              onClick={() => router.push('/transactions')}
              rootClass='items-center cursor-pointer'
              title='Cashback Transactions'
            />
          </div>
        </div>
        {/* <div className='mx-[6px] px-[8px] lg:mx-0 lg:px-[40px] mt-[12px] pt-[12px] pb-[30px] text-blackWhite bg-container rounded-[10px]'>
          <h4 className='text-[11px] font-pat ml-[23px]'>Giftcard</h4>
          <div className='mt-[11px] flex flex-col gap-y-[5px] lg:flex-row lg:flex-wrap lg:gap-x-[19px] lg:gap-y-[21px]'>
            <LinkContainer
              caption='Explore new Giftcards and offers'
              iconClass='w-[13px] h-[13px] lg:w-[23px] lg:h-[23px]'
              iconUrl={'/svg/all-links/new-giftcards.svg'}
              onClick={() => router.push('/giftcards')}
              rootClass='items-center cursor-pointer'
              title='New Giftcards'
            />
            <LinkContainer
              caption='Explore best offers you can get in Giftcards'
              iconClass='w-[13px] h-[13px] lg:w-[23px] lg:h-[23px]'
              iconUrl={'/svg/all-links/ticket-discount.svg'}
              onClick={() => router.push('/')} //FIXME - update route
              rootClass='items-center cursor-pointer'
              title='Best Offers'
            />
            <LinkContainer
              caption='You can redeem your ICB giftcard here'
              iconClass='w-[13px] h-[13px] lg:w-[23px] lg:h-[23px]'
              iconUrl={'/svg/all-links/new-giftcards.svg'}
              onClick={() => router.push('/redeem-icb-giftcard')}
              rootClass='items-center cursor-pointer'
              title='Redeem ICB Giftcard'
            />
            <LinkContainer
              caption='To see all giftcard related transactions'
              iconClass='w-[16px] h-[16px] lg:w-[23px] lg:h-[23px]'
              iconUrl={'/svg/all-links/bag.svg'}
              onClick={() => router.push('/transactions?type=giftcard')}
              rootClass='items-center cursor-pointer'
              title='Giftcard Transactions'
            />
          </div>
        </div>
        <div className='mx-[6px] px-[8px] lg:mx-0 lg:px-[40px] mt-[12px] pt-[12px] pb-[30px] text-blackWhite bg-container rounded-[10px]'>
          <h4 className='text-[11px] font-pat ml-[23px]'>ICB Card</h4>
          <div className='mt-[11px] flex flex-col gap-y-[5px] lg:flex-row lg:flex-wrap lg:gap-x-[19px] lg:gap-y-[21px]'>
            <LinkContainer
              caption='Check you eligibility and Apply for ICB card'
              iconClass='w-[13px] h-[13px] lg:w-[23px] lg:h-[23px]'
              iconUrl={'/svg/all-links/card-add.svg'}
              onClick={() => router.push('https://card.indiancashback.com/')}
              rootClass='items-center cursor-pointer'
              title='Apply for ICB card'
            />
            <LinkContainer
              caption='Card Overview, Transactions, Balance and Offers...etc'
              iconClass='w-[13px] h-[13px] lg:w-[23px] lg:h-[23px]'
              iconUrl={'/svg/all-links/card.svg'}
              onClick={() =>
                router.push('https://card.indiancashback.com/dashboard')
              }
              rootClass='items-center cursor-pointer'
              title='ICB Card Management'
            />
            <LinkContainer
              caption='All transaction details related to ICB Card'
              iconClass='w-[16px] h-[16px] lg:w-[23px] lg:h-[23px]'
              iconUrl={'/svg/all-links/card-rotated.svg'}
              onClick={() =>
                router.push('https://card.indiancashback.com/statements')
              }
              rootClass='items-center cursor-pointer'
              title='ICB Card Transactions'
            />
          </div>
        </div> */}
        <div className='mx-[6px] px-[8px] lg:mx-0 lg:px-[40px] mt-[12px] pt-[12px] pb-[30px] text-blackWhite bg-container rounded-[10px]'>
          <h4 className='text-[11px] font-pat ml-[23px]'>Knowledge Base</h4>
          <div className='mt-[11px] flex flex-col gap-y-[5px] lg:flex-row lg:flex-wrap lg:gap-x-[19px] lg:gap-y-[21px]'>
            <LinkContainer
              caption='Create tickets and check status'
              iconClass='w-[16px] lg:w-[31px]'
              iconUrl='/svg/user-page/ticket.svg'
              onClick={() => router.push('/report-missing-cashback-history')}
              rootClass=' cursor-pointer'
              title='My Tickets'
            />
            <LinkContainer
              caption='Chat with our executives, if you have any queries'
              iconClass='w-[19px] lg:w-[30px]'
              iconUrl='/svg/user-page/support.svg'
              onClick={() => router.push('/')} //FIXME - route update
              rootClass=' cursor-pointer'
              title='Support'
            />
            <LinkContainer
              caption='Frequently asked questions'
              iconClass='w-[17px] lg:w-[31px]'
              iconUrl='/svg/user-page/faq.svg'
              onClick={() => router.push('/')} //FIXME - route update
              rootClass=' cursor-pointer'
              title='FAQ'
            />
            <LinkContainer
              caption='Saved Products, Offers, Segments...Etc'
              iconClass='w-[17px] lg:w-[28px]'
              iconUrl='/svg/user-page/terms.svg'
              onClick={() => router.push('/terms-and-conditions')}
              rootClass=' cursor-pointer'
              title='Terms and Conditions'
            />
            <LinkContainer
              caption='Saved Products, Offers, Segments...Etc'
              iconClass='w-[12px] lg:w-[18px]'
              iconUrl='/svg/user-page/privacy.svg'
              onClick={() => router.push('/privacy-policies')}
              rootClass=' cursor-pointer'
              title='Privacy Policy'
            />
            <LinkContainer
              caption='Rate App, Tell us how to improve your services more!'
              iconClass='w-[20px] lg:w-[34px]'
              iconUrl='/svg/user-page/feedback.svg'
              onClick={() => router.push('/')} //FIXME - route update
              rootClass=' cursor-pointer'
              title='Feedback Corner'
            />
            <LinkContainer
              caption='Blogs related to all segments'
              iconClass='w-[15px] lg:w-[31px]'
              iconUrl='/svg/user-page/blogs.svg'
              onClick={() => router.push('https://indiancashback.com/blogs')}
              rootClass=' cursor-pointer'
              title='Blogs'
            />
          </div>
        </div>
      </section>
    </>
  );
};

export default IndexClientsAllLinks;
