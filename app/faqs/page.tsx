import React from 'react';
import IndexClientsFAQ from './index-clients';
import type { Metadata } from 'next';
import { generateFAQSchema } from '@/utils/schema';
import { faqData } from './faq-data';

export const metadata: Metadata = {
  title: 'FAQs - IndianCashback',
  description:
    'Get answers to frequently asked questions about IndianCashback. Learn how cashback works, tracking methods, withdrawal options, missing cashback claims, and tips to maximize your savings while shopping online.',
  alternates: {
    canonical: 'https://www.indiancashback.com/faqs',
  },
  openGraph: {
    url: 'https://www.indiancashback.com/faqs',
    title: 'FAQs - IndianCashback',
    description:
      'Get answers to frequently asked questions about IndianCashback. Learn how cashback works, tracking methods, withdrawal options, missing cashback claims, and tips to maximize your savings while shopping online.',
  },
};

const Page = () => {
  // Create FAQ schema for SEO
  const faqSchema = generateFAQSchema({
    mainEntity: faqData.map((faq) => ({
      '@type': 'Question' as const,
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer' as const,
        text: faq.answer,
      },
    })),
  });

  return (
    <>
      <script
        dangerouslySetInnerHTML={{ __html: faqSchema }}
        type='application/ld+json'
      />
      <IndexClientsFAQ />
    </>
  );
};

export default Page;

// ISR Configuration
// Revalidate every 12 hours (43200 seconds)
// FAQ content is mostly static but may be updated occasionally
export const revalidate = 43200;
