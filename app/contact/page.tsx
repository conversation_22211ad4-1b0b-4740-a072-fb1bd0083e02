import React from 'react';
import IndexClientsContact from './index-clients';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Contact Us - IndianCashback',
  description:
    "Need help with your cashback, account, or have questions about our offers? Contact the IndianCashback support team for quick assistance. We're available via email, contact form, and social media to resolve your queries.",
  alternates: {
    canonical: 'https://www.indiancashback.com/contact',
  },
  openGraph: {
    url: 'https://www.indiancashback.com/contact',
    title: 'Contact Us - IndianCashback',
    description:
      "Need help with your cashback, account, or have questions about our offers? Contact the IndianCashback support team for quick assistance. We're available via email, contact form, and social media to resolve your queries.",
  },
};

const Page = () => {
  return <IndexClientsContact />;
};

export default Page;

// ISR Configuration
// Revalidate every 24 hours (86400 seconds)
// Contact page content is static and changes infrequently
export const revalidate = 86400;
