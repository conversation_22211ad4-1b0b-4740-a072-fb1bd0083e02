'use client';
import React, { useEffect, useState } from 'react';
import CommonHeader from '../components/headers/common-header';
import boyImg from '@/public/img/trending-categories-boy.png';
import Image from 'next/image';
import ICBLogo from '../components/svg/icb-logo';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import { setClickGeneratedData } from '@/redux/slices/global-slice';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { Tooltip } from '../components/atoms/tooltip';
import SmartLink from '../components/common/smart-link';
import { LinkType } from '@/utils/link-utils';

const IndexClients = () => {
  const [countdown, setCountdown] = useState(3);
  const [isMounted, setMounted] = useState(false);
  const [validatedUrl, setValidatedUrl] = useState<string | null>(null);
  const [isRedirecting, setIsRedirecting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [uiData, setUiData] = useState({
    logo: '',
    offer: '',
    referenceId: '',
  });

  const dispatch = useAppDispatch();
  const { isUserLogin } = useAppSelector((state) => state.auth);
  const MAX_RETRIES = 5;
  const RETRY_DELAY = 500;
  const router = useRouter();

  // Enhanced animation variants
  const containerVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: 'easeOut',
        staggerChildren: 0.2,
      },
    },
    exit: {
      opacity: 0,
      y: -50,
      transition: { duration: 0.5, ease: 'easeInOut' },
    },
  };

  const childVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5, ease: 'easeOut' },
    },
  };

  const progressVariants = {
    initial: { width: '100%' },
    animate: {
      width: '0%',
      transition: { duration: 3, ease: 'linear' },
    },
  };

  // Floating animation for the image
  const floatingAnimation = {
    y: [-10, 10],
    transition: {
      y: {
        duration: 2,
        repeat: Infinity,
        repeatType: 'reverse',
        ease: 'easeInOut',
      },
    },
  };

  useEffect(() => {
    let attempts = 0;
    const validateAndSetData = async () => {
      try {
        const rawData = localStorage.getItem('clickGeneratedData');
        if (!rawData) {
          if (attempts < MAX_RETRIES) {
            attempts++;
            setTimeout(validateAndSetData, RETRY_DELAY);
            return;
          }
          throw new Error('No redirect data found after retries');
        }

        const parsedData = JSON.parse(rawData);
        if (!parsedData?.url || !parsedData.url.startsWith('http')) {
          throw new Error('Invalid URL in redirect data');
        }

        setValidatedUrl(parsedData.url);
        localStorage.setItem('previousUrl', parsedData.previousUrl);
        setUiData({
          logo: parsedData.logo,
          offer: parsedData.offer,
          referenceId: parsedData.referenceId,
        });
        dispatch(setClickGeneratedData(parsedData));
        setMounted(true);
      } catch (error) {
        setError(error instanceof Error ? error.message : 'An error occurred');
        localStorage.removeItem('clickGeneratedData');
        dispatch(
          setClickGeneratedData({
            logo: '',
            referenceId: '',
            url: '',
            offer: '',
          })
        );

        // Delayed redirect to improve UX
        setTimeout(async () => {
          const previousUrl = await localStorage.getItem('previousUrl');
          if (previousUrl) {
            window.location.replace(previousUrl);
          } else {
            router.push('/');
            window.history.replaceState(null, '', '/');
          }
        }, 2000);
      }
    };

    validateAndSetData();
  }, [dispatch, router]);

  useEffect(() => {
    if (!validatedUrl || !isMounted) {
      return;
    }

    const timer = setInterval(() => {
      setCountdown((prev) => prev - 1);
    }, 1000);

    const redirectionTimer = setTimeout(() => {
      setIsRedirecting(true);
      const finalUrl = validatedUrl;

      localStorage.removeItem('clickGeneratedData');
      dispatch(
        setClickGeneratedData({
          logo: '',
          referenceId: '',
          url: '',
          offer: '',
        })
      );

      setTimeout(() => {
        window.location.href = finalUrl;
      }, 500);
    }, 3000);

    return () => {
      clearInterval(timer);
      clearTimeout(redirectionTimer);
    };
  }, [validatedUrl, isMounted, dispatch]);

  if (error) {
    return (
      <>
        <CommonHeader headline='Error' />
        <motion.div
          animate={{ opacity: 1, y: 0 }}
          className='min-h-screen flex items-center justify-center bg-[#f5f5f5] dark:bg-[#2d2e32]'
          initial={{ opacity: 0, y: 20 }}
        >
          <div className='text-center p-8 rounded-lg bg-white dark:bg-container shadow-lg'>
            {/* <FiAlertCircle className="text-red-500 text-5xl mx-auto mb-4" /> */}
            <h2 className='text-xl font-bold mb-4 text-red-500'>{error}</h2>
            <p className='text-gray-600 dark:text-gray-300 mb-6'>
              Redirecting you back...
            </p>
            <div className='animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full mx-auto' />
          </div>
        </motion.div>
      </>
    );
  }

  return (
    <>
      <CommonHeader headline='Redirecting' />
      <AnimatePresence mode='wait'>
        {!isRedirecting && (
          <motion.section
            animate='visible'
            className='min-h-screen overflow-hidden max-w-[1280px] min-[1280px]:mx-auto bg-[#f5f5f5] dark:bg-[#2d2e32] px-4 lg:px-8 xl:px-12 pb-10'
            exit='exit'
            initial='hidden'
            variants={containerVariants}
          >
            {isUserLogin && isMounted && (
              <div className='mt-6 lg:mt-12'>
                <motion.div
                  className='bg-white dark:bg-container rounded-2xl shadow-lg overflow-hidden'
                  transition={{ duration: 0.3 }}
                  variants={childVariants}
                  whileHover={{ scale: 1.01 }}
                >
                  {/* Progress Bar */}
                  <motion.div
                    animate='animate'
                    className='h-1.5 bg-primary'
                    initial='initial'
                    variants={progressVariants}
                  />

                  {/* Header Section */}
                  <motion.div
                    className='bg-[#F1F1F1] dark:bg-[#0000006b] border-t border-white dark:border-black p-4 lg:p-6'
                    variants={childVariants}
                  >
                    <div className='flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4'>
                      <h4 className='text-sm lg:text-lg font-bold flex items-center gap-2'>
                        {/* <FiClock className="animate-pulse text-primary" /> */}
                        <span>Redirecting in </span>
                        <span className='text-primary'>{countdown}</span>
                        <span>seconds...</span>
                      </h4>
                      <Tooltip content='Reference ID for tracking your cashback'>
                        <div className='text-xs lg:text-sm text-gray-600 dark:text-gray-300 flex items-center gap-2'>
                          <span>Reference ID:</span>
                          <code className='bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded'>
                            {uiData.referenceId}
                          </code>
                        </div>
                      </Tooltip>
                    </div>
                  </motion.div>

                  {/* Main Content */}
                  <div className='p-6 lg:p-8 flex flex-col lg:flex-row items-center gap-8 lg:gap-16'>
                    {/* Left Section with Image */}
                    <motion.div
                      animate={floatingAnimation}
                      className='relative w-full lg:w-1/3'
                      variants={childVariants}
                    >
                      <Image
                        alt='redirect-illustration'
                        className='w-full h-auto max-w-[300px] mx-auto'
                        priority
                        src={boyImg}
                      />
                    </motion.div>

                    {/* Right Section with Content */}
                    <motion.div
                      className='flex-1 space-y-6'
                      variants={childVariants}
                    >
                      {uiData.logo && (
                        <motion.div
                          className='flex items-center justify-center lg:justify-start'
                          whileHover={{ scale: 1.05 }}
                        >
                          <Image
                            alt='store-logo'
                            className='object-contain'
                            height={80}
                            src={uiData.logo}
                            width={150}
                          />
                        </motion.div>
                      )}

                      {uiData.offer && (
                        <motion.div
                          className='bg-[#FFC554] p-4 rounded-lg shadow-md'
                          variants={childVariants}
                          whileHover={{ scale: 1.02 }}
                        >
                          <div
                            className='text-sm lg:text-base text-black font-medium'
                            dangerouslySetInnerHTML={{ __html: uiData.offer }}
                          />
                        </motion.div>
                      )}

                      <motion.div
                        className='space-y-4'
                        variants={childVariants}
                      >
                        <motion.div
                          className='flex items-center gap-2 text-primary'
                          whileHover={{ scale: 1.05 }}
                        >
                          <ICBLogo className='w-8 h-8' />
                          <span className='font-bold text-lg'>
                            IndianCashBack
                          </span>
                        </motion.div>
                        <p className='text-sm lg:text-base text-gray-700 dark:text-gray-300'>
                          You're being redirected to complete your purchase.
                          Your cashback will be tracked automatically.
                        </p>
                      </motion.div>

                      <motion.div
                        className='flex flex-col lg:flex-row gap-4 pt-4'
                        variants={childVariants}
                      >
                        <SmartLink
                          className='px-6 py-3 text-center border-2 border-primary text-primary rounded-lg hover:bg-primary hover:text-white transition-all duration-300 flex items-center justify-center gap-2'
                          href='/'
                          linkType={LinkType.INTERNAL}
                        >
                          Return to Home
                        </SmartLink>
                        <motion.button
                          className='px-6 py-3 bg-primary text-white rounded-lg hover:bg-primaryDark transition-all duration-300 flex items-center justify-center gap-2'
                          onClick={() => {
                            if (validatedUrl) {
                              window.location.href = validatedUrl;
                            }
                          }}
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <span>Go to Store Now</span>
                          {/* <FiExternalLink /> */}
                        </motion.button>
                      </motion.div>
                    </motion.div>
                  </div>
                </motion.div>
              </div>
            )}
          </motion.section>
        )}
      </AnimatePresence>
    </>
  );
};

export default IndexClients;
