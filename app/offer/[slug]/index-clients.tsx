'use client';
import BreadcrumbSaveShare from '@/app/components/atoms/breadcrumb-container';
import MainOfferCard from '@/app/components/cards/main-offer-card';
import CommonHeader from '@/app/components/headers/common-header';
import PlusIcon from '@/app/components/svg/plus-icon';
import type { ContextOfferDealsType } from '@/services/api/data-contracts';
import { formatIndRs } from '@/utils/helpers';
import Image from 'next/image';
import React from 'react';
import sadBoyImg from '@/public/img/sad-boy.png';

const IndexClients = ({
  data: similarOffers,
}: {
  data: ContextOfferDealsType[];
}) => {
  return (
    <div>
      <CommonHeader
        backRoute={`/store/`}
        headline='Expired Offer'
        showMenuBtn
        subHeading='Offer'
      />
      <div className='h-full lg:min-h-[calc(100svh-112px)] lg:m-[8px] mb-0 max-w-[1280px] min-[1280px]:mx-auto'>
        <BreadcrumbSaveShare
          breadCrumbs={[
            { title: 'Cashback Home', link: '/' },
            {
              title: `${'Expired Offer'}`,
              link: `/store/expired_offer`,
            },
            { title: 'Product Page' },
          ]}
          rootClass='dark:!bg-[#37393E]'
        />
        <div className='flex-center w-full h-[200px] lg:h-[300px] bg-[#EEEEEE] dark:bg-[#35383E] py-4 px-5'>
          <Image
            alt='sad-boy'
            className='h-[133px] w-[54px] lg:w-[80px] lg:h-auto lg:mx-[20px] shrink-0 relative z-[2]'
            height={200}
            src={sadBoyImg}
            width={100}
          />
          <div className='flex flex-col gap-y-2 text-sm lg:text-lg leading-none font-medium ml-4 lg:ml-10'>
            <span className='text-red-500'>
              This deal is no longer available
            </span>
            <span>Please check other deals below</span>
          </div>
        </div>
        {similarOffers.length > 0 && (
          <aside className='h-auto bg-[#EEEEEE] dark:bg-[#35383E] pt-[21px] mt-5'>
            <h4 className='ml-[25px] lg:ml-[35px font-pat text-sm font-normal text-blackWhite'>
              Similar Offers
            </h4>
            <div className='p-[25px] pb-[10px] lg:px-[30px] mb-[50px] flex gap-x-4 overflow-auto'>
              {similarOffers.map((item) => (
                <MainOfferCard
                  duration={item.endDate}
                  isOfferUpto={Boolean(item?.offerCaption?.trim())}
                  key={item.uid}
                  offerTitle={item.offerTitle}
                  productImgUrl={item.productImage || ''}
                  rootClass='min-w-[160px] max-w-[250px]'
                  showNewBadge={false}
                  storeImgUrl={item.storeLogoUrl}
                  storeName={item.storeName}
                  uid={item.uid}
                >
                  {/* first child */}
                  <p dangerouslySetInnerHTML={{ __html: item.offerTitle }} />
                  {/* second child */}
                  <p className='text-primary dark:text-white font-medium text-[9px]'>
                    {item.salePrice && (
                      <>
                        Offer Applied Price
                        <span className='font-nexa font-black ml-[4px] text-[10px] sm:text-[11px]'>
                          {formatIndRs(item.salePrice)}
                        </span>
                      </>
                    )}
                  </p>
                  {/* third child */}
                  <>
                    <PlusIcon className='text-black shrink-0' />
                    <p className='text-[10px] text-black font-bold leading-none ml-[4px] mt-[2px] truncate'>
                      {item.offerCaption}
                    </p>
                  </>
                  {/* fourth child */}
                  <div className='bg-primary p-[4px] h-full rounded-b-[6px] relative flex-center'>
                    <span className='text-[10px] font-semibold text-white'>
                      GRAB DEAL
                    </span>
                  </div>
                </MainOfferCard>
              ))}
            </div>
          </aside>
        )}
      </div>
    </div>
  );
};

export default IndexClients;
