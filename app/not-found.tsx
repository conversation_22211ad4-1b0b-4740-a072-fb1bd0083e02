import Image from 'next/image';
import CommonHeader from './components/headers/common-header';
import SmartLink from './components/common/smart-link';
import { LinkType } from '@/utils/link-utils';

const imageArray = ['/img/page-not-found-1.png', '/img/page-not-found-2.png'];
const getRandomImage = (images: Array<string>) => {
  const randomIndex = Math.floor(Math.random() * images.length);
  return images[randomIndex];
};

export default function NotFound() {
  const randomImage = getRandomImage(imageArray);
  return (
    <>
      <CommonHeader backRoute='/' headline='404!' showMenuBtn={false} />
      <section className='bg-container flex-center min-h-[calc(100vh-65px)] lg:min-h-[calc(100vh-104px)]'>
        <div className='flex flex-col items-center'>
          <h4 className='text-[#CBCBCB] text-[35px] lg:text-[50px] font-bold'>
            Page not found!
          </h4>
          <div className='relative w-[300px] h-[300px] lg:w-[500px] lg:h-[400px]'>
            <Image
              alt='404 image'
              className='object-contain'
              fill
              sizes='(max-width:1024px) 100vw, 50vw'
              src={randomImage}
              title='404 image'
            />
          </div>
          <SmartLink
            className='w-[135px] h-[40px] mt-[20px] border-[1.5px] border-[#4D3EC1] rounded-[5px] flex-center text-[10px] font-semibold text-blackWhite'
            href={'/'}
            linkType={LinkType.INTERNAL}
          >
            Take me to Home
          </SmartLink>
        </div>
      </section>
    </>
  );
}
