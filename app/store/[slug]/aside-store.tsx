'use client';
import React from 'react';
// import Image from 'next/image';
import CommonFilterSidebar from '@/app/components/misc/common-filter-sidebar';
// import RightArrow from '@/app/components/svg/right-arrow';
// import { generateGiftCardUrl } from '@/utils/helpers';
import { StoreGiftCard } from '@/services/api/data-contracts';
// import Link from 'next/link';

const AsidebarStore = ({
  storeName, //eslint-disable-line
  storeLogo, //eslint-disable-line
  giftCard, //eslint-disable-line
}: {
  storeName: string;
  storeLogo: string;
  giftCard: StoreGiftCard;
}) => {
  // const [value, setValue] = useState('');
  return (
    <aside className='shrink-0 w-[270px] lg:max-h-[calc(100svh-112px)] sticky top-[112px] overflow-hidden  hidden lg:block scrollbarNone'>
      {/* <div className='h-[155px] sticky top-0 bg-container flex flex-col gap-y-[25px] items-center justify-center pl-[20px] pr-[10px]'>
        <div className='relative w-[88px] h-[49px] shrink-0'>
          <Image alt={storeName} fill src={storeLogo} />
        </div>
        {
          <Link
            className='lg:text-[12px] xl:text-[14px] font-semibold shrink-0 flex items-center justify-center gap-x-[7px] text-primary'
            href={`/giftcards/${generateGiftCardUrl(
              giftCard?.name
            )}?uid=${giftCard?.uid}`}
          >
            {storeName} Giftcard
            <RightArrow className='w-[16px] text-primary -rotate-45' />
          </Link>
        }
      </div> */}

      <CommonFilterSidebar
        filterProps={[{ filter: 'user' }]}
        // rootClass='sticky top-[155px] !h-[calc(100%-160px)]'
        rootClass='sticky top-[0px] !h-full !mt-0'
      />
    </aside>
  );
};

export default AsidebarStore;
