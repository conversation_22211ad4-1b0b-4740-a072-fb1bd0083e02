"use client";
import BreadcrumbSaveShare from "@/app/components/atoms/breadcrumb-container";
import TabsContainer from "@/app/components/atoms/tabs-container";
import MainOfferCard from "@/app/components/cards/main-offer-card";
import ImportantPointCard from "@/app/components/cards/important-point-cb-rates";
import ProductCard from "@/app/components/cards/product-card";
import CommonHeader from "@/app/components/headers/common-header";
import ImportantUpdateBadge from "@/app/components/svg/important-update-badge";
import type React from "react";
import { useEffect, useRef, useState } from "react";
import PlusIcon from "@/app/components/svg/plus-icon";
import SearchInput from "@/app/components/atoms/search-input";
import CbRatesAccordian from "@/app/components/accordians/cb-rates-accordian";
import ProductStickyHeader from "@/app/components/headers/product-sticky-header";
import Image from "next/image";
import {
	type CashbackRateType,
	type GetOfferByIdResponse,
	ClickTypeTypeEnum,
} from "@/services/api/data-contracts";
import {
	DottedLine1,
	DottedLine2,
} from "@/app/components/svg/patterns/dotted-lines";
import { useWindowSize } from "usehooks-ts";
import BottomDrawer from "@/app/components/atoms/BottomDrawer";
import ThemeButton from "@/app/components/atoms/theme-btn";
import { Modal } from "antd";
import CrossSVG from "@/app/components/svg/cross";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import {
	setProceedWithoutCb,
	setSelectedOffer,
	setShowWarningModal,
} from "@/redux/slices/global-slice";
import { filterCashbackRates, formatIndRs } from "@/utils/helpers";
import { useCopyCoupon, useGenerateClickData } from "@/utils/custom-hooks";
import CopySVG from "@/app/components/svg/copy";
import { LoadingGif } from "@/app/components/misc/loading-components";
import clsx from "clsx";
import { setLoginModalOpen } from "@/redux/slices/auth-slice";
import { motion, AnimatePresence } from "framer-motion";

const IndexProductClients = ({
	data,
	cbRatesData,
}: {
	data: GetOfferByIdResponse;
	cbRatesData: CashbackRateType[];
}) => {
	const [activeTabId, setActiveTabId] = useState(1);
	const [searchValue, setSearchValue] = useState("");
	const [activeId, setActiveId] = useState(1);
	const [sticky, setSticky] = useState(false);
	const productRef = useRef<HTMLDivElement>(null);
	const [filteredCbRates, setFilteredCbRates] =
		useState<CashbackRateType[]>(cbRatesData);

	const handleToggle = (id: number) => {
		if (activeId === id) {
			setActiveId(0);
		} else {
			setActiveId(id);
		}
	};
	const { showWarningModal, isGlobalLoading } = useAppSelector(
		(state) => state.global,
	);
	const { isUserLogin } = useAppSelector((state) => state.auth);
	const dispatch = useAppDispatch();
	const handleCopyCoupon = useCopyCoupon();
	const { offer, similarOffers } = data;

	const generateClickData = useGenerateClickData();

	const grabDealClickHandler = async () => {
		// Dispatch the selected offer right away
		dispatch(
			setSelectedOffer({
				uid: offer.uid,
				type: ClickTypeTypeEnum.Offer,
				couponCode: offer?.couponCode ? offer?.couponCode : "",
			}),
		);

		// Handle window opening and login actions in parallel
		const actions = [];

		// Open a new tab if the user is logged in and no offer warning
		if (!offer?.offerWarning && isUserLogin) {
			actions.push(window.open("/redirecting", "_blank", "noreferrer"));
		}

		// Show warning modal if offer has a warning
		if (offer?.offerWarning) {
			dispatch(setShowWarningModal(true));
		} else {
			if (!isUserLogin) {
				// Set modal states for login
				dispatch(setProceedWithoutCb(true));
				return dispatch(setLoginModalOpen(true));
			}

			// Generate click data if the user is logged in and no warning
			actions.push(
				generateClickData({ uid: offer.uid, type: ClickTypeTypeEnum.Offer }),
			);
		}

		// Run all actions in parallel
		await Promise.all(actions);
	};

	const tabs =
		cbRatesData.length > 0
			? [
					{ id: 1, label: "Offer Details" },
					{ id: 2, label: "Cashback Rates" },
					{ id: 3, label: "How to get this offer" },
				]
			: [
					{ id: 1, label: "Offer Details" },
					{ id: 2, label: "How to get this offer" },
				];

	const importantPoints = [
		{
			url: "/svg/important-points/illustration1.svg",
			imgCaption: offer.minimumAmount
				? `₹${offer.minimumAmount.toString()}`
				: "",
			title: "Minimum Transaction Amount",
		},
		{
			url: "/svg/important-points/illustration2.svg",
			imgCaption: offer.trackingTime || "",
			title: "Tracking Time",
		},
		{
			url: "/svg/important-points/illustration3.svg",
			imgCaption: offer.confirmationTime || "",
			title: "Approx. Confirmation Time",
		},
		{
			url: "/svg/important-points/illustration4.svg",
			imgCaption: offer.missingAccepted ? "Yes" : "No",
			title: "Missing CB Acceptence",
		},
	];

	// handle scroll event
	const handleScroll = (elTopOffset: number, elHeight: number) => {
		if (window.scrollY > elHeight) {
			setSticky(true);
		} else {
			setSticky(false);
		}
	};

	// add/remove scroll event listener
	useEffect(() => {
		const handleScrollEvent = () => {
			if (productRef?.current) {
				const header = productRef?.current?.getBoundingClientRect();
				handleScroll(header.top, header.height);
			}
		};

		window.addEventListener("scroll", handleScrollEvent);

		return () => {
			window.removeEventListener("scroll", handleScrollEvent);
		};
	}, []);

	useEffect(() => {
		const filteredResult = filterCashbackRates(cbRatesData, searchValue);
		setFilteredCbRates(filteredResult);
	}, [searchValue, cbRatesData]);
	const { width = 0 } = useWindowSize();

	return (
		<>
			<CommonHeader
				backRoute={`/store/${offer?.storeName}`}
				headline={offer.offerTitle}
				showMenuBtn
				subHeading="Coupons and Deals"
			/>

			<ProductStickyHeader
				isSticky={sticky}
				offerProductHandler={grabDealClickHandler}
				productImg={offer.productImage}
				storeImg={offer.storeLogoUrl}
				title={offer.offerTitle}
			/>
			<motion.div
				animate={{ opacity: 1 }}
				className="h-full lg:min-h-[calc(100svh-112px)] lg:m-[8px] mb-0 max-w-[1280px] min-[1280px]:mx-auto"
				initial={{ opacity: 0 }}
				transition={{ duration: 0.5 }}
			>
				<BreadcrumbSaveShare
					breadCrumbs={[
						{ title: "Cashback Home", link: "/" },
						{
							title: `${offer?.storeName}`,
							link: `/store/${offer?.storeName}`,
						},
						{ title: "Product Page" },
					]}
					rootClass="dark:!bg-[#37393E]"
				/>
				<ProductCard
					appliedPrice={offer?.itemPrice}
					hideCbTag={offer?.hideCbTag}
					isAutoGenerated={offer?.isAutoGenerated}
					isExpired={offer?.isExpired}
					newUser={offer?.newUserRate}
					offerAmount={offer?.offerAmount}
					offerCaption={offer.offerCaption}
					offerEndsIn={offer?.endDate}
					offerPercent={offer?.offerPercent}
					offerProductHandler={grabDealClickHandler}
					offerTitle={offer.offerTitle}
					offerType={offer.offerType}
					oldUser={offer?.oldUserRate}
					productImg={offer.productImage || ""}
					ref={productRef}
					storeLogoUrl={offer.storeLogoUrl}
					storeName={offer.storeName}
				/>
				<div className="bg-container flex flex-col lg:flex-row">
					<div
						className={clsx(
							similarOffers.length && "lg:max-w-[calc(100%-278px)]",
							"w-full max-w-full scrollbarNone  lg:customScrollbar bg-[#f5f5f5] dark:bg-[#2d2e32] shadow-md",
						)}
					>
						<motion.div
							animate={{ opacity: 1, y: 0 }}
							className="w-full bg-[#f5f5f5] dark:bg-[#2d2e32] sticky top-[64px] lg:top-[103px] z-[9] shadow-md"
							initial={{ opacity: 0, y: -10 }}
							transition={{ duration: 0.3, ease: "easeOut" }}
						>
							<TabsContainer
								activeId={activeTabId}
								items={tabs}
								rootClass="h-[50px] lg:h-[60px]"
								setActiveId={setActiveTabId}
							/>
						</motion.div>
						<AnimatePresence mode="wait">
							{activeTabId === 1 && (
								<motion.div
									animate={{ opacity: 1, y: 0 }}
									className="px-[6px] lg:px-[13px] pt-[12px] pb-[50px] bg-body lg:my-[18px] lg:mx-[15px] lg:rounded-[10px]"
									exit={{ opacity: 0, y: -10 }}
									initial={{ opacity: 0, y: 10 }}
									transition={{ duration: 0.4, ease: "easeInOut" }}
								>
									<SectionBlock title="Description">
										{/* <p className='px-[18px] lg:px-[50px] text-[9px] leading-[22px] lg:text-[12px] font-normal text-blackWhite'>
                    {offer?.offerDescription}
                  </p> */}
										<div
											className="pl-[26px] pr-[16px] lg:pl-[60px] lg:pr-[50px] text-[9px] leading-[22px] lg:leading-[33px] list-disc lg:text-[12px] font-normal text-blackWhite list-outside disc"
											dangerouslySetInnerHTML={{
												__html: offer.offerDescription,
											}}
										/>
									</SectionBlock>
									<SectionBlock title="Important Points">
										<>
											<motion.div
												animate={{ opacity: 1 }}
												className="grid grid-cols-2 gap-x-[8px] gap-y-[10px] lg:justify-items-center xl:flex lg:flex-wrap lg:gap-x-[20px] mx-[15px] lg:my-[40px] lg:justify-center"
												initial={{ opacity: 0 }}
												transition={{ duration: 0.5 }}
											>
												{importantPoints.map((item, index) => (
													<motion.div
														animate={{ opacity: 1, y: 0 }}
														initial={{ opacity: 0, y: 20 }}
														key={index}
														transition={{
															delay: index * 0.1,
															duration: 0.4,
															ease: "easeOut",
														}}
														whileHover={{
															scale: 1.05,
															transition: { duration: 0.2 },
														}}
													>
														<ImportantPointCard
															imgCaption={item.imgCaption}
															imgUrl={item.url}
															title={item.title}
														/>
													</motion.div>
												))}
											</motion.div>

											{offer.importantUpdate && (
												<motion.div
													animate={{ opacity: 1, y: 0 }}
													className="rounded-[10px] bg-container text-blackWhite mt-[30px] mx-[6px] p-[16px] lg:px-[50px] lg:bg-transparent"
													initial={{ opacity: 0, y: 20 }}
													transition={{ delay: 0.3, duration: 0.5 }}
												>
													<motion.div
														className="flex items-center"
														transition={{
															type: "spring",
															stiffness: 400,
															damping: 10,
														}}
														whileHover={{ x: 5 }}
													>
														<ImportantUpdateBadge className="text-primary dark:text-white" />
														<span className="ml-[7px] text-[9px] font-semibold text-primary lg:text-xs">
															Important Update
														</span>
													</motion.div>
													<motion.div
														animate={{ opacity: 1 }}
														className="mt-[16px] text-[8px] sm:text-[9px] lg:text-xs leading-[17px] lg:leading-[24px] disc"
														dangerouslySetInnerHTML={{
															__html: offer.importantUpdate,
														}}
														initial={{ opacity: 0 }}
														transition={{ delay: 0.5, duration: 0.5 }}
													/>
												</motion.div>
											)}
										</>
									</SectionBlock>
									{offer?.keySpecs && (
										<SectionBlock title="Key Specs">
											<div
												className="pl-[26px] pr-[16px] lg:pl-[60px] lg:pr-[50px] text-[9px] leading-[22px] lg:leading-[33px] list-disc lg:text-[12px] font-normal text-blackWhite list-outside disc"
												dangerouslySetInnerHTML={{ __html: offer.keySpecs }}
											/>
										</SectionBlock>
									)}
									<SectionBlock title="Terms and Conditions">
										<div
											className="pl-[26px] pr-[16px] lg:pl-[60px] lg:pr-[50px] text-[9px] leading-[22px] lg:leading-[33px] list-disc lg:text-[12px] font-normal text-blackWhite list-outside disc"
											dangerouslySetInnerHTML={{
												__html: offer.termsAndConditions,
											}}
										/>
									</SectionBlock>
								</motion.div>
							)}
						</AnimatePresence>

						{activeTabId === 2 && cbRatesData.length > 0 && (
							<motion.div
								animate={{ opacity: 1, y: 0 }}
								exit={{ opacity: 0, y: -10 }}
								initial={{ opacity: 0, y: 10 }}
								transition={{ duration: 0.4, ease: "easeInOut" }}
							>
								<div className="bg-body overflow-hidden lg:mx-[15px] lg:mt-[18px] lg:rounded-[10px]">
									<div className="dark:bg-container lg:mt-[20px] dark:lg:mt-0 min-h-[56px] flex lg:justify-end items-center lg:h-fit px-[8px] lg:px-[40px] ">
										<SearchInput
											onChange={(value) => setSearchValue(value)}
											value={searchValue}
										/>
									</div>

									<div className="pt-[16px] mx-[6px] lg:mx-0 lg:px-[40px] pb-[10vh]">
										<motion.div
											animate={{ opacity: 1 }}
											className="flex flex-col gap-[10px] lg:gap-[18px]"
											initial={{ opacity: 0 }}
											transition={{ duration: 0.5 }}
										>
											{filteredCbRates.map((item, index) => (
												<motion.div
													animate={{ opacity: 1, y: 0 }}
													initial={{ opacity: 0, y: 20 }}
													key={index}
													transition={{
														delay: index * 0.05,
														duration: 0.3,
														ease: "easeOut",
													}}
													whileHover={{
														scale: 1.01,
														transition: { duration: 0.2 },
													}}
												>
													<CbRatesAccordian
														activeId={activeId}
														data={{
															id: index + 1,
															uid: item.uid,
															question: item.name,
															answer: item.description,
															newUser: item.newUserRate,
															oldUser: item.oldUserRate,
															cashbackType: item.type,
														}}
														onClick={handleToggle}
													/>
												</motion.div>
											))}
										</motion.div>
									</div>
								</div>
							</motion.div>
						)}

						{activeTabId === (cbRatesData.length > 0 ? 3 : 2) && (
							<motion.div
								animate={{ opacity: 1, y: 0 }}
								exit={{ opacity: 0, y: -10 }}
								initial={{ opacity: 0, y: 10 }}
								transition={{ duration: 0.4, ease: "easeInOut" }}
							>
								<div className="flex justify-center items-start md:items-center flex-col px-[16px] pt-[14px] lg:pt-[33px] pb-[50px] lg:pb-[22px] bg-[#EEE] dark:bg-[#35383E] overflow-auto lg:mt-[18px] lg:mx-[15px] lg:rounded-[10px] mb-[20px]">
									<div className="hidden lg:flex justify-start items-center gap-x-[25px] lg:gap-x-[35px] pl-[160px] lg:pl-0 mx-auto">
										<div className="flex-center rounded-full bg-[#FFC554] text-black w-[22px] h-[22px] lg:w-[33px] lg:h-[33px] shrink-0">
											1
										</div>
										<div className="shrink-0">
											<Image
												alt="arrow"
												className="lg:w-[150px] xl:w-[180px] h-full"
												height={1}
												src="/svg/giftcard/long-arrow.svg"
												width={250}
											/>
										</div>
										<div className="flex-center rounded-full bg-[#FFC554] text-black w-[22px] h-[22px] lg:w-[33px] lg:h-[33px] shrink-0">
											2
										</div>
										<div className="shrink-0">
											<Image
												alt="arrow"
												className="lg:w-[150px] xl:w-[180px] h-full"
												height={1}
												src="/svg/giftcard/long-arrow.svg"
												width={250}
											/>
										</div>
										<div className="flex-center rounded-full bg-[#FFC554] text-black w-[22px] h-[22px] lg:w-[33px] lg:h-[33px] shrink-0">
											3
										</div>
									</div>
									<motion.div
										animate={{ opacity: 1 }}
										className="flex-center flex-col lg:flex-row gap-x-[25px] lg:gap-x-[80px] xl:gap-x-[100px] mt-[25px]"
										initial={{ opacity: 0 }}
										transition={{ duration: 0.5, delay: 0.2 }}
									>
										<motion.div
											animate={{ opacity: 1, x: 0 }}
											className="flex items-center flex-row lg:flex-col gap-y-[15px] shrink-0 lg:max-w-[170px] xl:max-w-[200px]"
											initial={{ opacity: 0, x: -20 }}
											transition={{ duration: 0.5, delay: 0.3 }}
											whileHover={{
												scale: 1.03,
												transition: { duration: 0.2 },
											}}
										>
											<Image
												alt="step1"
												className="max-w-[150px] lg:max-w-[170px] xl:max-w-[200px]  h-auto shrink-0"
												height={200}
												src={"/img/product/step1.png"}
												width={200}
											/>
											<p className="break-words text-[10px] !leading-[22px] lg:text-xs font-normal text-blackWhite text-center">
												Join <b>indiancashback.com</b>, free, easy and log in to
												your account.
											</p>
										</motion.div>
										<motion.div
											animate={{ opacity: 1, y: 0 }}
											className="relative flex items-center flex-row-reverse mt-[50px] lg:mt-0 lg:flex-col gap-y-[15px] shrink-0 lg:max-w-[170px] xl:max-w-[200px]"
											initial={{ opacity: 0, y: 20 }}
											transition={{ duration: 0.5, delay: 0.4 }}
											whileHover={{
												scale: 1.03,
												transition: { duration: 0.2 },
											}}
										>
											<Image
												alt="step2"
												className="max-w-[150px] lg:max-w-[170px] xl:max-w-[200px] h-auto shrink-0"
												height={200}
												src={"/img/product/step2.png"}
												width={200}
											/>
											<p className="break-words text-[10px] !leading-[22px] lg:text-xs font-normal text-blackWhite text-center">
												<b>Click</b> on the offer you want, this will{" "}
												<b>redirect</b> you to <b>merchant’s website.</b>
											</p>
											<div className="lg:hidden absolute top-[-75px] right-[30%]">
												<DottedLine1 className="text-blackWhite" />
											</div>
											<div className="lg:hidden absolute bottom-[-75px] right-[30%]">
												<DottedLine2 className="text-blackWhite" />
											</div>
										</motion.div>
										<motion.div
											animate={{ opacity: 1, x: 0 }}
											className="flex items-center flex-row mt-[50px] lg:mt-0 lg:flex-col gap-y-[15px] shrink-0 lg:max-w-[170px] xl:max-w-[200px]"
											initial={{ opacity: 0, x: 20 }}
											transition={{ duration: 0.5, delay: 0.5 }}
											whileHover={{
												scale: 1.03,
												transition: { duration: 0.2 },
											}}
										>
											<Image
												alt="step3"
												className="max-w-[150px] lg:max-w-[170px] xl:max-w-[200px] h-auto shrink-0"
												height={200}
												src={"/img/product/step3.png"}
												width={200}
											/>
											<p className="break-words text-[10px] !leading-[22px] lg:text-xs font-normal text-blackWhite text-center">
												<b>Shop normally</b> there, your <b>Reward voucher</b>{" "}
												will reach your account.
											</p>
										</motion.div>
									</motion.div>
								</div>
							</motion.div>
						)}
					</div>

					{similarOffers.length > 0 && (
						<motion.aside
							animate={{ opacity: 1, x: 0 }}
							className="lg:w-[278px] h-auto bg-[#EEEEEE] dark:bg-[#35383E]"
							initial={{ opacity: 0, x: 20 }}
							transition={{ duration: 0.5, delay: 0.2 }}
						>
							<h4 className="ml-[25px] lg:ml-[45px] mt-[21px] font-pat text-sm font-normal text-blackWhite">
								Similar Offers
							</h4>
							<motion.div
								animate={{ opacity: 1 }}
								className="p-[25px] pb-[10px] lg:px-0 pt-[36px] mb-[50px] flex lg:flex-col lg:items-center gap-x-[7px] gap-y-[30px] overflow-auto"
								initial={{ opacity: 0 }}
								transition={{ duration: 0.5, delay: 0.4 }}
							>
								{similarOffers.slice(0, 5).map((item, index) => (
									<motion.div
										animate={{ opacity: 1, y: 0 }}
										initial={{ opacity: 0, y: 20 }}
										key={item.uid}
										transition={{
											delay: 0.5 + index * 0.1,
											duration: 0.3,
											ease: "easeOut",
										}}
										whileHover={{
											scale: 1.05,
											transition: { duration: 0.2 },
										}}
									>
										<MainOfferCard
											duration={item.endDate}
											isOfferUpto={Boolean(item?.offerCaption?.trim())}
											offerTitle={item.offerTitle}
											productImgUrl={item.productImage || ""}
											rootClass="min-w-[160px]"
											showNewBadge={false}
											storeImgUrl={item.storeLogoUrl}
											storeName={item.storeName}
											uid={item.uid}
										>
											{/* first child */}

											<p
												className="truncate w-auto max-w-[200px] lg:max-w-none  lg:whitespace-normal"
												dangerouslySetInnerHTML={{ __html: item.offerTitle }}
											/>

											{/* second child */}
											<p className="text-primary dark:text-white font-medium text-[9px]">
												{item.salePrice && item.salePrice > 0 ? (
													<>
														Offer Applied Price
														<span className="font-nexa font-black ml-[4px] text-[10px] sm:text-[11px]">
															{formatIndRs(item.salePrice)}
														</span>
													</>
												) : (
													""
												)}
											</p>
											{/* third child */}
											<>
												<PlusIcon className="text-black shrink-0" />
												<p className="text-[10px] text-black font-bold leading-none ml-[4px] mt-[2px] truncate">
													{item.offerCaption}
												</p>
											</>
											{/* fourth child */}
											{item.couponCode ? (
												<button
													className="bg-primary w-full p-[4px] h-full rounded-b-[6px] relative hover:bg-[#5448b0] active:bg-primary"
													onClick={(event) =>
														handleCopyCoupon({
															event,
															uid: item.uid,
															couponCode: item.couponCode,
														})
													}
													type="button"
												>
													<div className="border-dashed border-[0.5px] border-[#E0DCFF] rounded-b-[4px] h-full flex items-center justify-center">
														<span className="text-[10px] sm:text-[12px] font-semibold text-white">
															Copy Code
														</span>
														<CopySVG className="text-white absolute top-[16px] right-[12px]" />
													</div>
												</button>
											) : (
												<div className="bg-primary p-[4px] h-full rounded-b-[6px] relative flex-center">
													<span className="text-[10px] font-semibold text-white">
														GRAB DEAL
													</span>
												</div>
											)}
										</MainOfferCard>
									</motion.div>
								))}
							</motion.div>
						</motion.aside>
					)}
				</div>
			</motion.div>

			{offer.offerWarning && width <= 768 ? (
				<BottomDrawer
					heightClass="60svh"
					maskClosable={true}
					onClose={() => dispatch(setShowWarningModal(false))}
					open={showWarningModal}
					sectionClass="!px-0"
					title="Important steps to ensure your Cashback tracks!"
					titleIcon={""}
					topClass="calc(100% - 60svh)"
				>
					<div className="text-[12px] text-blackWhite px-[16px] mt-[30px] pb-[30px] disc list-inside">
						<div dangerouslySetInnerHTML={{ __html: offer.offerWarning }} />
						{!isGlobalLoading ? (
							<ThemeButton
								className="!w-[170px] text-xs font-semibold uppercase mt-[40px] mx-auto transition-transform duration-300 hover:scale-105 active:scale-95"
								onClick={async () => {
									if (!isUserLogin) {
										dispatch(setProceedWithoutCb(true));
										return dispatch(setLoginModalOpen(true));
									}

									const actions = [];

									// Open new tab action
									actions.push(
										window.open(`/redirecting`, "_blank", "noreferrer"),
									);

									// Generate click data action
									actions.push(
										generateClickData({
											uid: offer.uid,
											type: ClickTypeTypeEnum.Offer,
										}),
									);

									// Run both actions concurrently
									await Promise.all(actions);
								}}
								text="Agree and Proceed"
							/>
						) : (
							<LoadingGif className="!h-[30px] mt-[40px]" />
						)}
					</div>
				</BottomDrawer>
			) : (
				offer.offerWarning && (
					<Modal
						cancelText=""
						centered
						classNames={{
							content: "!bg-container",
							header: "!bg-container !text-blackWhite",
						}}
						closeIcon={<CrossSVG className="text-blackWhite w-[16px]" />}
						destroyOnClose={true}
						footer={<></>}
						maskClosable={true}
						okText=""
						onCancel={() => dispatch(setShowWarningModal(false))}
						open={showWarningModal}
						title={
							<h4 className="text-[16px] font-bold text-blackWhite">
								Important steps to ensure your Cashback tracks!
							</h4>
						}
					>
						<div className="text-[12px] text-blackWhite px-[16px] mt-[30px] pb-[30px] disc">
							<div dangerouslySetInnerHTML={{ __html: offer.offerWarning }} />
							{!isGlobalLoading ? (
								<ThemeButton
									className="!w-[170px] text-xs font-semibold uppercase mt-[40px] mx-auto transition-transform duration-300 hover:scale-105 active:scale-95"
									onClick={async () => {
										if (!isUserLogin) {
											dispatch(setProceedWithoutCb(true));
											return dispatch(setLoginModalOpen(true));
										}

										const actions = [];

										// Open a new tab
										actions.push(
											window.open(`/redirecting`, "_blank", "noreferrer"),
										);

										// Generate click data asynchronously
										actions.push(
											generateClickData({
												uid: offer.uid,
												type: ClickTypeTypeEnum.Offer,
											}),
										);

										// Ensure both actions run in parallel
										await Promise.all(actions);
									}}
									text="Agree and Proceed"
								/>
							) : (
								<LoadingGif className="!h-[30px] mt-[40px]" />
							)}
						</div>
					</Modal>
				)
			)}
		</>
	);
};

const SectionBlock = ({
	title,
	children,
}: {
	title: string;
	children: React.ReactNode;
}) => {
	return (
		<motion.div
			animate={{ opacity: 1, y: 0 }}
			className="rounded-[10px] overflow-hidden mt-[10px] lg:mt-[15px] shadow-sm bg-[#eee] dark:bg-[#35383E]"
			initial={{ opacity: 0, y: 10 }}
			transition={{ duration: 0.3 }}
			whileHover={{
				boxShadow: "0px 8px 20px rgba(0, 0, 0, 0.1)",
				transition: { duration: 0.3 },
			}}
		>
			<div
				className="h-[46px] lg:h-[56px] bg-container dark:bg-mainCard pl-[18px] lg:pl-[50px] flex items-center font-pat text-[10px] lg:text-sm border-b-[0.5px] text-blackWhite dark:border-[#353943]"
				style={{ boxShadow: "0px 4px 16px 0px rgba(0, 0, 0, 0.04)" }}
			>
				{title}
			</div>
			<div className="py-[20px] text-blackWhite">{children}</div>
		</motion.div>
	);
};

export default IndexProductClients;
