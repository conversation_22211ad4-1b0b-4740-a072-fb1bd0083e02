import { BASE_URL } from '@/config';
import IndexProductClients from './index-clients';
import {
  CashbackRateType,
  GetCashbackRatesByStoreResponse,
  GetOfferByIdResponse,
} from '@/services/api/data-contracts';
import fetchWrapper from '@/utils/fetch-wrapper';
import { Metadata } from 'next';
import { generateProductUrl } from '@/utils/helpers';

export async function generateMetadata({
  searchParams,
}: {
  searchParams: { uid: number };
}): Promise<Metadata> {
  try {
    const resData = await getProductData({
      uid: Number(searchParams?.uid) || 0,
    });
    const url = generateProductUrl(
      resData?.offer?.storeName,
      resData?.offer?.offerTitle
    );
    return {
      title: `${resData?.offer?.storeName} - ${resData?.offer?.offerTitle} |  online best price India | offers, deals and coupons`,
      openGraph: {
        url: `https://www.indiancashback.com/store/${resData?.offer?.storeName}/${url}?uid=${searchParams?.uid}`,
      },
    };
  } catch (err) {
    console.error(err);
    return {
      title: 'Error',
    };
  }
}

async function getProductData({ uid }: { uid: number }) {
  const res = await fetchWrapper<GetOfferByIdResponse>(
    `${BASE_URL}/offers/offer${uid}`,
    {
      cache: 'no-store',
    }
  );
  return res;
}
async function getCbRatesData({ storeId }: { storeId: object }) {
  const res = await fetchWrapper<GetCashbackRatesByStoreResponse>(
    `${BASE_URL}/stores/cashback-rates-by-store${storeId}`,
    {
      cache: 'no-store',
    }
  );
  return res;
}

const Page = async ({ searchParams }: { searchParams: { uid: number } }) => {
  let resData: GetOfferByIdResponse;
  let cbRatesData: CashbackRateType[] = [];
  try {
    resData = await getProductData({
      uid: Number(searchParams?.uid) || 0,
    });

    if (resData.offer?.storeId) {
      const res = await getCbRatesData({ storeId: resData.offer?.storeId });
      cbRatesData = res.cashbackRates;
    }
  } catch (err: any) {
    console.log(err);
    return err;
  }
  return <IndexProductClients cbRatesData={cbRatesData} data={resData} />;
};

export default Page;

// ISR Configuration
// Revalidate every 4 minutes (240 seconds)
// Product pages contain mix of static product info and dynamic offers
export const revalidate = 240;
