import { MetadataRoute } from 'next';


// Single sitemap generation function
export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  try {
    console.log("Attempting to generate sitemap...");
    const appUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.indiancashback.com';
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://api-main.indiancashback.com';

    const currentDate = new Date();

    const sitemapData = await fetch(`${baseUrl}/sitemap`).then(res => res.json());
    // Initialize the sitemap with static pages
    const sitemapEntries: MetadataRoute.Sitemap = [
      { url: appUrl, lastModified: currentDate, changeFrequency: 'daily', priority: 1.0 },
      { url: `${appUrl}/about-us`, lastModified: currentDate, changeFrequency: 'monthly', priority: 0.8 },
      // { url: `${appUrl}/privacy-policies`, lastModified: currentDate, changeFrequency: 'monthly', priority: 0.7 },
      // { url: `${appUrl}/terms-and-conditions`, lastModified: currentDate, changeFrequency: 'monthly', priority: 0.7 },
      { url: `${appUrl}/all-links`, lastModified: currentDate, changeFrequency: 'weekly', priority: 0.6 },
      { url: `${appUrl}/categories`, lastModified: currentDate, changeFrequency: 'weekly', priority: 0.8 },
      // { url: `${appUrl}/deals-and-coupons`, lastModified: currentDate, changeFrequency: 'daily', priority: 0.9 },
      // { url: `${appUrl}/giftcards`, lastModified: currentDate, changeFrequency: 'daily', priority: 0.9 },
      { url: `${appUrl}/on-going-sale-offers`, lastModified: currentDate, changeFrequency: 'daily', priority: 0.9 },
      { url: `${appUrl}/online-free-shopping-stores`, lastModified: currentDate, changeFrequency: 'weekly', priority: 0.9 },
    ];

    // Add store pages
    try {
      const stores = sitemapData.stores
      const storeEntries = stores.map((store: any) => ({
        url: `${appUrl}/store/${store.slug}`,
        lastModified: new Date(store.updatedAt),
        changeFrequency: 'daily' as const,
        priority: 0.8
      }));
      sitemapEntries.push(...storeEntries);
    } catch (error) {
      console.error('Error generating stores section:', error);
    }

    // // Add offer pages
    // try {
    //   const offers = await fetchOffersSlugs();
    //   const offerEntries = offers.map(offer => ({
    //     url: `${baseUrl}/offer/${offer.slug}`,
    //     lastModified: new Date(offer.updatedAt),
    //     changeFrequency: 'daily' as const,
    //     priority: 0.9
    //   }));
    //   sitemapEntries.push(...offerEntries);
    // } catch (error) {
    //   console.error('Error generating offers section:', error);
    // }

    console.log("Sitemap generated successfully.");
    return sitemapEntries;
  } catch (error) {
    console.error('Error generating sitemap:', error);
    // Return minimal sitemap in case of errors
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.indiancashback.com';
    return [{ url: baseUrl, lastModified: new Date() }];
  }
}