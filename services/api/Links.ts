/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import {
  GenerateLinkDto,
  LinkControllerGenerateLinkData,
  LinkControllerGetLinkDetailsData,
  LinkControllerGetUserAnalyticsData,
  LinkControllerGetUserAnalyticsParams,
  LinkControllerGetUserLinksData,
  LinkControllerGetUserLinksParams,
  LinkResponseDto,
  PaginationResponseDto,
  UserAnalyticsResponseDto,
} from "./data-contracts";
import { ContentType, HttpClient, RequestParams } from "./http-client";

export class Links<
  SecurityDataType = unknown,
> extends HttpClient<SecurityDataType> {
  /**
   * No description
   *
   * @tags Links
   * @name LinkControllerGenerateLink
   * @request POST:/links/generate
   * @secure
   * @response `201` `LinkControllerGenerateLinkData`
   * @response `401` `void` Unauthorized
   * @response `default` `LinkResponseDto`
   */
  linkControllerGenerateLink = (
    data: GenerateLinkDto,
    params: RequestParams = {},
  ) =>
    this.request<LinkControllerGenerateLinkData, void | LinkResponseDto>({
      path: `/links/generate`,
      method: "POST",
      body: data,
      secure: true,
      type: ContentType.Json,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags Links
   * @name LinkControllerGetUserLinks
   * @request GET:/links
   * @secure
   * @response `200` `LinkControllerGetUserLinksData`
   * @response `401` `void` Unauthorized
   * @response `default` `PaginationResponseDto`
   */
  linkControllerGetUserLinks = (
    query: LinkControllerGetUserLinksParams,
    params: RequestParams = {},
  ) =>
    this.request<LinkControllerGetUserLinksData, void | PaginationResponseDto>({
      path: `/links`,
      method: "GET",
      query: query,
      secure: true,
      ...params,
    });
  /**
   * No description
   *
   * @tags Links
   * @name LinkControllerGetUserAnalytics
   * @request GET:/links/analytics
   * @secure
   * @response `200` `LinkControllerGetUserAnalyticsData`
   * @response `401` `void` Unauthorized
   * @response `default` `UserAnalyticsResponseDto`
   */
  linkControllerGetUserAnalytics = (
    query: LinkControllerGetUserAnalyticsParams,
    params: RequestParams = {},
  ) =>
    this.request<
      LinkControllerGetUserAnalyticsData,
      void | UserAnalyticsResponseDto
    >({
      path: `/links/analytics`,
      method: "GET",
      query: query,
      secure: true,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags Links
   * @name LinkControllerGetLinkDetails
   * @request GET:/links/{linkId}
   * @response `200` `LinkControllerGetLinkDetailsData`
   * @response `default` `void` Redirect to the original URL
   */
  linkControllerGetLinkDetails = (linkId: string, params: RequestParams = {}) =>
    this.request<LinkControllerGetLinkDetailsData, void>({
      path: `/links/${linkId}`,
      method: "GET",
      ...params,
    });
}
