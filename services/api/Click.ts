/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import {
  Click<PERSON>ontrollerCreateData,
  ClickControllerCreateNoAuthData,
  ClickControllerGetClickedStoresData,
  ClickControllerGetClicksByStoresData,
  ClickControllerGetClicksByStoresParams,
  ClickControllerGetClicksData,
  ClickControllerGetClicksParams,
  ClickCreateResponse,
  ClicksByStoreResponse,
  ClicksResponse,
  CreateClickDto,
  MissingCashbackControllerListMissingCashbackData,
  MissingCashbackControllerListMissingCashbackParams,
  MissingCashbackControllerReportMissingCashbackData,
  MissingCashbackResponse,
  ReportMissingCashbackDto,
} from "./data-contracts";
import { ContentType, HttpClient, RequestParams } from "./http-client";

export class Click<
  SecurityDataType = unknown,
> extends HttpClient<SecurityDataType> {
  /**
   * No description
   *
   * @tags Click
   * @name ClickControllerCreate
   * @request POST:/click
   * @secure
   * @response `201` `ClickControllerCreateData`
   * @response `401` `void` Unauthorized
   * @response `default` `ClickCreateResponse`
   */
  clickControllerCreate = (data: CreateClickDto, params: RequestParams = {}) =>
    this.request<ClickControllerCreateData, void | ClickCreateResponse>({
      path: `/click`,
      method: "POST",
      body: data,
      secure: true,
      type: ContentType.Json,
      ...params,
    });
  /**
   * No description
   *
   * @tags Click
   * @name ClickControllerGetClicks
   * @request GET:/click
   * @secure
   * @response `200` `ClickControllerGetClicksData`
   * @response `401` `void` Unauthorized
   * @response `default` `ClicksResponse`
   */
  clickControllerGetClicks = (
    query: ClickControllerGetClicksParams,
    params: RequestParams = {},
  ) =>
    this.request<ClickControllerGetClicksData, void | ClicksResponse>({
      path: `/click`,
      method: "GET",
      query: query,
      secure: true,
      ...params,
    });
  /**
   * No description
   *
   * @tags Click
   * @name ClickControllerCreateNoAuth
   * @request POST:/click/no-auth
   * @response `201` `ClickControllerCreateNoAuthData`
   * @response `default` `ClickCreateResponse`
   */
  clickControllerCreateNoAuth = (
    data: CreateClickDto,
    params: RequestParams = {},
  ) =>
    this.request<ClickControllerCreateNoAuthData, ClickCreateResponse>({
      path: `/click/no-auth`,
      method: "POST",
      body: data,
      type: ContentType.Json,
      ...params,
    });
  /**
   * No description
   *
   * @tags Click
   * @name ClickControllerGetClicksByStores
   * @request GET:/click/stores
   * @secure
   * @response `200` `ClickControllerGetClicksByStoresData`
   * @response `401` `void` Unauthorized
   * @response `default` `ClicksByStoreResponse`
   */
  clickControllerGetClicksByStores = (
    query: ClickControllerGetClicksByStoresParams,
    params: RequestParams = {},
  ) =>
    this.request<
      ClickControllerGetClicksByStoresData,
      void | ClicksByStoreResponse
    >({
      path: `/click/stores`,
      method: "GET",
      query: query,
      secure: true,
      ...params,
    });
  /**
   * No description
   *
   * @tags Click
   * @name ClickControllerGetClickedStores
   * @request GET:/click/clicked_stores
   * @secure
   * @response `200` `ClickControllerGetClickedStoresData`
   * @response `401` `void` Unauthorized
   */
  clickControllerGetClickedStores = (params: RequestParams = {}) =>
    this.request<ClickControllerGetClickedStoresData, void>({
      path: `/click/clicked_stores`,
      method: "GET",
      secure: true,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags Click
   * @name MissingCashbackControllerReportMissingCashback
   * @request POST:/click/missing-cashback/report
   * @secure
   * @response `201` `MissingCashbackControllerReportMissingCashbackData`
   * @response `401` `void` Unauthorized
   * @response `default` `string`
   */
  missingCashbackControllerReportMissingCashback = (
    data: ReportMissingCashbackDto,
    params: RequestParams = {},
  ) =>
    this.request<
      MissingCashbackControllerReportMissingCashbackData,
      void | string
    >({
      path: `/click/missing-cashback/report`,
      method: "POST",
      body: data,
      secure: true,
      type: ContentType.FormData,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags Click
   * @name MissingCashbackControllerListMissingCashback
   * @request GET:/click/missing-cashback/list
   * @secure
   * @response `200` `MissingCashbackControllerListMissingCashbackData`
   * @response `401` `void` Unauthorized
   * @response `default` `MissingCashbackResponse`
   */
  missingCashbackControllerListMissingCashback = (
    query: MissingCashbackControllerListMissingCashbackParams,
    params: RequestParams = {},
  ) =>
    this.request<
      MissingCashbackControllerListMissingCashbackData,
      void | MissingCashbackResponse
    >({
      path: `/click/missing-cashback/list`,
      method: "GET",
      query: query,
      secure: true,
      ...params,
    });
}
