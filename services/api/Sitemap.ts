/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import { SitemapControllerGenerateSitemapData } from "./data-contracts";
import { HttpClient, RequestParams } from "./http-client";

export class Sitemap<
  SecurityDataType = unknown,
> extends HttpClient<SecurityDataType> {
  /**
   * No description
   *
   * @tags Sitemap
   * @name SitemapControllerGenerateSitemap
   * @request GET:/sitemap
   * @response `200` `SitemapControllerGenerateSitemapData`
   */
  sitemapControllerGenerateSitemap = (params: RequestParams = {}) =>
    this.request<SitemapControllerGenerateSitemapData, any>({
      path: `/sitemap`,
      method: "GET",
      format: "json",
      ...params,
    });
}
