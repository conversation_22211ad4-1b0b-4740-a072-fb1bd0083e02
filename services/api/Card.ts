/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import {
  CardAuthControllerCardLoginData,
  CardLoginDto,
  CardLoginResponseDto,
  WishListControllerJoinWishlistData,
  WishlistEmailDto,
  WishlistResponseDto,
} from "./data-contracts";
import { ContentType, HttpClient, RequestParams } from "./http-client";

export class Card<
  SecurityDataType = unknown,
> extends HttpClient<SecurityDataType> {
  /**
   * No description
   *
   * @tags WishList
   * @name WishListControllerJoinWishlist
   * @request POST:/card/wishlist/join
   * @response `201` `WishListControllerJoinWishlistData`
   * @response `default` `WishlistResponseDto`
   */
  wishListControllerJoinWishlist = (
    data: WishlistEmailDto,
    params: RequestParams = {},
  ) =>
    this.request<WishListControllerJoinWishlistData, WishlistResponseDto>({
      path: `/card/wishlist/join`,
      method: "POST",
      body: data,
      type: ContentType.Json,
      ...params,
    });
  /**
   * No description
   *
   * @tags Card Auth
   * @name CardAuthControllerCardLogin
   * @request POST:/card/auth/login
   * @response `201` `CardAuthControllerCardLoginData`
   * @response `default` `CardLoginResponseDto`
   */
  cardAuthControllerCardLogin = (
    data: CardLoginDto,
    params: RequestParams = {},
  ) =>
    this.request<CardAuthControllerCardLoginData, CardLoginResponseDto>({
      path: `/card/auth/login`,
      method: "POST",
      body: data,
      type: ContentType.Json,
      ...params,
    });
}
