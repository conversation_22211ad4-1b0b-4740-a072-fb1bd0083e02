/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import {
  AuthControllerCheckData,
  AuthControllerCreateData,
  AuthControllerGetCsrfTokenData,
  AuthControllerGoogleAuthCallbackData,
  AuthControllerGoogleAuthCallbackParams,
  AuthControllerGoogleAuthData,
  AuthControllerGoogleAuthParams,
  AuthControllerLoginData,
  AuthControllerLogoutData,
  AuthControllerResendOtpData,
  AuthControllerVerifyOtpData,
  AuthControllerVerifyTokenData,
  AuthControllerVerifyTokenParams,
  CreateUserDto,
  LoginDto,
  LoginResponse,
  UserResponse,
  VerifyUserDto,
} from "./data-contracts";
import { ContentType, HttpClient, RequestParams } from "./http-client";

export class Auth<
  SecurityDataType = unknown,
> extends HttpClient<SecurityDataType> {
  /**
   * No description
   *
   * @tags Auth
   * @name AuthControllerCreate
   * @request POST:/auth/register
   * @response `201` `AuthControllerCreateData`
   * @response `default` `UserResponse`
   */
  authControllerCreate = (data: CreateUserDto, params: RequestParams = {}) =>
    this.request<AuthControllerCreateData, UserResponse>({
      path: `/auth/register`,
      method: "POST",
      body: data,
      type: ContentType.Json,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags Auth
   * @name AuthControllerLogin
   * @request POST:/auth/login
   * @response `201` `AuthControllerLoginData`
   * @response `default` `LoginResponse`
   */
  authControllerLogin = (data: LoginDto, params: RequestParams = {}) =>
    this.request<AuthControllerLoginData, LoginResponse>({
      path: `/auth/login`,
      method: "POST",
      body: data,
      type: ContentType.Json,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags Auth
   * @name AuthControllerVerifyOtp
   * @request POST:/auth/verify-user
   * @response `201` `AuthControllerVerifyOtpData`
   * @response `default` `boolean`
   */
  authControllerVerifyOtp = (data: VerifyUserDto, params: RequestParams = {}) =>
    this.request<AuthControllerVerifyOtpData, boolean>({
      path: `/auth/verify-user`,
      method: "POST",
      body: data,
      type: ContentType.Json,
      ...params,
    });
  /**
   * No description
   *
   * @tags Auth
   * @name AuthControllerResendOtp
   * @request POST:/auth/resend-otp
   * @response `201` `AuthControllerResendOtpData`
   * @response `default` `LoginResponse`
   */
  authControllerResendOtp = (data: LoginDto, params: RequestParams = {}) =>
    this.request<AuthControllerResendOtpData, LoginResponse>({
      path: `/auth/resend-otp`,
      method: "POST",
      body: data,
      type: ContentType.Json,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags Auth
   * @name AuthControllerLogout
   * @request DELETE:/auth/logout
   * @secure
   * @response `200` `AuthControllerLogoutData`
   * @response `401` `void` Unauthorized
   */
  authControllerLogout = (params: RequestParams = {}) =>
    this.request<AuthControllerLogoutData, void>({
      path: `/auth/logout`,
      method: "DELETE",
      secure: true,
      ...params,
    });
  /**
   * No description
   *
   * @tags Auth
   * @name AuthControllerCheck
   * @request GET:/auth/check
   * @secure
   * @response `200` `AuthControllerCheckData`
   * @response `401` `void` Unauthorized
   */
  authControllerCheck = (params: RequestParams = {}) =>
    this.request<AuthControllerCheckData, void>({
      path: `/auth/check`,
      method: "GET",
      secure: true,
      ...params,
    });
  /**
   * No description
   *
   * @tags Auth
   * @name AuthControllerGetCsrfToken
   * @request GET:/auth/token
   * @response `200` `AuthControllerGetCsrfTokenData`
   */
  authControllerGetCsrfToken = (params: RequestParams = {}) =>
    this.request<AuthControllerGetCsrfTokenData, any>({
      path: `/auth/token`,
      method: "GET",
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags Auth
   * @name AuthControllerGoogleAuth
   * @request GET:/auth/google
   * @response `200` `AuthControllerGoogleAuthData`
   * @response `default` `void` Initiates Google OAuth authentication flow
   */
  authControllerGoogleAuth = (
    query: AuthControllerGoogleAuthParams,
    params: RequestParams = {},
  ) =>
    this.request<AuthControllerGoogleAuthData, void>({
      path: `/auth/google`,
      method: "GET",
      query: query,
      ...params,
    });
  /**
   * No description
   *
   * @tags Auth
   * @name AuthControllerGoogleAuthCallback
   * @request GET:/auth/google/callback
   * @response `200` `AuthControllerGoogleAuthCallbackData`
   * @response `default` `void` Handles the callback from Google OAuth authentication
   */
  authControllerGoogleAuthCallback = (
    query: AuthControllerGoogleAuthCallbackParams,
    params: RequestParams = {},
  ) =>
    this.request<AuthControllerGoogleAuthCallbackData, void>({
      path: `/auth/google/callback`,
      method: "GET",
      query: query,
      ...params,
    });
  /**
   * No description
   *
   * @tags Auth
   * @name AuthControllerVerifyToken
   * @request GET:/auth/verify-token
   * @response `200` `AuthControllerVerifyTokenData`
   */
  authControllerVerifyToken = (
    query: AuthControllerVerifyTokenParams,
    params: RequestParams = {},
  ) =>
    this.request<AuthControllerVerifyTokenData, any>({
      path: `/auth/verify-token`,
      method: "GET",
      query: query,
      ...params,
    });
}
