/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import {
  BannerControllerGetBannersData,
  CategoryControllerGetAllCategoriesData,
  CategoryControllerGetAllCategoriesDetailsData,
  CategoryControllerGetSubCategoryByCategoryIdData,
  CategoryControllerGetSubCategorySearchData,
  OffersControllerGetLandingOffersData,
  OffersControllerGetOnGoingSaleOffersData,
  QuickAccessControllerGetHeroQuickAccessesData,
  QuickAccessControllerGetQuickAccessesData,
  SearchControllerGetSearchResultsData,
  StoresControllerGetAllCategoriesData,
  StoresControllerGetContextStoresByCbData,
  StoriesControllerGetStoriesData,
  TermsAndPrivacyControllerGetAllTermsAndConditionsData,
  TestimonialControllerGetAllTestimonialsData,
} from "./data-contracts";

export namespace Context {
  /**
   * No description
   * @tags Context
   * @name BannerControllerGetBanners
   * @request GET:/context/banner
   * @response `200` `BannerControllerGetBannersData`
   * @response `default` `BannerResponse`
   */
  export namespace BannerControllerGetBanners {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = BannerControllerGetBannersData;
  }

  /**
   * No description
   * @tags Context
   * @name QuickAccessControllerGetQuickAccesses
   * @request GET:/context/quick-access
   * @response `200` `QuickAccessControllerGetQuickAccessesData`
   * @response `default` `(QuickAccessResponseItem)[]`
   */
  export namespace QuickAccessControllerGetQuickAccesses {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = QuickAccessControllerGetQuickAccessesData;
  }

  /**
   * No description
   * @tags Context
   * @name QuickAccessControllerGetHeroQuickAccesses
   * @request GET:/context/quick-access/hero
   * @response `200` `QuickAccessControllerGetHeroQuickAccessesData`
   * @response `default` `(HeroResponseItem)[]`
   */
  export namespace QuickAccessControllerGetHeroQuickAccesses {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = QuickAccessControllerGetHeroQuickAccessesData;
  }

  /**
   * No description
   * @tags Context
   * @name StoriesControllerGetStories
   * @request GET:/context/stories
   * @response `200` `StoriesControllerGetStoriesData`
   * @response `default` `(ResponseMobileStories)[]`
   */
  export namespace StoriesControllerGetStories {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = StoriesControllerGetStoriesData;
  }

  /**
   * No description
   * @tags Context
   * @name OffersControllerGetLandingOffers
   * @request GET:/context/offers
   * @secure
   * @response `200` `OffersControllerGetLandingOffersData`
   * @response `401` `void` Unauthorized
   * @response `default` `CategorizedOffers`
   */
  export namespace OffersControllerGetLandingOffers {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = OffersControllerGetLandingOffersData;
  }

  /**
   * No description
   * @tags Context
   * @name OffersControllerGetOnGoingSaleOffers
   * @request GET:/context/offers/all-on-going-offers
   * @response `200` `OffersControllerGetOnGoingSaleOffersData`
   * @response `default` `(GetAllOnGoingOffersResponse)[]`
   */
  export namespace OffersControllerGetOnGoingSaleOffers {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = OffersControllerGetOnGoingSaleOffersData;
  }

  /**
   * No description
   * @tags Context
   * @name CategoryControllerGetAllCategories
   * @request GET:/context/category
   * @response `200` `CategoryControllerGetAllCategoriesData`
   * @response `default` `(CategoryResponse)[]`
   */
  export namespace CategoryControllerGetAllCategories {
    export type RequestParams = {};
    export type RequestQuery = {
      trending: boolean;
    };
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = CategoryControllerGetAllCategoriesData;
  }

  /**
   * No description
   * @tags Context
   * @name CategoryControllerGetSubCategoryByCategoryId
   * @request GET:/context/category/sub-category{id}
   * @secure
   * @response `200` `CategoryControllerGetSubCategoryByCategoryIdData`
   * @response `401` `void` Unauthorized
   * @response `default` `SubCategoriesByCategoryResponse`
   */
  export namespace CategoryControllerGetSubCategoryByCategoryId {
    export type RequestParams = {
      id: string;
    };
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = CategoryControllerGetSubCategoryByCategoryIdData;
  }

  /**
   * No description
   * @tags Context
   * @name CategoryControllerGetSubCategorySearch
   * @request GET:/context/category/search{value}
   * @secure
   * @response `200` `CategoryControllerGetSubCategorySearchData`
   * @response `401` `void` Unauthorized
   * @response `default` `(SubCategoriesByCategory)[]`
   */
  export namespace CategoryControllerGetSubCategorySearch {
    export type RequestParams = {
      value: string;
    };
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = CategoryControllerGetSubCategorySearchData;
  }

  /**
   * No description
   * @tags Context
   * @name CategoryControllerGetAllCategoriesDetails
   * @request GET:/context/category/all-categories-details
   * @response `200` `CategoryControllerGetAllCategoriesDetailsData`
   * @response `default` `(AllCategoriesResponse)[]`
   */
  export namespace CategoryControllerGetAllCategoriesDetails {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = CategoryControllerGetAllCategoriesDetailsData;
  }

  /**
   * No description
   * @tags Context
   * @name StoresControllerGetContextStoresByCb
   * @request GET:/context/stores-by-cb-percent
   * @secure
   * @response `200` `StoresControllerGetContextStoresByCbData`
   * @response `401` `void` Unauthorized
   * @response `default` `StoresByCbContextResponse`
   */
  export namespace StoresControllerGetContextStoresByCb {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = StoresControllerGetContextStoresByCbData;
  }

  /**
   * No description
   * @tags Context
   * @name StoresControllerGetAllCategories
   * @request GET:/context/stores-by-cb-percent/find-by-category
   * @response `200` `StoresControllerGetAllCategoriesData`
   * @response `default` `(CategoryStoresResponse)[]`
   */
  export namespace StoresControllerGetAllCategories {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = StoresControllerGetAllCategoriesData;
  }

  /**
   * No description
   * @tags Context
   * @name SearchControllerGetSearchResults
   * @request GET:/context/search
   * @response `200` `SearchControllerGetSearchResultsData`
   * @response `default` `SearchResponseItem`
   */
  export namespace SearchControllerGetSearchResults {
    export type RequestParams = {};
    export type RequestQuery = {
      text: string;
    };
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = SearchControllerGetSearchResultsData;
  }

  /**
   * No description
   * @tags Context
   * @name TermsAndPrivacyControllerGetAllTermsAndConditions
   * @request GET:/context/terms-and-privacy
   * @response `200` `TermsAndPrivacyControllerGetAllTermsAndConditionsData`
   * @response `default` `TermsAndPrivacyResponseItem`
   */
  export namespace TermsAndPrivacyControllerGetAllTermsAndConditions {
    export type RequestParams = {};
    export type RequestQuery = {
      /** @example "terms" */
      type: string;
    };
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody =
      TermsAndPrivacyControllerGetAllTermsAndConditionsData;
  }

  /**
   * No description
   * @tags Context
   * @name TestimonialControllerGetAllTestimonials
   * @request GET:/context/testimonials
   * @response `200` `TestimonialControllerGetAllTestimonialsData`
   * @response `default` `(TestimonialResponseType)[]`
   */
  export namespace TestimonialControllerGetAllTestimonials {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = TestimonialControllerGetAllTestimonialsData;
  }
}
