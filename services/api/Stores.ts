/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import {
  CreateReviewDto,
  CreateReviewResponse,
  GetAllReviewsResponse,
  GetAllStoresResponse,
  GetCashbackRatesByStoreResponse,
  GetStoreDetailsResponse,
  RemoveOfferDto,
  ReviewControllerCreateData,
  ReviewControllerGetAllGiftCardsData,
  ReviewControllerGetAllGiftCardsParams,
  SaveItemResponse,
  SaveOfferDto,
  StoreControllerGetAllStoresData,
  StoreControllerGetAllStoresParams,
  StoreControllerGetCashbackRatesByStoreIdData,
  StoreControllerGetStoreDetailsByNameData,
  StoreControllerRemoveSavedItemData,
  StoreControllerSaveItemData,
} from "./data-contracts";
import { ContentType, HttpClient, RequestParams } from "./http-client";

export class Stores<
  SecurityDataType = unknown,
> extends HttpClient<SecurityDataType> {
  /**
   * No description
   *
   * @tags Stores
   * @name StoreControllerGetAllStores
   * @request GET:/stores
   * @secure
   * @response `200` `StoreControllerGetAllStoresData`
   * @response `401` `void` Unauthorized
   * @response `default` `GetAllStoresResponse`
   */
  storeControllerGetAllStores = (
    query: StoreControllerGetAllStoresParams,
    params: RequestParams = {},
  ) =>
    this.request<StoreControllerGetAllStoresData, void | GetAllStoresResponse>({
      path: `/stores`,
      method: "GET",
      query: query,
      secure: true,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags Stores
   * @name StoreControllerGetStoreDetailsByName
   * @request GET:/stores/store-details{name}
   * @secure
   * @response `200` `StoreControllerGetStoreDetailsByNameData`
   * @response `401` `void` Unauthorized
   * @response `default` `GetStoreDetailsResponse`
   */
  storeControllerGetStoreDetailsByName = (
    name: string,
    params: RequestParams = {},
  ) =>
    this.request<
      StoreControllerGetStoreDetailsByNameData,
      void | GetStoreDetailsResponse
    >({
      path: `/stores/store-details${name}`,
      method: "GET",
      secure: true,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags Stores
   * @name StoreControllerGetCashbackRatesByStoreId
   * @request GET:/stores/cashback-rates-by-store{id}
   * @response `200` `StoreControllerGetCashbackRatesByStoreIdData`
   * @response `default` `GetCashbackRatesByStoreResponse`
   */
  storeControllerGetCashbackRatesByStoreId = (
    id: string,
    params: RequestParams = {},
  ) =>
    this.request<
      StoreControllerGetCashbackRatesByStoreIdData,
      GetCashbackRatesByStoreResponse
    >({
      path: `/stores/cashback-rates-by-store${id}`,
      method: "GET",
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags Stores
   * @name StoreControllerSaveItem
   * @request POST:/stores/save
   * @secure
   * @response `201` `StoreControllerSaveItemData`
   * @response `401` `void` Unauthorized
   * @response `default` `SaveItemResponse`
   */
  storeControllerSaveItem = (data: SaveOfferDto, params: RequestParams = {}) =>
    this.request<StoreControllerSaveItemData, void | SaveItemResponse>({
      path: `/stores/save`,
      method: "POST",
      body: data,
      secure: true,
      type: ContentType.Json,
      ...params,
    });
  /**
   * No description
   *
   * @tags Stores
   * @name StoreControllerRemoveSavedItem
   * @request POST:/stores/remove
   * @secure
   * @response `201` `StoreControllerRemoveSavedItemData`
   * @response `401` `void` Unauthorized
   * @response `default` `SaveItemResponse`
   */
  storeControllerRemoveSavedItem = (
    data: RemoveOfferDto,
    params: RequestParams = {},
  ) =>
    this.request<StoreControllerRemoveSavedItemData, void | SaveItemResponse>({
      path: `/stores/remove`,
      method: "POST",
      body: data,
      secure: true,
      type: ContentType.Json,
      ...params,
    });
  /**
   * No description
   *
   * @tags Review
   * @name ReviewControllerCreate
   * @request POST:/stores/review/add-review
   * @secure
   * @response `201` `ReviewControllerCreateData`
   * @response `401` `void` Unauthorized
   * @response `default` `CreateReviewResponse`
   */
  reviewControllerCreate = (
    data: CreateReviewDto,
    params: RequestParams = {},
  ) =>
    this.request<ReviewControllerCreateData, void | CreateReviewResponse>({
      path: `/stores/review/add-review`,
      method: "POST",
      body: data,
      secure: true,
      type: ContentType.Json,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags Review
   * @name ReviewControllerGetAllGiftCards
   * @request GET:/stores/review
   * @response `200` `ReviewControllerGetAllGiftCardsData`
   * @response `default` `GetAllReviewsResponse`
   */
  reviewControllerGetAllGiftCards = (
    query: ReviewControllerGetAllGiftCardsParams,
    params: RequestParams = {},
  ) =>
    this.request<ReviewControllerGetAllGiftCardsData, GetAllReviewsResponse>({
      path: `/stores/review`,
      method: "GET",
      query: query,
      ...params,
    });
}
