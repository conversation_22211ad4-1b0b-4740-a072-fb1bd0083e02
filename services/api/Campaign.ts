/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import {
  GetReferralLeaderboardResponse,
  ReferralCampaignControllerGetReferralEarningsLeaderboardData,
  ReferralCampaignControllerGetReferralEarningsLeaderboardParams,
} from "./data-contracts";
import { HttpClient, RequestParams } from "./http-client";

export class Campaign<
  SecurityDataType = unknown,
> extends HttpClient<SecurityDataType> {
  /**
   * No description
   *
   * @tags ReferralCampaign
   * @name ReferralCampaignControllerGetReferralEarningsLeaderboard
   * @request GET:/campaign/referral-earnings-leaderboard
   * @response `200` `ReferralCampaignControllerGetReferralEarningsLeaderboardData`
   * @response `default` `(GetReferralLeaderboardResponse)[]`
   */
  referralCampaignControllerGetReferralEarningsLeaderboard = (
    query: ReferralCampaignControllerGetReferralEarningsLeaderboardParams,
    params: RequestParams = {},
  ) =>
    this.request<
      ReferralCampaignControllerGetReferralEarningsLeaderboardData,
      GetReferralLeaderboardResponse[]
    >({
      path: `/campaign/referral-earnings-leaderboard`,
      method: "GET",
      query: query,
      format: "json",
      ...params,
    });
}
