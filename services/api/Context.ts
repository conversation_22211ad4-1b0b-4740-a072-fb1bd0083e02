/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import {
  AllCategoriesResponse,
  BannerControllerGetBannersData,
  BannerResponse,
  CategorizedOffers,
  CategoryControllerGetAllCategoriesData,
  CategoryControllerGetAllCategoriesDetailsData,
  CategoryControllerGetAllCategoriesParams,
  CategoryControllerGetSubCategoryByCategoryIdData,
  CategoryControllerGetSubCategorySearchData,
  CategoryResponse,
  CategoryStoresResponse,
  GetAllOnGoingOffersResponse,
  HeroResponseItem,
  OffersControllerGetLandingOffersData,
  OffersControllerGetOnGoingSaleOffersData,
  QuickAccessControllerGetHeroQuickAccessesData,
  QuickAccessControllerGetQuickAccessesData,
  QuickAccessResponseItem,
  ResponseMobileStories,
  SearchControllerGetSearchResultsData,
  SearchControllerGetSearchResultsParams,
  SearchResponseItem,
  StoresByCbContextResponse,
  StoresControllerGetAllCategoriesData,
  StoresControllerGetContextStoresByCbData,
  StoriesControllerGetStoriesData,
  SubCategoriesByCategory,
  SubCategoriesByCategoryResponse,
  TermsAndPrivacyControllerGetAllTermsAndConditionsData,
  TermsAndPrivacyControllerGetAllTermsAndConditionsParams,
  TermsAndPrivacyResponseItem,
  TestimonialControllerGetAllTestimonialsData,
  TestimonialResponseType,
} from "./data-contracts";
import { HttpClient, RequestParams } from "./http-client";

export class Context<
  SecurityDataType = unknown,
> extends HttpClient<SecurityDataType> {
  /**
   * No description
   *
   * @tags Context
   * @name BannerControllerGetBanners
   * @request GET:/context/banner
   * @response `200` `BannerControllerGetBannersData`
   * @response `default` `BannerResponse`
   */
  bannerControllerGetBanners = (params: RequestParams = {}) =>
    this.request<BannerControllerGetBannersData, BannerResponse>({
      path: `/context/banner`,
      method: "GET",
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags Context
   * @name QuickAccessControllerGetQuickAccesses
   * @request GET:/context/quick-access
   * @response `200` `QuickAccessControllerGetQuickAccessesData`
   * @response `default` `(QuickAccessResponseItem)[]`
   */
  quickAccessControllerGetQuickAccesses = (params: RequestParams = {}) =>
    this.request<
      QuickAccessControllerGetQuickAccessesData,
      QuickAccessResponseItem[]
    >({
      path: `/context/quick-access`,
      method: "GET",
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags Context
   * @name QuickAccessControllerGetHeroQuickAccesses
   * @request GET:/context/quick-access/hero
   * @response `200` `QuickAccessControllerGetHeroQuickAccessesData`
   * @response `default` `(HeroResponseItem)[]`
   */
  quickAccessControllerGetHeroQuickAccesses = (params: RequestParams = {}) =>
    this.request<
      QuickAccessControllerGetHeroQuickAccessesData,
      HeroResponseItem[]
    >({
      path: `/context/quick-access/hero`,
      method: "GET",
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags Context
   * @name StoriesControllerGetStories
   * @request GET:/context/stories
   * @response `200` `StoriesControllerGetStoriesData`
   * @response `default` `(ResponseMobileStories)[]`
   */
  storiesControllerGetStories = (params: RequestParams = {}) =>
    this.request<StoriesControllerGetStoriesData, ResponseMobileStories[]>({
      path: `/context/stories`,
      method: "GET",
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags Context
   * @name OffersControllerGetLandingOffers
   * @request GET:/context/offers
   * @secure
   * @response `200` `OffersControllerGetLandingOffersData`
   * @response `401` `void` Unauthorized
   * @response `default` `CategorizedOffers`
   */
  offersControllerGetLandingOffers = (params: RequestParams = {}) =>
    this.request<
      OffersControllerGetLandingOffersData,
      void | CategorizedOffers
    >({
      path: `/context/offers`,
      method: "GET",
      secure: true,
      ...params,
    });
  /**
   * No description
   *
   * @tags Context
   * @name OffersControllerGetOnGoingSaleOffers
   * @request GET:/context/offers/all-on-going-offers
   * @response `200` `OffersControllerGetOnGoingSaleOffersData`
   * @response `default` `(GetAllOnGoingOffersResponse)[]`
   */
  offersControllerGetOnGoingSaleOffers = (params: RequestParams = {}) =>
    this.request<
      OffersControllerGetOnGoingSaleOffersData,
      GetAllOnGoingOffersResponse[]
    >({
      path: `/context/offers/all-on-going-offers`,
      method: "GET",
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags Context
   * @name CategoryControllerGetAllCategories
   * @request GET:/context/category
   * @response `200` `CategoryControllerGetAllCategoriesData`
   * @response `default` `(CategoryResponse)[]`
   */
  categoryControllerGetAllCategories = (
    query: CategoryControllerGetAllCategoriesParams,
    params: RequestParams = {},
  ) =>
    this.request<CategoryControllerGetAllCategoriesData, CategoryResponse[]>({
      path: `/context/category`,
      method: "GET",
      query: query,
      ...params,
    });
  /**
   * No description
   *
   * @tags Context
   * @name CategoryControllerGetSubCategoryByCategoryId
   * @request GET:/context/category/sub-category{id}
   * @secure
   * @response `200` `CategoryControllerGetSubCategoryByCategoryIdData`
   * @response `401` `void` Unauthorized
   * @response `default` `SubCategoriesByCategoryResponse`
   */
  categoryControllerGetSubCategoryByCategoryId = (
    id: string,
    params: RequestParams = {},
  ) =>
    this.request<
      CategoryControllerGetSubCategoryByCategoryIdData,
      void | SubCategoriesByCategoryResponse
    >({
      path: `/context/category/sub-category${id}`,
      method: "GET",
      secure: true,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags Context
   * @name CategoryControllerGetSubCategorySearch
   * @request GET:/context/category/search{value}
   * @secure
   * @response `200` `CategoryControllerGetSubCategorySearchData`
   * @response `401` `void` Unauthorized
   * @response `default` `(SubCategoriesByCategory)[]`
   */
  categoryControllerGetSubCategorySearch = (
    value: string,
    params: RequestParams = {},
  ) =>
    this.request<
      CategoryControllerGetSubCategorySearchData,
      void | SubCategoriesByCategory[]
    >({
      path: `/context/category/search${value}`,
      method: "GET",
      secure: true,
      ...params,
    });
  /**
   * No description
   *
   * @tags Context
   * @name CategoryControllerGetAllCategoriesDetails
   * @request GET:/context/category/all-categories-details
   * @response `200` `CategoryControllerGetAllCategoriesDetailsData`
   * @response `default` `(AllCategoriesResponse)[]`
   */
  categoryControllerGetAllCategoriesDetails = (params: RequestParams = {}) =>
    this.request<
      CategoryControllerGetAllCategoriesDetailsData,
      AllCategoriesResponse[]
    >({
      path: `/context/category/all-categories-details`,
      method: "GET",
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags Context
   * @name StoresControllerGetContextStoresByCb
   * @request GET:/context/stores-by-cb-percent
   * @secure
   * @response `200` `StoresControllerGetContextStoresByCbData`
   * @response `401` `void` Unauthorized
   * @response `default` `StoresByCbContextResponse`
   */
  storesControllerGetContextStoresByCb = (params: RequestParams = {}) =>
    this.request<
      StoresControllerGetContextStoresByCbData,
      void | StoresByCbContextResponse
    >({
      path: `/context/stores-by-cb-percent`,
      method: "GET",
      secure: true,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags Context
   * @name StoresControllerGetAllCategories
   * @request GET:/context/stores-by-cb-percent/find-by-category
   * @response `200` `StoresControllerGetAllCategoriesData`
   * @response `default` `(CategoryStoresResponse)[]`
   */
  storesControllerGetAllCategories = (params: RequestParams = {}) =>
    this.request<
      StoresControllerGetAllCategoriesData,
      CategoryStoresResponse[]
    >({
      path: `/context/stores-by-cb-percent/find-by-category`,
      method: "GET",
      ...params,
    });
  /**
   * No description
   *
   * @tags Context
   * @name SearchControllerGetSearchResults
   * @request GET:/context/search
   * @response `200` `SearchControllerGetSearchResultsData`
   * @response `default` `SearchResponseItem`
   */
  searchControllerGetSearchResults = (
    query: SearchControllerGetSearchResultsParams,
    params: RequestParams = {},
  ) =>
    this.request<SearchControllerGetSearchResultsData, SearchResponseItem>({
      path: `/context/search`,
      method: "GET",
      query: query,
      ...params,
    });
  /**
   * No description
   *
   * @tags Context
   * @name TermsAndPrivacyControllerGetAllTermsAndConditions
   * @request GET:/context/terms-and-privacy
   * @response `200` `TermsAndPrivacyControllerGetAllTermsAndConditionsData`
   * @response `default` `TermsAndPrivacyResponseItem`
   */
  termsAndPrivacyControllerGetAllTermsAndConditions = (
    query: TermsAndPrivacyControllerGetAllTermsAndConditionsParams,
    params: RequestParams = {},
  ) =>
    this.request<
      TermsAndPrivacyControllerGetAllTermsAndConditionsData,
      TermsAndPrivacyResponseItem
    >({
      path: `/context/terms-and-privacy`,
      method: "GET",
      query: query,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags Context
   * @name TestimonialControllerGetAllTestimonials
   * @request GET:/context/testimonials
   * @response `200` `TestimonialControllerGetAllTestimonialsData`
   * @response `default` `(TestimonialResponseType)[]`
   */
  testimonialControllerGetAllTestimonials = (params: RequestParams = {}) =>
    this.request<
      TestimonialControllerGetAllTestimonialsData,
      TestimonialResponseType[]
    >({
      path: `/context/testimonials`,
      method: "GET",
      format: "json",
      ...params,
    });
}
