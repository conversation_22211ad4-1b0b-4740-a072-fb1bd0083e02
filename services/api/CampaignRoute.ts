/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import {
  ReferralCampaignControllerGetReferralEarningsLeaderboardData,
  ReferralCampaignControllerGetReferralEarningsLeaderboardParams1TimeFrameEnum,
} from "./data-contracts";

export namespace Campaign {
  /**
   * No description
   * @tags ReferralCampaign
   * @name ReferralCampaignControllerGetReferralEarningsLeaderboard
   * @request GET:/campaign/referral-earnings-leaderboard
   * @response `200` `ReferralCampaignControllerGetReferralEarningsLeaderboardData`
   * @response `default` `(GetReferralLeaderboardResponse)[]`
   */
  export namespace ReferralCampaignControllerGetReferralEarningsLeaderboard {
    export type RequestParams = {};
    export type RequestQuery = {
      timeFrame: ReferralCampaignControllerGetReferralEarningsLeaderboardParams1TimeFrameEnum;
    };
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody =
      ReferralCampaignControllerGetReferralEarningsLeaderboardData;
  }
}
