/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import {
  GetPaymentListResponse,
  PaymentControllerGetAllPaymentRequestedUserData,
  PaymentControllerGetAllPaymentRequestedUserParams,
  PaymentControllerRequestPaymentsData,
  PaymentRequestDto,
} from "./data-contracts";
import { ContentType, HttpClient, RequestParams } from "./http-client";

export class Payment<
  SecurityDataType = unknown,
> extends HttpClient<SecurityDataType> {
  /**
   * No description
   *
   * @tags PaymentRequest
   * @name PaymentControllerRequestPayments
   * @request POST:/payment/withdraw
   * @secure
   * @response `201` `PaymentControllerRequestPaymentsData`
   * @response `401` `void` Unauthorized
   * @response `default` `boolean`
   */
  paymentControllerRequestPayments = (
    data: PaymentRequestDto,
    params: RequestParams = {},
  ) =>
    this.request<PaymentControllerRequestPaymentsData, void | boolean>({
      path: `/payment/withdraw`,
      method: "POST",
      body: data,
      secure: true,
      type: ContentType.Json,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags PaymentRequest
   * @name PaymentControllerGetAllPaymentRequestedUser
   * @request GET:/payment
   * @secure
   * @response `200` `PaymentControllerGetAllPaymentRequestedUserData`
   * @response `401` `void` Unauthorized
   * @response `default` `GetPaymentListResponse`
   */
  paymentControllerGetAllPaymentRequestedUser = (
    query: PaymentControllerGetAllPaymentRequestedUserParams,
    params: RequestParams = {},
  ) =>
    this.request<
      PaymentControllerGetAllPaymentRequestedUserData,
      void | GetPaymentListResponse
    >({
      path: `/payment`,
      method: "GET",
      query: query,
      secure: true,
      format: "json",
      ...params,
    });
}
