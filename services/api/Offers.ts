/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import {
  DealAndCouponsResponse,
  GetOfferByIdResponse,
  GetSimilarOffers,
  OfferControllerGetAllCategoriesData,
  OfferControllerGetAllCategoriesParams,
  OfferControllerGetOfferByIdData,
  OfferControllerGetOfferByTitleData,
  OfferControllerGetOngoingOffersData,
  OfferControllerGetOngoingOffersParams,
  OfferControllerRemoveSavedItemData,
  OfferControllerSaveItemData,
  OngoingOffersResponse,
  RemoveOfferDto,
  SaveItemResponse,
  SaveOfferDto,
} from "./data-contracts";
import { ContentType, HttpClient, RequestParams } from "./http-client";

export class Offers<
  SecurityDataType = unknown,
> extends HttpClient<SecurityDataType> {
  /**
   * No description
   *
   * @tags Offers
   * @name OfferControllerGetAllCategories
   * @request GET:/offers/deals-and-coupons
   * @secure
   * @response `200` `OfferControllerGetAllCategoriesData`
   * @response `401` `void` Unauthorized
   * @response `default` `DealAndCouponsResponse`
   */
  offerControllerGetAllCategories = (
    query: OfferControllerGetAllCategoriesParams,
    params: RequestParams = {},
  ) =>
    this.request<
      OfferControllerGetAllCategoriesData,
      void | DealAndCouponsResponse
    >({
      path: `/offers/deals-and-coupons`,
      method: "GET",
      query: query,
      secure: true,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags Offers
   * @name OfferControllerGetOngoingOffers
   * @request GET:/offers/ongoing-offers
   * @secure
   * @response `200` `OfferControllerGetOngoingOffersData`
   * @response `401` `void` Unauthorized
   * @response `default` `OngoingOffersResponse`
   */
  offerControllerGetOngoingOffers = (
    query: OfferControllerGetOngoingOffersParams,
    params: RequestParams = {},
  ) =>
    this.request<
      OfferControllerGetOngoingOffersData,
      void | OngoingOffersResponse
    >({
      path: `/offers/ongoing-offers`,
      method: "GET",
      query: query,
      secure: true,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags Offers
   * @name OfferControllerGetOfferById
   * @request GET:/offers/offer{uid}
   * @secure
   * @response `200` `OfferControllerGetOfferByIdData`
   * @response `401` `void` Unauthorized
   * @response `default` `GetOfferByIdResponse`
   */
  offerControllerGetOfferById = (uid: number, params: RequestParams = {}) =>
    this.request<OfferControllerGetOfferByIdData, void | GetOfferByIdResponse>({
      path: `/offers/offer${uid}`,
      method: "GET",
      secure: true,
      ...params,
    });
  /**
   * No description
   *
   * @tags Offers
   * @name OfferControllerGetOfferByTitle
   * @request GET:/offers/offer/title/{title}
   * @secure
   * @response `200` `OfferControllerGetOfferByTitleData`
   * @response `401` `void` Unauthorized
   * @response `default` `GetSimilarOffers`
   */
  offerControllerGetOfferByTitle = (
    title: string,
    params: RequestParams = {},
  ) =>
    this.request<OfferControllerGetOfferByTitleData, void | GetSimilarOffers>({
      path: `/offers/offer/title/${title}`,
      method: "GET",
      secure: true,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags Offers
   * @name OfferControllerSaveItem
   * @request POST:/offers/save
   * @secure
   * @response `201` `OfferControllerSaveItemData`
   * @response `401` `void` Unauthorized
   * @response `default` `SaveItemResponse`
   */
  offerControllerSaveItem = (data: SaveOfferDto, params: RequestParams = {}) =>
    this.request<OfferControllerSaveItemData, void | SaveItemResponse>({
      path: `/offers/save`,
      method: "POST",
      body: data,
      secure: true,
      type: ContentType.Json,
      ...params,
    });
  /**
   * No description
   *
   * @tags Offers
   * @name OfferControllerRemoveSavedItem
   * @request POST:/offers/remove
   * @secure
   * @response `201` `OfferControllerRemoveSavedItemData`
   * @response `401` `void` Unauthorized
   * @response `default` `SaveItemResponse`
   */
  offerControllerRemoveSavedItem = (
    data: RemoveOfferDto,
    params: RequestParams = {},
  ) =>
    this.request<OfferControllerRemoveSavedItemData, void | SaveItemResponse>({
      path: `/offers/remove`,
      method: "POST",
      body: data,
      secure: true,
      type: ContentType.Json,
      ...params,
    });
}
