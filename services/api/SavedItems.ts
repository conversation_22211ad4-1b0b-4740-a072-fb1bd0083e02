/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import {
  GetAllStoresResponse,
  GetGiftCardListResponse,
  SavedCouponsResponse,
  SavedDealsResponse,
  SavedItemControllerGetAllSavedCouponsData,
  SavedItemControllerGetAllSavedCouponsParams,
  SavedItemControllerGetAllSavedDealsData,
  SavedItemControllerGetAllSavedDealsParams,
  SavedItemControllerGetAllSavedGiftCardsData,
  SavedItemControllerGetAllSavedGiftCardsParams,
  SavedItemControllerGetAllSavedOfferUidsData,
  SavedItemControllerGetAllSavedStoresData,
  SavedItemControllerGetAllSavedStoresParams,
} from "./data-contracts";
import { HttpClient, RequestParams } from "./http-client";

export class SavedItems<
  SecurityDataType = unknown,
> extends HttpClient<SecurityDataType> {
  /**
   * No description
   *
   * @tags SavedItems
   * @name SavedItemControllerGetAllSavedDeals
   * @request GET:/saved-items/deals
   * @secure
   * @response `200` `SavedItemControllerGetAllSavedDealsData`
   * @response `401` `void` Unauthorized
   * @response `default` `SavedDealsResponse`
   */
  savedItemControllerGetAllSavedDeals = (
    query: SavedItemControllerGetAllSavedDealsParams,
    params: RequestParams = {},
  ) =>
    this.request<
      SavedItemControllerGetAllSavedDealsData,
      void | SavedDealsResponse
    >({
      path: `/saved-items/deals`,
      method: "GET",
      query: query,
      secure: true,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags SavedItems
   * @name SavedItemControllerGetAllSavedOfferUids
   * @request GET:/saved-items/saved-offer-ids
   * @secure
   * @response `200` `SavedItemControllerGetAllSavedOfferUidsData`
   * @response `401` `void` Unauthorized
   * @response `default` `(number)[]`
   */
  savedItemControllerGetAllSavedOfferUids = (params: RequestParams = {}) =>
    this.request<SavedItemControllerGetAllSavedOfferUidsData, void | number[]>({
      path: `/saved-items/saved-offer-ids`,
      method: "GET",
      secure: true,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags SavedItems
   * @name SavedItemControllerGetAllSavedCoupons
   * @request GET:/saved-items/coupons
   * @secure
   * @response `200` `SavedItemControllerGetAllSavedCouponsData`
   * @response `401` `void` Unauthorized
   * @response `default` `SavedCouponsResponse`
   */
  savedItemControllerGetAllSavedCoupons = (
    query: SavedItemControllerGetAllSavedCouponsParams,
    params: RequestParams = {},
  ) =>
    this.request<
      SavedItemControllerGetAllSavedCouponsData,
      void | SavedCouponsResponse
    >({
      path: `/saved-items/coupons`,
      method: "GET",
      query: query,
      secure: true,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags SavedItems
   * @name SavedItemControllerGetAllSavedStores
   * @request GET:/saved-items/stores
   * @secure
   * @response `200` `SavedItemControllerGetAllSavedStoresData`
   * @response `401` `void` Unauthorized
   * @response `default` `GetAllStoresResponse`
   */
  savedItemControllerGetAllSavedStores = (
    query: SavedItemControllerGetAllSavedStoresParams,
    params: RequestParams = {},
  ) =>
    this.request<
      SavedItemControllerGetAllSavedStoresData,
      void | GetAllStoresResponse
    >({
      path: `/saved-items/stores`,
      method: "GET",
      query: query,
      secure: true,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags SavedItems
   * @name SavedItemControllerGetAllSavedGiftCards
   * @request GET:/saved-items/gift-cards
   * @secure
   * @response `200` `SavedItemControllerGetAllSavedGiftCardsData`
   * @response `401` `void` Unauthorized
   * @response `default` `GetGiftCardListResponse`
   */
  savedItemControllerGetAllSavedGiftCards = (
    query: SavedItemControllerGetAllSavedGiftCardsParams,
    params: RequestParams = {},
  ) =>
    this.request<
      SavedItemControllerGetAllSavedGiftCardsData,
      void | GetGiftCardListResponse
    >({
      path: `/saved-items/gift-cards`,
      method: "GET",
      query: query,
      secure: true,
      format: "json",
      ...params,
    });
}
