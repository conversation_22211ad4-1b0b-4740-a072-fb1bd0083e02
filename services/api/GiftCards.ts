/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import {
  GetGiftCardListResponse,
  GetGiftCardResponse,
  GetRedeemGiftCardListResponse,
  GiftCardBannersResponse,
  GiftCardControllerGetAllGiftCardBannersData,
  GiftCardControllerGetAllGiftCardsData,
  GiftCardControllerGetAllGiftCardsParams,
  GiftCardControllerGetGiftCardDetailsData,
  GiftCardControllerGetIcbCardData,
  GiftCardControllerOrderGiftCardData,
  GiftCardControllerRedeemGiftCardHistoryData,
  GiftCardControllerRedeemGiftCardHistoryParams,
  GiftCardControllerRedeemIcbGiftCardData,
  GiftCardControllerRemoveSavedItemData,
  GiftCardControllerSaveItemData,
  GiftCardControllerVerifyPaymentData,
  IcbCardTypeResponse,
  OrderGiftCardDto,
  OrderResponse,
  PaymentVerifyDto,
  RedeemIcbGiftCardDto,
  RemoveOfferDto,
  SaveItemResponse,
  SaveOfferDto,
} from "./data-contracts";
import { ContentType, HttpClient, RequestParams } from "./http-client";

export class GiftCards<
  SecurityDataType = unknown,
> extends HttpClient<SecurityDataType> {
  /**
   * No description
   *
   * @tags GiftCards
   * @name GiftCardControllerGetAllGiftCards
   * @request GET:/gift-cards
   * @secure
   * @response `200` `GiftCardControllerGetAllGiftCardsData`
   * @response `401` `void` Unauthorized
   * @response `default` `GetGiftCardListResponse`
   */
  giftCardControllerGetAllGiftCards = (
    query: GiftCardControllerGetAllGiftCardsParams,
    params: RequestParams = {},
  ) =>
    this.request<
      GiftCardControllerGetAllGiftCardsData,
      void | GetGiftCardListResponse
    >({
      path: `/gift-cards`,
      method: "GET",
      query: query,
      secure: true,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags GiftCards
   * @name GiftCardControllerGetAllGiftCardBanners
   * @request GET:/gift-cards/banners
   * @response `200` `GiftCardControllerGetAllGiftCardBannersData`
   * @response `default` `GiftCardBannersResponse`
   */
  giftCardControllerGetAllGiftCardBanners = (params: RequestParams = {}) =>
    this.request<
      GiftCardControllerGetAllGiftCardBannersData,
      GiftCardBannersResponse
    >({
      path: `/gift-cards/banners`,
      method: "GET",
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags GiftCards
   * @name GiftCardControllerGetGiftCardDetails
   * @request GET:/gift-cards/gift-card{id}
   * @secure
   * @response `200` `GiftCardControllerGetGiftCardDetailsData`
   * @response `401` `void` Unauthorized
   * @response `default` `GetGiftCardResponse`
   */
  giftCardControllerGetGiftCardDetails = (
    id: string,
    params: RequestParams = {},
  ) =>
    this.request<
      GiftCardControllerGetGiftCardDetailsData,
      void | GetGiftCardResponse
    >({
      path: `/gift-cards/gift-card${id}`,
      method: "GET",
      secure: true,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags GiftCards
   * @name GiftCardControllerSaveItem
   * @request POST:/gift-cards/save
   * @secure
   * @response `201` `GiftCardControllerSaveItemData`
   * @response `401` `void` Unauthorized
   * @response `default` `SaveItemResponse`
   */
  giftCardControllerSaveItem = (
    data: SaveOfferDto,
    params: RequestParams = {},
  ) =>
    this.request<GiftCardControllerSaveItemData, void | SaveItemResponse>({
      path: `/gift-cards/save`,
      method: "POST",
      body: data,
      secure: true,
      type: ContentType.Json,
      ...params,
    });
  /**
   * No description
   *
   * @tags GiftCards
   * @name GiftCardControllerRemoveSavedItem
   * @request POST:/gift-cards/remove
   * @secure
   * @response `201` `GiftCardControllerRemoveSavedItemData`
   * @response `401` `void` Unauthorized
   * @response `default` `SaveItemResponse`
   */
  giftCardControllerRemoveSavedItem = (
    data: RemoveOfferDto,
    params: RequestParams = {},
  ) =>
    this.request<
      GiftCardControllerRemoveSavedItemData,
      void | SaveItemResponse
    >({
      path: `/gift-cards/remove`,
      method: "POST",
      body: data,
      secure: true,
      type: ContentType.Json,
      ...params,
    });
  /**
   * No description
   *
   * @tags GiftCards
   * @name GiftCardControllerOrderGiftCard
   * @request POST:/gift-cards/create-order
   * @secure
   * @response `201` `GiftCardControllerOrderGiftCardData`
   * @response `401` `void` Unauthorized
   * @response `default` `OrderResponse`
   */
  giftCardControllerOrderGiftCard = (
    data: OrderGiftCardDto,
    params: RequestParams = {},
  ) =>
    this.request<GiftCardControllerOrderGiftCardData, void | OrderResponse>({
      path: `/gift-cards/create-order`,
      method: "POST",
      body: data,
      secure: true,
      type: ContentType.Json,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags GiftCards
   * @name GiftCardControllerVerifyPayment
   * @request PUT:/gift-cards/verify-payment
   * @secure
   * @response `200` `GiftCardControllerVerifyPaymentData`
   * @response `401` `void` Unauthorized
   */
  giftCardControllerVerifyPayment = (
    data: PaymentVerifyDto,
    params: RequestParams = {},
  ) =>
    this.request<GiftCardControllerVerifyPaymentData, void>({
      path: `/gift-cards/verify-payment`,
      method: "PUT",
      body: data,
      secure: true,
      type: ContentType.Json,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags GiftCards
   * @name GiftCardControllerRedeemIcbGiftCard
   * @request POST:/gift-cards/redeem
   * @secure
   * @response `201` `GiftCardControllerRedeemIcbGiftCardData`
   * @response `401` `void` Unauthorized
   * @response `default` `SaveItemResponse`
   */
  giftCardControllerRedeemIcbGiftCard = (
    data: RedeemIcbGiftCardDto,
    params: RequestParams = {},
  ) =>
    this.request<
      GiftCardControllerRedeemIcbGiftCardData,
      void | SaveItemResponse
    >({
      path: `/gift-cards/redeem`,
      method: "POST",
      body: data,
      secure: true,
      type: ContentType.Json,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags GiftCards
   * @name GiftCardControllerRedeemGiftCardHistory
   * @request GET:/gift-cards/redeem
   * @secure
   * @response `200` `GiftCardControllerRedeemGiftCardHistoryData`
   * @response `401` `void` Unauthorized
   * @response `default` `GetRedeemGiftCardListResponse`
   */
  giftCardControllerRedeemGiftCardHistory = (
    query: GiftCardControllerRedeemGiftCardHistoryParams,
    params: RequestParams = {},
  ) =>
    this.request<
      GiftCardControllerRedeemGiftCardHistoryData,
      void | GetRedeemGiftCardListResponse
    >({
      path: `/gift-cards/redeem`,
      method: "GET",
      query: query,
      secure: true,
      ...params,
    });
  /**
   * No description
   *
   * @tags GiftCards
   * @name GiftCardControllerGetIcbCard
   * @request GET:/gift-cards/icb-card
   * @response `200` `GiftCardControllerGetIcbCardData`
   * @response `default` `IcbCardTypeResponse`
   */
  giftCardControllerGetIcbCard = (params: RequestParams = {}) =>
    this.request<GiftCardControllerGetIcbCardData, IcbCardTypeResponse>({
      path: `/gift-cards/icb-card`,
      method: "GET",
      ...params,
    });
}
