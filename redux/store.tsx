import { configureStore } from '@reduxjs/toolkit';
import storiesSlice from './slices/stories-slice';
import globalSearchSlice from './slices/global-search-slice';
import mainHeader from './slices/main-header-slice';
import reportMissingCbSlice from './slices/report-missing-cb-slice';
import authSlice from './slices/auth-slice';
import categoriesListSlice from './slices/categories-list-slice';
import commonFiltersSlice from './slices/common-filters-slice';
import globalSlice from './slices/global-slice';
import savedOffersSlice from './slices/offer-slice';
import earningToolbarSlice from './slices/earnings-toolbar-slice';

export const store = configureStore({
  reducer: {
    global: globalSlice,
    stories: storiesSlice,
    globalSearch: globalSearchSlice,
    mainHeader: mainHeader,
    reportMissingCbStep: reportMissingCbSlice,
    auth: authSlice,
    categoriesList: categoriesListSlice,
    commonFilters: commonFiltersSlice,
    offer: savedOffersSlice,
    earningsToolbar: earningToolbarSlice,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
