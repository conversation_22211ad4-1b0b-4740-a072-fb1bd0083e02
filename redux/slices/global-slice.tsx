import {
  type ClickCreateResponse,
  ClickTypeTypeEnum,
} from '@/services/api/data-contracts';
import { createSlice, type PayloadAction } from '@reduxjs/toolkit';

export interface GlobalSliceType {
  isGlobalLoading: boolean;
  showWarningModal: boolean;
  showClickRegisModal: boolean;
  clickGeneratedData: {
    logo: string;
    offer?: string;
    referenceId: string;
    url: string;
  };
  selectedOffer: { uid: number; type: ClickTypeTypeEnum; couponCode?: string };
  hasProceedWithoutCb: boolean;
}

const initialState: GlobalSliceType = {
  isGlobalLoading: false,
  showWarningModal: false,
  showClickRegisModal: false,
  clickGeneratedData: {
    logo: '',
    referenceId: '',
    url: '',
  },
  selectedOffer: { uid: -1, type: ClickTypeTypeEnum.Offer, couponCode: '' },
  hasProceedWithoutCb: false,
};

const globalSlice = createSlice({
  name: 'global',
  initialState,
  reducers: {
    setShowWarningModal: (state, action: PayloadAction<boolean>) => {
      state.showWarningModal = action.payload;
    },
    setShowClickRegisModal: (state, action: PayloadAction<boolean>) => {
      state.showClickRegisModal = action.payload;
    },
    setClickGeneratedData: (
      state,
      action: PayloadAction<ClickCreateResponse>
    ) => {
      state.clickGeneratedData = action.payload;
    },
    setGlobalLoading: (state, action: PayloadAction<boolean>) => {
      state.isGlobalLoading = action.payload;
    },
    setSelectedOffer: (
      state,
      action: PayloadAction<{
        uid: number;
        type: ClickTypeTypeEnum;
        couponCode?: string;
      }>
    ) => {
      state.selectedOffer = action.payload;
    },
    setProceedWithoutCb: (state, action: PayloadAction<boolean>) => {
      state.hasProceedWithoutCb = action.payload;
    },
  },
});

export const {
  setShowWarningModal,
  setClickGeneratedData,
  setGlobalLoading,
  setShowClickRegisModal,
  setSelectedOffer,
  setProceedWithoutCb,
} = globalSlice.actions;

export default globalSlice.reducer;
