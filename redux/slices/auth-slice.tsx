import { PersonalInterestTypes } from '@/services/api/data-contracts';
import { UserDetailsType } from '@/types/global-types';
import { PayloadAction, createSlice } from '@reduxjs/toolkit';

const initialState: {
  isLoading: boolean;
  isUserLogin: boolean;
  isLoginModalOpen: boolean;
  isLoginModalClosable: boolean;
  screen: { for: 'login' | 'signup'; step: 1 | 2 | 3 };
  userDetails: UserDetailsType;
  refetchUserDetails: boolean;
} = {
  isLoading: false,
  isUserLogin: false,
  isLoginModalOpen: false,
  isLoginModalClosable: true,
  screen: { for: 'login', step: 1 },
  userDetails: {
    cancelledCount: 0,
    avatar: '',
    name: '',
    email: '',
    mobile: '',
    personalInterest: [],
    referralCode: '',
    pendingCount: 0,
    confirmedCount: 0,
    balance: 0,
    sendNotification: true,
  },
  refetchUserDetails: false,
};

function updatePersonalInterests(
  personalInterests: PersonalInterestTypes[],
  item: PersonalInterestTypes
): PersonalInterestTypes[] {
  /** Finds the index of an existing item with the same id  */
  const existingIndex = personalInterests.findIndex(
    (interest) => interest.id === item.id
  );

  /** If the item already exists, remove it */
  if (existingIndex !== -1) {
    return [
      ...personalInterests.slice(0, existingIndex),
      ...personalInterests.slice(existingIndex + 1),
    ];
  } else {
    /** If the item doesn't exist, add it to the list */
    return [...personalInterests, item];
  }
}

export const authSlice = createSlice({
  name: 'auth',
  initialState: initialState,
  reducers: {
    setIsUserLogin: (state, action: PayloadAction<boolean>) => {
      state.isUserLogin = action.payload;
    },
    setLoginModalOpen: (state, action: PayloadAction<boolean>) => {
      state.isLoginModalOpen = action.payload;
    },
    setLoginModalClosable: (state, action: PayloadAction<boolean>) => {
      state.isLoginModalClosable = action.payload;
    },
    setLoginSignupScreen: (
      state,
      action: PayloadAction<(typeof initialState)['screen']>
    ) => {
      state.screen = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setUserDetails: (
      state,
      action: PayloadAction<Partial<UserDetailsType>>
    ) => {
      const { payload } = action;
      const newUserDetails = {
        ...state.userDetails,
        ...(payload?.avatar && { avatar: payload.avatar }),
        ...(payload?.balance && { balance: payload.balance }),
        ...(payload?.pendingCount && {
          pendingCount: payload.pendingCount,
        }),
        ...(payload?.confirmedCount && {
          confirmedCount: payload.confirmedCount,
        }),
        ...(payload?.email !== undefined && { email: payload.email }),
        ...(payload?.mobile !== undefined && { mobile: payload.mobile }),
        ...(payload?.name !== undefined && { name: payload.name }),
        ...(payload?.personalInterest && {
          personalInterest: payload.personalInterest,
        }),
        ...(payload?.referralCode && { referralCode: payload.referralCode }),
        ...(payload?.sendNotification !== undefined && {
          sendNotification: payload.sendNotification,
        }),
      };

      state.userDetails = newUserDetails;
    },

    toggleUserPersonalInterest: (
      state,
      action: PayloadAction<PersonalInterestTypes>
    ) => {
      const { payload } = action;
      let newPersonalDetails: PersonalInterestTypes[] = [];
      if (payload?.id && payload?.name) {
        newPersonalDetails = updatePersonalInterests(
          state.userDetails.personalInterest,
          payload
        );
      }
      state.userDetails.personalInterest = newPersonalDetails;
    },
    setRefetchUserDetails: (state, action: PayloadAction<boolean>) => {
      state.refetchUserDetails = action.payload;
    },
    clearAuthState: (state) => {
      // Reset all auth-related state to initial values
      state.isUserLogin = false;
      state.isLoginModalOpen = false;
      state.isLoginModalClosable = true;
      state.screen = { for: 'login', step: 1 };
      state.userDetails = {
        cancelledCount: 0,
        avatar: '',
        name: '',
        email: '',
        mobile: '',
        personalInterest: [],
        referralCode: '',
        pendingCount: 0,
        confirmedCount: 0,
        balance: 0,
        sendNotification: true,
      };
      state.refetchUserDetails = false;
    },
  },
});

export const {
  setUserDetails,
  toggleUserPersonalInterest,
  setIsUserLogin,
  setLoginModalOpen,
  setLoginModalClosable,
  setLoginSignupScreen,
  setLoading,
  setRefetchUserDetails,
  clearAuthState,
} = authSlice.actions;
export default authSlice.reducer;
