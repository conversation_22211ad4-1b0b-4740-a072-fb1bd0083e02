import { ClickedStores } from '@/app/(protected-pages)/(my-earnings)/click-history/page';
import { PayloadAction, createSlice } from '@reduxjs/toolkit';

// Define the type for sortItems
type SortItem = {
  label: string;
  key: string;
};

const initialState: {
  title: string;
  sortItems: SortItem[];
  statusItems: string[];
  hideSearchFilter: boolean;
  hideSortFilter: boolean;
  singleDatePicker: boolean;
  hideSlidingUI: boolean;
  showStoreFilter: boolean;
  showStatusFilter: boolean;
  totalFiltersApplied: number;
  storesList: ClickedStores[];
  statusList: string[];
} = {
  title: '',
  sortItems: [],
  statusItems: [],
  hideSearchFilter: false,
  hideSortFilter: false,
  singleDatePicker: false,
  hideSlidingUI: false,
  showStatusFilter: true,
  showStoreFilter: true,
  totalFiltersApplied: 0,
  storesList: [],
  statusList: [],
};

export const earningToolbarSlice = createSlice({
  name: 'earningToolbarSlice',
  initialState: initialState,
  reducers: {
    setSortItems: (state, action: PayloadAction<SortItem[]>) => {
      state.sortItems = action.payload;
    },
    setStatusItems: (state, action: PayloadAction<string[]>) => {
      state.statusItems = action.payload;
    },
    setHideSearchFilter: (state, action: PayloadAction<boolean>) => {
      state.hideSearchFilter = action.payload;
    },
    setHideSortFilter: (state, action: PayloadAction<boolean>) => {
      state.hideSortFilter = action.payload;
    },
    setSingleDatePicker: (state, action: PayloadAction<boolean>) => {
      state.singleDatePicker = action.payload;
    },
    setHideSlidingUI: (state, action: PayloadAction<boolean>) => {
      state.hideSlidingUI = action.payload;
    },
    setShowStoresFilter: (state, action: PayloadAction<boolean>) => {
      state.showStoreFilter = action.payload;
    },
    setShowStatusFilter: (state, action: PayloadAction<boolean>) => {
      state.showStatusFilter = action.payload;
    },
    setTitle: (state, action: PayloadAction<string>) => {
      state.title = action.payload;
    },
    setTotalFiltersApplied: (state, action: PayloadAction<number>) => {
      state.totalFiltersApplied = action.payload;
    },
    setStoresList: (state, action: PayloadAction<ClickedStores[]>) => {
      state.storesList = action.payload;
    },
    setStatusList: (state, action: PayloadAction<string[]>) => {
      state.statusList = action.payload;
    },
  },
});

export const {
  setSortItems,
  setStatusItems,
  setHideSearchFilter,
  setHideSortFilter,
  setSingleDatePicker,
  setHideSlidingUI,
  setTitle,
  setTotalFiltersApplied,
  setStoresList,
  setStatusList,
  setShowStoresFilter,
} = earningToolbarSlice.actions;
export default earningToolbarSlice.reducer;
