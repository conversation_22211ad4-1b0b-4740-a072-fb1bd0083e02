// example for conditional type such that if category1 pass in argument then we have to pass defined arguments,
// and if category2 passed then we have to pass some other arguments

import {
  OfferControllerGetAllCategoriesParams,
  MobileStoryResponse,
  OfferCouponsType,
  OfferDealsType,
  SubCategoriesByCategoryResponse,
  OfferControllerGetAllCategoriesParams,
  MobileStoryResponse,
  OfferCouponsType,
  OfferDealsType,
  SubCategoriesByCategoryResponse,
  GetUserProfileResponseItem,
} from '@/services/api/data-contracts';

// type CategoryProps<T extends string> = T extends 'category1'
//   ? { category: T; arg1: string; arg2: number }
//   : T extends 'category2'
//   ? { category: T; arg3: boolean; arg4: string }
//   : { category: T };

// interface MyComponentProps<T extends string> {
//   // Common props for all categories
//   commonProp: string;
//   // Use the CategoryProps type for specific categories
//   specificProps: CategoryProps<T>;
// }

// function MyComponent<T extends string>(props: MyComponentProps<T>) {
//   const { commonProp, specificProps } = props;

//   // Access specificProps based on the category
//   if (specificProps.category === 'category1') {
//     const { arg1, arg2 } = specificProps;
//     // Render for category1
//   } else if (specificProps.category === 'category2') {
//     const { arg3, arg4 } = specificProps;
//     // Render for category2
//   }

//   // Render common content
//   return <div>{commonProp}</div>;
// }

// // Example usage:
// const component1 = <MyComponent commonProp="Common" specificProps={{ category: 'category1', arg1: 'value', arg2: 42 }} />;
// const component2 = <MyComponent commonProp="Common" specificProps={{ category: 'category2', arg3: true, arg4: 'value' }} />;

export type PromiseStatus = 'fulfilled' | 'rejected';

export type SidebarFilterType =
  | 'user'
  | 'offer'
  | 'category'
  | 'sale'
  | 'percentage'
  | 'clickStatus';

type SidebarFilterPropsMap = {
  user: {
    filter: 'user';
  };
  offer: {
    filter: 'offer';
  };
  category: {
    filter: 'category';
  };
  sale: {
    filter: 'sale';
  };
  percentage: {
    filter: 'percentage';
  };
  clickStatus: {
    filter: 'clickStatus';
  };
};

type FilterProps<T extends SidebarFilterType> =
  T extends keyof SidebarFilterPropsMap ? SidebarFilterPropsMap[T] : never;

export interface SidebarFilterProps {
  rootClass?: string;
  filterProps: Array<FilterProps<SidebarFilterType>>;
}

export interface MobileFilterProps
  extends Omit<SidebarFilterProps, 'rootClass'> {
  setShowFilterModal: () => void;
  isShowFilterModal: boolean;
}

export interface ProductProps {
  storeLogoUrl: string;
  storeName: string;
  productImg: string;
  offerTitle: React.ReactNode;
  offerPercent?: number;
  offerAmount?: number;
  newUser?: number;
  oldUser?: number;
  appliedPrice?: number;
  offerType: string;
  offerEndsIn?: string;
  offerProductHandler: () => void;
  description?: React.ReactNode;
  offerCaption?: string;
  isExpired?: boolean;
  isAutoGenerated?: boolean;
  hideCbTag?: boolean;
}

export interface CommonPropsSelectMultipleOptions {
  placeholder?: string;
  data: Array<string>;
  selectedItems?: Array<string>;
  onSelectItem?: (input: string) => void;
}

type ConditionalPropsSelectMultipleOptions =
  | {
      isSearchable: false;
    }
  | {
      isSearchable: true;
      searchValue: string;
      onChangeSearch: (input: string) => void;
      onClose?: () => void;
    };

type SelectMultipleOptionsProps = CommonPropsSelectMultipleOptions &
  ConditionalPropsSelectMultipleOptions;

export interface MyBannerResponse {
  desktopBanners: BannerItem[];
  mobileBanners: BannerItem[];
}

export interface BannerItem {
  imageUrl: string;
  redirectUrl: string;
}

export interface ResponseStoriesData {
  storeLogo: string;
  storeName: string;
  stories: MobileStoryResponse[];
}

export interface trendingOffersType {
  coupons: OfferCouponsType[];
  deals: OfferDealsType[];
}

export interface OfferCardProps {
  uid: number;
  storeName: string;
  offerTitle: string;
  productImgUrl: string;
  storeImgUrl: string;
  duration: string;
  isOfferUpto: boolean;
  children: React.ReactNode;
  showNewBadge: boolean;
  rootClass?: string;
  isFromSavedScreen?: boolean;
  saved?: boolean;
  hideCbTag?: boolean;
  isAutoGenerated?: boolean;
}

export type SubCategoriesList = {
  [key: string]: SubCategoriesByCategoryResponse;
};

export interface CustomSearchParamsTypes
  extends OfferControllerGetAllCategoriesParams {
  page: string;
  pageSize: string;
  minPercent?: number;
  maxPercent?: number;
  startDate?: string;
  endDate?: string;
  stores?: string;
  status?: string;
  date?: string;
  reviewSortType?: string;
}

export interface GiftCardAmountsList {
  amount: number;
  quantity: number;
  isSelected: boolean;
}

export interface GiftCardAmountsList {
  amount: number;
  quantity: number;
  isSelected: boolean;
}

export interface UserDetailsType extends GetUserProfileResponseItem {
  mobile?: string; // Override the type of 'mobile' property
}
