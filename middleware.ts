import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(req: NextRequest) {
  const { pathname, search } = req.nextUrl;

  // Handle API proxy requests
  if (pathname.startsWith('/api/proxy/')) {
    // Get the response from the next middleware
    const response = NextResponse.next();

    // Add CORS headers for local development
    if (process.env.NEXT_PUBLIC_ENVIRONMENT === 'local') {
      // Allow credentials
      response.headers.set('Access-Control-Allow-Credentials', 'true');
      // Allow the frontend origin
      response.headers.set('Access-Control-Allow-Origin', req.headers.get('origin') || 'http://localhost:3000');
      // Allow common headers
      response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
      // Allow common methods
      response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    }

    return response;
  }

  // Check if the pathname contains %20 (encoded space)
  if (pathname.includes('%20')) {
    // Remove %20 from the URL and convert to camelCase
    const formattedPathname = pathname
      .split('/')
      .map((segment) =>
        segment
          .split('%20')
          .map((word, index) =>
            index === 0
              ? word.toLowerCase()
              : word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
          )
          .join('')
      )
      .join('/');

    // Construct the new URL
    const newUrl = req.nextUrl.origin + formattedPathname + search;

    // Redirect to the new URL (301: Permanent Redirect)
    return NextResponse.redirect(newUrl, 301);
  }

  // Continue to the next handler if no redirect is needed
  return NextResponse.next();
}

// Define the matcher to apply middleware to both store paths and API proxy paths
export const config = {
  matcher: ['/store/:path*', '/api/proxy/:path*'],
};
