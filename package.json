{"name": "icb-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --experimental-https", "build": "next build", "start": "next start", "postinstall": "bun run swagger", "t": "tsc --noemit -watch", "fix": "eslint --fix --ext .js,.tsx .", "lint": "eslint --ext .js,.tsx,.ts . --quiet", "l": "next lint", "format": "prettier --write .", "swagger": "bun scripts/fetchAndWrite.cjs && bunx swagger-typescript-api generate -p ./swagger.json -o ./services/api --responses --union-enums --route-types  --extract-request-params --extract-request-body --extract-response-body   --unwrap-response-data --sort-types --modular  --extract-enums"}, "dependencies": {"@ant-design/nextjs-registry": "^1.0.2", "@ducanh2912/next-pwa": "^10.2.9", "@emailjs/browser": "^4.4.1", "@next/third-parties": "^15.3.0", "@reduxjs/toolkit": "^1.9.7", "@types/node": "20.5.1", "@types/react": "18.2.20", "@types/react-dom": "18.2.7", "antd": "^5.24.6", "antd-style": "^3.7.1", "autoprefixer": "10.4.15", "caniuse-lite": "^1.0.30001713", "clsx": "^2.1.1", "cookies-next": "^4.3.0", "dayjs": "^1.11.13", "destr": "^2.0.5", "eslint-config-next": "14.1.0", "framer-motion": "^11.18.2", "highcharts": "^11.4.8", "highcharts-react-official": "^3.2.1", "js.foresight": "^2.0.0", "lucide-react": "^0.447.0", "next": "14.1.0", "next-themes": "^0.2.1", "postcss": "8.4.28", "react": "18.2.0", "react-dom": "18.2.0", "react-hook-form": "^7.55.0", "react-insta-stories": "^2.8.0", "react-otp-input": "^3.1.1", "react-redux": "^8.1.3", "react-toastify": "^10.0.6", "react-web-share": "^2.0.2", "sass": "^1.86.3", "sharp": "^0.32.6", "swiper": "^10.3.1", "tailwindcss": "3.3.3", "usehooks-ts": "^2.16.0"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-validate-jsx-nesting": "^0.1.1", "prettier": "3.0.2", "typescript": "5.1.6", "webpack": "^5.99.5"}}