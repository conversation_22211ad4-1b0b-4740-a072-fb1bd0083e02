#!/usr/bin/env node

/**
 * ISR Testing Script
 * 
 * This script helps verify that ISR is working correctly by:
 * 1. Building the application
 * 2. Starting the production server
 * 3. Testing the ISR routes
 * 4. Checking response headers for cache information
 */

const http = require('http'); // eslint-disable-line @typescript-eslint/no-var-requires

const ISR_ROUTES = [
  // Previously implemented routes
  '/',
  '/online-free-shopping-stores',
  '/deals-and-coupons',
  '/on-going-sale-offers',
  '/store/amazon', // Example store - replace with actual store name

  // Newly implemented static content routes
  '/about-us',
  '/all-links',
  '/best-practices',
  '/contact',
  '/faqs',
  '/link-generator',
  '/notifications',
  '/redirecting',

  // API-dependent routes
  '/categories',
  '/terms-and-conditions',
  '/privacy-policies',
  '/referral',

  // Dynamic routes with frequent changes
  '/giftcards',
  // '/giftcards/example-gift-card?uid=123', // Example gift card
  // '/offer/example-offer', // Example offer
];

const EXPECTED_REVALIDATE = {
  // Previously implemented routes
  '/': 60,
  '/online-free-shopping-stores': 300,
  '/deals-and-coupons': 180,
  '/on-going-sale-offers': 180,
  '/store/amazon': 240,

  // Static content routes (24 hours)
  '/about-us': 86400,
  '/all-links': 86400,
  '/best-practices': 86400,
  '/contact': 86400,
  '/link-generator': 86400,
  '/notifications': 86400,
  '/redirecting': 86400,

  // FAQ content (12 hours)
  '/faqs': 43200,

  // API-dependent routes
  '/categories': 21600, // 6 hours
  '/terms-and-conditions': 43200, // 12 hours
  '/privacy-policies': 43200, // 12 hours
  '/referral': 1800, // 30 minutes

  // Dynamic routes
  '/giftcards': 600, // 10 minutes
};

async function testRoute(route) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: route,
      method: 'GET',
      headers: {
        'User-Agent': 'ISR-Test-Script'
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          route,
          statusCode: res.statusCode,
          headers: res.headers,
          hasContent: data.length > 0,
          isStatic: res.headers['x-nextjs-cache'] === 'HIT' || res.headers['x-vercel-cache'] === 'HIT'
        });
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error(`Timeout testing route: ${route}`));
    });

    req.end();
  });
}

async function runTests() {
  console.log('🧪 Testing ISR Implementation...\n');
  
  const results = [];
  
  for (const route of ISR_ROUTES) {
    try {
      console.log(`Testing ${route}...`);
      const result = await testRoute(route);
      results.push(result);
      
      console.log(`  ✅ Status: ${result.statusCode}`);
      console.log(`  📄 Has Content: ${result.hasContent}`);
      console.log(`  ⚡ Cache Status: ${result.headers['x-nextjs-cache'] || 'UNKNOWN'}`);
      console.log(`  🕒 Expected Revalidate: ${EXPECTED_REVALIDATE[route]}s`);
      console.log('');
      
    } catch (error) {
      console.log(`  ❌ Error: ${error.message}`);
      console.log('');
    }
  }
  
  // Summary
  console.log('📊 Test Summary:');
  console.log(`Total routes tested: ${results.length}`);
  console.log(`Successful responses: ${results.filter(r => r.statusCode === 200).length}`);
  console.log(`Static responses: ${results.filter(r => r.isStatic).length}`);
  
  return results;
}

async function main() {
  console.log('🚀 ISR Testing Script');
  console.log('Make sure your Next.js app is running on localhost:3000\n');
  
  try {
    await runTests();
    console.log('\n✅ ISR testing completed!');
  } catch (error) {
    console.error('\n❌ Testing failed:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { testRoute, runTests };
