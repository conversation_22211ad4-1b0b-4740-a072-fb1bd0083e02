'use client';

import { useRouter } from 'next/navigation';
import { determineLinkType, isExternalUrl, LinkType } from './link-utils';

/**
 * Hook for handling navigation with proper SEO attributes
 * 
 * This hook provides a function to navigate to a URL with proper handling
 * of external links and SEO attributes.
 * 
 * @example
 * const { navigateTo } = useSmartNavigation();
 * 
 * // Usage in a click handler
 * const handleClick = () => {
 *   navigateTo('/internal-page');
 * };
 * 
 * // Usage with an external link
 * const handleExternalClick = () => {
 *   navigateTo('https://example.com', LinkType.EXTERNAL_SPONSORED);
 * };
 */
export const useSmartNavigation = () => {
  const router = useRouter();

  /**
   * Navigate to a URL with proper handling of external links and SEO attributes
   * @param url The URL to navigate to
   * @param linkType Optional override for the link type
   */
  const navigateTo = (url: string, linkType?: LinkType) => {
    const type = determineLinkType(url, linkType);
    
    if (isExternalUrl(url)) {
      // For external links, open in new tab with proper rel attributes
      const relValue = type === LinkType.EXTERNAL ? 'noopener' :
                      type === LinkType.EXTERNAL_NOFOLLOW ? 'nofollow noopener' :
                      type === LinkType.EXTERNAL_SPONSORED ? 'sponsored noopener' :
                      'nofollow sponsored noopener';
      
      // Create a temporary anchor element to set the rel attribute
      const a = document.createElement('a');
      a.href = url;
      a.rel = relValue;
      a.target = '_blank';
      a.click();
    } else {
      // For internal links, use the Next.js router
      router.push(url);
    }
  };

  return { navigateTo };
};

/**
 * Hook for handling window.open with proper SEO attributes
 * 
 * This hook provides a function to open a URL in a new window/tab with proper
 * handling of SEO attributes.
 * 
 * @example
 * const { openWindow } = useSmartWindowOpen();
 * 
 * // Usage in a click handler
 * const handleClick = () => {
 *   openWindow('https://example.com');
 * };
 * 
 * // Usage with a sponsored link
 * const handleSponsoredClick = () => {
 *   openWindow('https://partner.com', LinkType.EXTERNAL_SPONSORED);
 * };
 */
export const useSmartWindowOpen = () => {
  /**
   * Open a URL in a new window/tab with proper SEO attributes
   * @param url The URL to open
   * @param linkType Optional override for the link type
   * @param windowFeatures Optional window features
   */
  const openWindow = (
    url: string, 
    linkType?: LinkType,
    windowFeatures?: string
  ) => {
    const type = determineLinkType(url, linkType);
    
    // Set the appropriate noopener/noreferrer features
    let features = windowFeatures || '';
    if (!features.includes('noopener')) {
      features += (features ? ',' : '') + 'noopener';
    }
    
    // For external links, we need to handle the rel attribute differently
    if (isExternalUrl(url)) {
      // Create a temporary anchor element to set the rel attribute
      const a = document.createElement('a');
      a.href = url;
      a.rel = type === LinkType.EXTERNAL ? 'noopener' :
              type === LinkType.EXTERNAL_NOFOLLOW ? 'nofollow noopener' :
              type === LinkType.EXTERNAL_SPONSORED ? 'sponsored noopener' :
              'nofollow sponsored noopener';
      a.target = '_blank';
      a.click();
    } else {
      // For internal links, just use window.open
      window.open(url, '_blank', features);
    }
  };

  return { openWindow };
};
