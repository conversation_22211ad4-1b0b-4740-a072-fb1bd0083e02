/**
 * Utility functions for handling link rel attributes
 */

/**
 * Link types for determining appropriate rel attributes
 */
export enum LinkType {
  INTERNAL = 'internal',
  EXTERNAL = 'external',
  EXTERNAL_NOFOLLOW = 'external-nofollow',
  EXTERNAL_SPONSORED = 'external-sponsored',
  EXTERNAL_NOFOLLOW_SPONSORED = 'external-nofollow-sponsored',
}

/**
 * Determines if a URL is external
 * @param url The URL to check
 * @returns boolean indicating if the URL is external
 */
export const isExternalUrl = (url: string): boolean => {
  // Check if the URL is absolute (starts with http:// or https://)
  if (/^https?:\/\//.test(url)) {
    // Check if the URL is not from our domain
    return !url.includes('indiancashback.com');
  }
  return false;
};

/**
 * Gets the appropriate rel attribute value based on link type
 * @param type The type of link
 * @returns The rel attribute value
 */
export const getRelAttribute = (type: LinkType): string | undefined => {
  switch (type) {
    case LinkType.INTERNAL:
      return undefined;
    case LinkType.EXTERNAL:
      return 'noopener';
    case LinkType.EXTERNAL_NOFOLLOW:
      return 'nofollow noopener';
    case LinkType.EXTERNAL_SPONSORED:
      return 'sponsored noopener';
    case LinkType.EXTERNAL_NOFOLLOW_SPONSORED:
      return 'nofollow sponsored noopener';
    default:
      return undefined;
  }
};

/**
 * Determines the link type based on the URL and optional override
 * @param url The URL to analyze
 * @param overrideType Optional override for the link type
 * @returns The determined LinkType
 */
export const determineLinkType = (
  url: string,
  overrideType?: LinkType
): LinkType => {
  if (overrideType) {
    return overrideType;
  }

  if (!isExternalUrl(url)) {
    return LinkType.INTERNAL;
  }

  // Default external links to nofollow for SEO best practices
  return LinkType.EXTERNAL_NOFOLLOW;
};
