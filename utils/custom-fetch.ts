// import { setCookie } from 'cookies-next';
import { BASE_URL } from '@/config';

const customFetch = async (
  url: string,
  accessToken?: string,
  refreshToken?: string,
  method: string = 'GET',
  body?: any
) => {
  const headers = {
    'Content-Type': 'application/json',
    Authorization: `Bearer ${accessToken}`,
  };

  const options: RequestInit = {
    method,
    headers,
    body: body ? JSON.stringify(body) : undefined,
  };

  try {
    const response = await fetch(`${BASE_URL}${url}`, options);
    console.log(response.status, 'status');
    if (response.status === 401) {
      console.log('Unauthtorized, jj');
    }
    return response.json();
  } catch (error) {
    if (error instanceof TypeError) {
      // Handle network errors
      console.error('Network error:', error.message);
    } else {
      console.error('Other error:', error);
    }
    throw error;
  }
};

// const setTokens = (accessToken: string, refreshToken: string) => {
//     setCookie('accessToken', accessToken);
//     localStorage.setItem('refreshToken', refreshToken);
// };

export default customFetch;
