//takes timestamp in milisecond and return the time past away
export function timeAgo(timestamp: number) {
  const currentTime = Date.now();
  const timeDifference = currentTime - timestamp;

  if (timeDifference < 1000) {
    return 'Just now';
  } else if (timeDifference < 60000) {
    const seconds = Math.floor(timeDifference / 1000);
    return `${seconds}s ago`;
  } else if (timeDifference < 3600000) {
    const minutes = Math.floor(timeDifference / 60000);
    return `${minutes}min ago`;
  } else if (timeDifference < 86400000) {
    const hours = Math.floor(timeDifference / 3600000);
    return `${hours}h ago`;
  } else {
    const days = Math.floor(timeDifference / 86400000);
    return `${days}d ago`;
  }
}

export const sideScroll = (
  element: HTMLDivElement | null,
  speed: number,
  distance: number,
  step: number
): { hideLeftButton: boolean; hideRightButton: boolean } => {
  let hideLeftButton = false,
    hideRightButton = false;

  if (element) {
    let scrollAmount = 0;
    const slideTimer = setInterval(() => {
      element.scrollLeft += step;
      scrollAmount += Math.abs(step);
      if (scrollAmount >= distance) {
        clearInterval(slideTimer);
      }
    }, speed);

    if (element.scrollLeft <= 0) {
      hideLeftButton = true;
    } else {
      hideLeftButton = false;
    }

    if (element.scrollLeft >= element.offsetWidth) {
      hideRightButton = true;
    } else {
      hideRightButton = false;
    }
  }

  return { hideLeftButton, hideRightButton };
};

export function formatIndRs(amount: number) {
  // Convert the to indian currency format with max 2 fractional digit
  const formattedAmount = amount?.toLocaleString('en-IN', {
    maximumFractionDigits: 2,
    minimumFractionDigits: 0,
    style: 'currency',
    currency: 'INR',
  });
  return `${formattedAmount}`;
}

export const copyToClipboard = (text: number | string) => {
  // Create a temporary input element to hold the value
  const tempInput = document.createElement('input');
  tempInput.value = text.toString();
  document.body.appendChild(tempInput);

  // Select the text in the input element
  tempInput.select();
  tempInput.setSelectionRange(0, 99999); // For mobile devices

  // Copy the selected text to the clipboard
  document.execCommand('copy');

  // Remove the temporary input element
  document.body.removeChild(tempInput);
};

import type {
  AllCategoriesResponse,
  CashbackRateType,
  GetAllOnGoingOffersResponse,
} from '@/services/api/data-contracts';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import duration from 'dayjs/plugin/duration';

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(duration);

dayjs.extend(duration);

export function formatDuration(endDate: string) {
  // Convert end date to Indian time (IST)
  const indianDate = dayjs(endDate).tz('Asia/Kolkata', true);

  // Get the current time in Indian time (IST)
  const now = dayjs().tz('Asia/Kolkata');

  // Calculate the total duration between now and the end date in milliseconds
  const durationInMillis = indianDate.diff(now);

  // Check if the end date has passed
  if (durationInMillis <= 0) {
    return '0 Days 0 Hours'; // Return 0 days and hours if the end date has passed
  }

  // Calculate total hours and days from the duration in milliseconds
  const totalHours = Math.floor(durationInMillis / (1000 * 60 * 60)); // Convert milliseconds to hours
  const totalDays = Math.floor(totalHours / 24); // Calculate total days
  const remainingHours = totalHours % 24; // Calculate remaining hours

  // Check if duration is less than an hour and return only minutes
  if (totalHours === 0) {
    const remainingMinutes = Math.floor(durationInMillis / (1000 * 60)); // Convert milliseconds to minutes
    return `${remainingMinutes} Minute${remainingMinutes !== 1 ? 's' : ''}`; // Return minutes if less than an hour
  }

  // Format the result, excluding minutes if the duration is more than an hour
  const formattedResult = `${totalDays} ${
    totalDays > 1 ? 'Days' : 'Day'
  } ${remainingHours} Hour${remainingHours !== 1 ? 's' : ''}`;

  return formattedResult;
}

function sanitizeString(input: string): string {
  return input
    ?.replace(/<strong>|<\/strong>/gi, '') // Remove <strong> tags
    .replace(/%/g, 'pc') // Replace % with pc
    .replace(/[^\w\s]/gi, ''); // Remove all special characters
}

export function generateProductUrl(
  storeName: string,
  offerTitle: string
): string {
  // Sanitize inputsw
  const sanitizedStoreName = sanitizeString(storeName);
  const sanitizedTitle = sanitizeString(offerTitle);

  // Concatenate and sanitize the URL
  let url = `${sanitizedStoreName}_${sanitizedTitle}`;
  url = url.substring(0, 60); // Limit to 60 characters

  // Replace spaces with dashes and convert to lowercase
  url = url.toLowerCase().replace(/\s+/g, '-');

  return url;
}

export function generateGiftCardUrl(name: string): string {
  // Sanitize input
  const url = sanitizeString(name)
    .substring(0, 60)
    .toLowerCase()
    .replace(/\s+/g, '-');

  return url;
}

export function toggleCategory({
  arr,
  uid,
  name,
}: {
  arr: Array<{ uid: number; name: string }>;
  uid: number;
  name: string;
}) {
  const index = arr.findIndex((item) => item.uid === uid);
  const newArr = Array.from(arr);
  if (index !== -1) {
    // Item found, toggle or remove it
    newArr.splice(index, 1); // Remove the item
  } else {
    // Item not found, add it
    newArr.push({ uid, name });
  }
  return newArr;
}

export function filterSubcategoriesByUid(
  categories: Array<AllCategoriesResponse>,
  subcategoryUids: number[]
) {
  const filteredSubcategories: Array<{ uid: number; name: string }> = [];

  categories.forEach((category) => {
    category.subCategories.forEach((subCategory) => {
      if (subcategoryUids.includes(subCategory.uid)) {
        filteredSubcategories.push({
          uid: subCategory.uid,
          name: subCategory.name,
        });
      }
    });
  });

  return filteredSubcategories;
}

export function filterOnGoingSalesByUid(
  onGoingOffers: Array<GetAllOnGoingOffersResponse>,
  onGoingOffersUids: number[]
) {
  const filteredOnGoingOffers: Array<{ uid: number; name: string }> = [];

  onGoingOffers.forEach((offer) => {
    if (onGoingOffersUids.includes(offer.uid)) {
      filteredOnGoingOffers.push({
        uid: offer.uid,
        name: offer.name,
      });
    }
  });
  return filteredOnGoingOffers;
}

export function searchSubCategoriesByName({
  categories,
  query,
}: {
  categories: Array<AllCategoriesResponse>;
  query: string;
}) {
  return categories.reduce<Array<AllCategoriesResponse>>((acc, category) => {
    const matchingSubcategories = category.subCategories.filter((subCategory) =>
      subCategory.name.toLowerCase().includes(query.toLowerCase())
    );
    if (matchingSubcategories.length > 0) {
      acc.push({ ...category, subCategories: matchingSubcategories });
    }
    return acc;
  }, []);
}

export function filterCashbackRates(
  array: CashbackRateType[],
  filterString: string
): CashbackRateType[] {
  return array.filter((rate) => {
    const { description, name, newUserRate, oldUserRate } = rate;
    // Check if any of the first four properties match the filterString
    if (
      description
        .toLocaleLowerCase()
        .includes(filterString.toLocaleLowerCase()) ||
      name.toLocaleLowerCase().includes(filterString.toLocaleLowerCase()) ||
      newUserRate.toString().includes(filterString.replace(/[₹%]/g, '')) ||
      oldUserRate.toString().includes(filterString.replace(/[₹%]/g, ''))
    ) {
      return true;
    }

    return false;
  });
}

export function ensureFourSlides(
  banners: Array<{ redirectUrl: string; imageUrl: string }>
) {
  const minSlides = 4;
  const length = banners.length;

  if (length >= minSlides) {
    return banners;
  }

  const duplicatedBanners = [...banners];
  let i = 0;

  while (duplicatedBanners.length < minSlides) {
    duplicatedBanners.push({ ...banners[i % length] });
    i++;
  }

  return duplicatedBanners;
}

export function ensureFourSlidesDesktop(
  banners: Array<{ redirectUrl: string; imageUrl: string }>
) {
  const minSlides = 4;
  const length = banners.length;

  // If there are 4 or more banners, return as is
  if (length >= minSlides) {
    return banners;
  }

  // Duplicate the banners array
  const duplicatedBanners = [...banners];

  // If there are exactly 3 banners, use the second banner as the 4th item
  if (length === 3) {
    duplicatedBanners.push({ ...banners[1] });
  } else {
    let i = 0;
    // Add more banners until there are 4
    while (duplicatedBanners.length < minSlides) {
      duplicatedBanners.push({ ...banners[i % length] });
      i++;
    }
  }

  return duplicatedBanners;
}

export const chunkArray = <T>(array: T[], chunkSize: number): T[][] => {
  const chunks: T[][] = [];
  for (let i = 0; i < array.length; i += chunkSize) {
    chunks.push(array.slice(i, i + chunkSize));
  }
  return chunks;
};

export const formatStoreName = (storeName: string): string =>
  storeName
    ?.split(' ')
    ?.map((word, index) =>
      index === 0
        ? word.toLowerCase()
        : word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
    )
    ?.join('');

export function getLastWord(text: string): string {
  // Handle null, undefined, or empty string cases
  if (!text || text.trim() === '') {
    return '';
  }

  // Split the string into words and get the last word
  const words = text.trim().split(/\s+/); // Split by whitespace
  return words[words.length - 1];
}
