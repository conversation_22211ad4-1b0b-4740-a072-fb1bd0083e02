/**
 * Schema.org structured data utilities for Next.js 14 App Router
 */

export interface WebsiteSchema {
  name: string;
  url: string;
  potentialAction?: {
    '@type': string;
    target: string;
    'query-input'?: string;
  }[];
  sameAs?: string[];
}

export interface OrganizationSchema {
  name: string;
  url: string;
  logo: string;
  description?: string;
  sameAs?: string[];
  address?: {
    '@type': string;
    companyName: string;
    streetAddress: string;
    addressLocality: string;
    addressRegion: string;
    postalCode: string;
    addressCountry: string;
  };
  telephone?: string;
  founders?: {
    '@type': string;
    name: string;
  }[];
}

export interface ProductSchema {
  name: string;
  description: string;
  image: string;
  offers?: {
    price: string;
    priceCurrency: string;
    url: string;
    availability?: string;
    validFrom?: string;
    priceValidUntil?: string;
  };
  brand?: {
    name: string;
  };
}

export interface BreadcrumbSchema {
  itemListElement: {
    '@type': 'ListItem';
    position: number;
    name: string;
    item?: string;
  }[];
}

export interface StoreSchema {
  name: string;
  description: string;
  image: string;
  url: string;
  offers?: {
    '@type': 'Offer';
    name: string;
    description?: string;
    url: string;
    validFrom?: string;
    validThrough?: string;
    price?: string;
    priceCurrency?: string;
  }[];
}

export interface FAQSchema {
  mainEntity: {
    '@type': 'Question';
    name: string;
    acceptedAnswer: {
      '@type': 'Answer';
      text: string;
    };
  }[];
}

/**
 * Generate JSON-LD structured data for a website
 */
export function generateWebsiteSchema(website: WebsiteSchema): string {
  return JSON.stringify({
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    ...website,
  });
}

/**
 * Generate JSON-LD structured data for an organization
 */
export function generateOrganizationSchema(org: OrganizationSchema): string {
  return JSON.stringify({
    '@context': 'https://schema.org',
    '@type': 'Organization',
    ...org,
  });
}

/**
 * Generate JSON-LD structured data for a product
 */
export function generateProductSchema(product: ProductSchema): string {
  return JSON.stringify({
    '@context': 'https://schema.org',
    '@type': 'Product',
    ...product,
  });
}

/**
 * Generate JSON-LD structured data for breadcrumbs
 */
export function generateBreadcrumbSchema(breadcrumb: BreadcrumbSchema): string {
  return JSON.stringify({
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    ...breadcrumb,
  });
}

/**
 * Generate JSON-LD structured data for a store
 */
export function generateStoreSchema(store: StoreSchema): string {
  return JSON.stringify({
    '@context': 'https://schema.org',
    '@type': 'Store',
    ...store,
  });
}

/**
 * Generate JSON-LD structured data for a FAQ page
 */
export function generateFAQSchema(faq: FAQSchema): string {
  return JSON.stringify({
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    ...faq,
  });
}

/**
 * Generate JSON-LD structured data for a generic article
 */
export function generateArticleSchema({
  title,
  description,
  url,
  authorName,
  authorUrl,
  publisherName,
  publisherLogo,
  datePublished,
  dateModified,
  image,
}: {
  title: string;
  description: string;
  url: string;
  authorName: string;
  authorUrl?: string;
  publisherName: string;
  publisherLogo: string;
  datePublished: string;
  dateModified?: string;
  image: string;
}): string {
  return JSON.stringify({
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline: title,
    description,
    image,
    url,
    author: {
      '@type': 'Person',
      name: authorName,
      ...(authorUrl && { url: authorUrl }),
    },
    publisher: {
      '@type': 'Organization',
      name: publisherName,
      logo: {
        '@type': 'ImageObject',
        url: publisherLogo,
      },
    },
    datePublished,
    ...(dateModified && { dateModified }),
  });
}

/**
 * Generate all necessary base schemas for the website
 */
export function generateBaseSchema(): string[] {
  // Base schema for the website
  const websiteSchema = generateWebsiteSchema({
    name: 'IndianCashback.com',
    url: 'https://www.indiancashback.com',
    potentialAction: [
      {
        '@type': 'SearchAction',
        target: 'https://www.indiancashback.com/search?q={search_term_string}',
        'query-input': 'required name=search_term_string',
      },
    ],
    sameAs: [
      'https://www.facebook.com/indiancashback',
      'https://twitter.com/indiancashback',
      'https://www.instagram.com/indiancashback',
      'https://www.youtube.com/@indiancashback',
      'https://www.linkedin.com/company/indiancashback-com',
    ],
  });

  // Organization schema
  const organizationSchema = generateOrganizationSchema({
    name: 'IndianCashback',
    url: 'https://www.indiancashback.com',
    logo: 'https://www.indiancashback.com/img/logo.png',
    description: 'One of the best cashback websites in India offering deals, coupons, and cashback on online shopping.',
    sameAs: [
      'https://www.facebook.com/indiancashback',
      'https://twitter.com/indiancashback',
      'https://www.instagram.com/indiancashback',
      'https://www.youtube.com/@indiancashback',
      'https://www.linkedin.com/company/indiancashback-com',
    ],
    address: {
      '@type': 'PostalAddress',
      companyName: 'Revontulet solutions private limite',
      streetAddress: 'No.112, AKR Tech Park, "A" Block, 7th Mile Hosur Rd, Krishna Reddy Industrial Area',
      addressLocality: 'Bengaluru',
      addressRegion: 'Karnataka',
      postalCode: '560068',
      addressCountry: 'IN'
    },
    founders: [
      {
        '@type': 'Founder',
        name: 'Delbin Thomas'
      },
      {
        '@type': 'Founder',
        name: 'Jesvin Jacob'
      }
    ]
  });

  return [websiteSchema, organizationSchema];
}
