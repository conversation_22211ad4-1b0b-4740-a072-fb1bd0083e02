import fetchWrapper from './fetch-wrapper';
import { setIsUserLogin, clearAuthState, setLoginModalOpen } from '@/redux/slices/auth-slice';
import { AppDispatch } from '@/redux/store';

/**
 * Checks if the user is authenticated by calling the auth/check endpoint
 * Updates the Redux store with user details if authenticated
 */
export const checkAuthStatus = async (dispatch: AppDispatch): Promise<boolean> => {
  try {
    const response = await fetchWrapper('/api/proxy/auth/check', {
      method: 'GET',
      suppressToast: true, // Don't show error toasts for auth checks
    });

    if (response) {
      // User is authenticated, update Redux store
      dispatch(setIsUserLogin(true));
      return true;
    }

    // User is not authenticated
    dispatch(setIsUserLogin(false));
    return false;
  } catch (error) {
    console.error('Auth check failed:', error);
    dispatch(setIsUserLogin(false));
    return false;
  }
};

/**
 * Handles authentication errors (401/403) by logging out the user
 * and updating the UI state consistently across the application
 */
export const handleAuthError = (dispatch: AppDispatch, showLoginModal: boolean = false): void => {
  console.log('Session expired - logging out user');

  // Clear all authentication state
  dispatch(clearAuthState());

  // Don't show error toaster for session expiration
  // Users will be prompted to login when they try to access protected features

  // Only show login modal if explicitly requested (e.g., on protected pages)
  if (showLoginModal) {
    dispatch(setLoginModalOpen(true));
  }
};

/**
 * Logs the user out by calling the auth/logout endpoint
 * Updates the Redux store to reflect logged out state
 */
export const logoutUser = async (dispatch: AppDispatch): Promise<boolean> => {
  try {
    await fetchWrapper('/api/proxy/auth/logout', {
      method: 'DELETE',
      suppressToast: true, // Don't show error toast for intentional logout
    });

    // Clear auth state using the new action
    dispatch(clearAuthState());

    return true;
  } catch (error) {
    console.error('Logout failed:', error);
    // Even if logout API fails, clear local state
    dispatch(clearAuthState());
    return false;
  }
};

/**
 * Checks if an error is an authentication error (401 or 403)
 */
export const isAuthError = (error: any): boolean => {
  return error?.status === 401 || error?.status === 403 ||
    error?.message?.includes('401') || error?.message?.includes('403') ||
    error?.message?.includes('Unauthorized') || error?.message?.includes('Forbidden');
};
